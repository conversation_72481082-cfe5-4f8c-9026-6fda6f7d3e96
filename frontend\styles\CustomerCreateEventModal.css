.CreateEventModal-overlay {
  position: fixed;
  inset: 0;
  padding: var(--pp-sp-24);
  z-index: var(--pp-z-index-modal);
  background: rgba(0, 0, 0, 0.32);
  display: flex;
  align-items: center;
  justify-content: center;
}
  /* Radio-like two-button group (yes/no) */
  .pp-event-checkout-form__radio-wrap {
    display: flex;
   padding: 5px;
    margin-top: var(--pp-sp-4);
    border: 1px solid var(--pp-clr-border-light);
    border-radius: var(--pp-bor-rad-8);
    width: fit-content;
    background-color: var(--pp-clr-secondary);
  }
  .pp-event-checkout-form__radio-btn {
    width: 36px;
    height: 28px;
    background: var(--pp-clr-primary);
    border: 1px solid var(--pp-clr-border);
    color: var(--pp-clr-text-main);
    font-family: var(--pp-font-Metro-Sans);
    border-radius: var(--pp-bor-rad-8);
    padding: 4px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.14s;
    box-shadow: none;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .pp-event-checkout-form__radio-btn.selected,
  .pp-event-checkout-form__radio-btn:active {
    background: var(--pp-clr-secondary);
    color: transparent;
    border-color: var(--pp-clr-secondary);
  }
  .pp-event-checkout-form__radio-btn:not(.selected):hover {
    border-color: var(--pp-clr-text-gray);
  }
.CreateEventModal-container {
  background: var(--pp-clr-primary);
  border-radius: var(--pp-bor-rad-16);
  box-shadow: var(--pp-shdw-lg);
  width: 100%;
  max-width: 700px;
  display: flex;
  flex-direction: column;
  font-family: var(--pp-font-Metro-Sans);
  color: var(--pp-clr-text-main);
  max-height: 95vh;
  overflow: hidden;
}

.CreateEventModal-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: var(--pp-sp-24) var(--pp-sp-24) var(--pp-sp-12) var(--pp-sp-24);
}

.CreateEventModal-title {
  font-size: var(--pp-font-heading4);
  font-weight: 400;
  margin-bottom: var(--pp-sp-8);
}

.CreateEventModal-status {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
}

.CreateEventModal-close {
  background: transparent;
  border: none;
  font-size: var(--pp-font-heading4);
  color: var(--pp-clr-text-main);
  cursor: pointer;
  align-self: flex-start;
  padding: var(--pp-sp-4);
  border-radius: var(--pp-bor-rad-round);
  transition: background 0.2s;
}

.CreateEventModal-close:hover {
  background: var(--pp-clr-border-light);
}

.CreateEventModal-form {
  
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-24);
}

.CreateEventModal-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--pp-sp-16) var(--pp-sp-16);
}

.CreateEventModal-label-static {
  padding: var(--pp-sp-12) 0;
  color: var(--pp-clr-text-gray);
  font-size: var(--pp-font-extra-small);
  font-weight: 600;
}

.CreateEventModal-colspan2 {
  grid-column: span 2;
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-16);
}

.CreateEventModal-radio-group {
  display: flex;
  gap: var(--pp-sp-16);
  margin-top: var(--pp-sp-8);
}

.CreateEventModal-radio-option {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8);
  cursor: pointer;
  font-size: var(--pp-font-small);
}

.CreateEventModal-radio-option input[type="radio"] {
  margin: 0;
  cursor: pointer;
}

.CreateEventModal-radio-option span {
  color: var(--pp-clr-text-main);
}

.CreateEventModal-textarea-subtitle {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  margin-top: var(--pp-sp-4);
  margin-bottom: var(--pp-sp-8);
}

.CreateEventModal-buttons {
  display: flex;
  justify-content: space-between;
  gap: var(--pp-sp-12);
  padding-top: var(--pp-sp-16);
  border-top: 1px solid var(--pp-clr-border-light);
}

.pp-form-input--textarea {
  min-height: 120px;
  resize: vertical;
  font-family: inherit;
}

.CreateEventModal-colspan2 .pp-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--pp-sp-24) !important;
}

.pp-form-row:has(.pp-form-group:nth-child(3)) {
  
  grid-template-columns: 300px fit-content(200px) 150px;

}

@media (max-width: 768px) {
  .CreateEventModal-grid {
    grid-template-columns: 1fr;
    gap: var(--pp-sp-16);
  }
  
  .CreateEventModal-colspan2 {
    grid-column: span 1;
  }
  
  .pp-form-row {
    grid-template-columns: 1fr;
    gap: var(--pp-sp-16);
  }
  .pp-form-row:has(.pp-form-group:nth-child(3)) {
  
    grid-template-columns: 1fr fit-content(200px) fit-content(150px) ;
  
  }

}
@media (max-width: 600px) {
  .pp-form-row:has(.pp-form-group:nth-child(3)) {
  
    grid-template-columns: 1fr fit-content(200px) ;
  
  }
  .CreateEventModal-colspan2 .pp-form-row{
    grid-template-columns: 1fr;
    display: grid!important;
    gap:var(--pp-sp-16)!important;
    margin-bottom: 0;
  
  }
  .CreateEventModal-colspan2 .pp-form-group{
    margin-bottom: 0;
  }

}
@media (max-width: 480px) {
  .CreateEventModal-radio-group {
    flex-direction: column;
    gap: var(--pp-sp-12);
    margin-top: 0;
  }
  
  .CreateEventModal-buttons {
    flex-direction: row;
    gap: var(--pp-sp-12);
    justify-content: space-between;
  }
}
