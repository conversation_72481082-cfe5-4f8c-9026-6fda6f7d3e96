

   .csm-modal-overlay {
    position: fixed;
    inset: 0;
    background-color: var(--pp-overlay-bg);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: var(--pp-z-index-modal);
    padding: var(--pp-sp-16);
  }
  
  .csm-modal-container {
    background-color: var(--pp-clr-primary);
    font-family: var(--pp-font-Metro-Sans);
    border-radius: var(--pp-bor-rad-12);
    color: var(--pp-clr-text-main);
    box-shadow: var(--pp-shdw-lg);
    max-width: var(--pp-modal-max-width);
    width: 100%;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: var(--pp-sp-40) var(--pp-sp-20) var(--pp-sp-32) var(--pp-sp-20);
  }
  
  /* Header */
  .csm-modal-header {
    padding: 0 12px var(--pp-sp-24) 12px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    background-color: var(--pp-clr-primary);
    flex-direction: column-reverse;
  }
  
  .csm-modal-title {
    font-size: var(--pp-font-heading4);
    font-weight: 400;
    margin: 0;
    line-height: 1.2;
  }
  
  .csm-btn-close {
    font-size: 1.75rem;
    background: transparent;
    border: none;
    cursor: pointer;
    transition: color 0.3s ease;
    width: var(--pp-sp-32);
    height: var(--pp-sp-32);
    display: flex;
    align-items: center;
    justify-content: center;
    align-self: end;
  }
  
  .csm-btn-close:hover,
  .csm-btn-close:focus {
    color: var(--pp-clr-secondary);
    outline: none;
    background-color: var(--pp-clr-border-light);
  }
  
  /* Tabs (desktop) */
  .csm-modal-tabs {
    display: flex;
    border-bottom: var(--pp-bor-w-1) solid var(--pp-clr-border-light);
    background-color: var(--pp-clr-primary);
    gap: var(--pp-font-heading5);
    padding: 0 12px;
  }
  
  .csm-tab {
    padding: var(--pp-sp-16) 0;
    font-weight: 500;
    font-size: var(--pp-font-extra-small);
    color: var(--pp-clr-text-gray);
    text-align: center;
    background: transparent;
    border: none;
    border-bottom: var(--pp-bor-w-1) solid transparent;
    cursor: pointer;
    transition: all 0.25s ease;
    position: relative;
  }
  
  .csm-tab:hover,
  .csm-tab:focus {
    color: var(--pp-clr-secondary);
    outline: none;
  }
  
  .csm-tab-active {
    color: var(--pp-clr-secondary);
    border-bottom-color: var(--pp-clr-secondary);
    font-weight: 600;
  }
  
  .csm-tab-active::after {
    content: "";
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--pp-clr-secondary);
  }
  
  .csm-modal-overflowconatiner {
    overflow-y: auto;

  }
  
  /* Mobile stepper tabs */
  .csm-mobile-tabs {
    display: none; /* hidden by default */
    border: 1px solid #D2D2D2;
    background-color: var(--pp-clr-primary);
    border-radius: 6px;
  }
  
  .csm-mobile-tab-title {
    flex: 1;
    text-align: center;
    font-weight: 600;
    font-size: var(--pp-font-small-font);
  }
  
  .csm-mobile-dropdown-container {
   width: 100%;
   display: flex;
   align-items: center;
   justify-content: center;
  }

  .csm-mobile-step-select {
    width: 100%;
  }

  .csm-mobile-step-select .custom-select-box {
   border: none;
   border-right: 1px solid #D2D2D2;
   border-left: 1px solid #D2D2D2;
   border-radius: 0;
   width: 100%;
  }

  .csm-mobile-step-select .custom-select-dropdown {
    width: 100%;
  }

  .csm-mobile-nav-btn {
    background: transparent;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: var(--pp-clr-text-gray);
    padding: var(--pp-sp-8);
    border-radius: var(--pp-bor-rad-4);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
  }

  .csm-mobile-nav-btn:hover:not(:disabled) {
    background-color: var(--pp-clr-bg-light);
    color: var(--pp-clr-secondary);
  }

  .csm-mobile-nav-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
  }
  
  /* Content */
  .csm-modal-content {
    flex-grow: 1;
    padding: var(--pp-sp-32) 12px;
    display: flex;
    flex-direction: column;
    gap: var(--pp-sp-32);
    min-height: 400px;
    background-color: var(--pp-clr-primary);
  }
  
  /* Footer */
  .csm-modal-footer {
    border-top: var(--pp-bor-w-1) solid var(--pp-clr-border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--pp-sp-16);
    padding: var(--pp-sp-24) var(--pp-sp-32);
    background-color: var(--pp-clr-primary);
    user-select: none;
  }
  
  /* Buttons */
  .csm-btn-nav {
    background-color: var(--pp-clr-primary);
    border: 1px solid var(--pp-clr-border);
    border-radius: var(--pp-bor-rad-24);
    padding: var(--pp-sp-12) var(--pp-sp-24);
    color: var(--pp-clr-secondary);
    font-weight: 600;
    font-size: var(--pp-font-extra-small);
    cursor: pointer;
    transition: all 0.25s ease;
    min-width: 100px;
  }
  
  .csm-btn-nav:hover:not(:disabled),
  .csm-btn-nav:focus:not(:disabled) {
    background-color: var(--pp-clr-secondary);
    color: var(--pp-clr-primary);
    outline: none;
    transform: translateY(-1px);
    box-shadow: var(--pp-shdw-sm);
  }
  
  .csm-btn-nav:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
  
  .csm-btn-primary {
    background-color: var(--pp-clr-secondary);
    color: var(--pp-clr-primary);
    border: none;
    padding: var(--pp-sp-12) var(--pp-sp-32);
    border-radius: var(--pp-bor-rad-24);
    font-weight: 600;
    font-size: var(--pp-font-extra-small);
    cursor: pointer;
    transition: all 0.25s ease;
    min-width: 120px;
  }
  
  .csm-btn-primary:hover,
  .csm-btn-primary:focus {
    background-color: var(--pp-clr-text-gray);
    outline: none;
    transform: translateY(-1px);
    box-shadow: var(--pp-shdw-sm);
  }
  
  .csm-btn-secondary {
    background-color: transparent;
    border: 1px solid var(--pp-clr-border);
    border-radius: var(--pp-bor-rad-24);
    color: var(--pp-clr-secondary);
    font-weight: 600;
    font-size: var(--pp-font-extra-small);
    padding: var(--pp-sp-12) var(--pp-sp-24);
    cursor: pointer;
    transition: all 0.25s ease;
    min-width: 120px;
  }
  
  .csm-btn-secondary:hover,
  .csm-btn-secondary:focus {
    background-color: var(--pp-clr-secondary);
    color: var(--pp-clr-primary);
    outline: none;
    transform: translateY(-1px);
    box-shadow: var(--pp-shdw-sm);
  }
  
  .csm-modal-container .pp-form-group {
    margin-bottom: 0;
    max-width: 535px;
    position: relative;
  }
  .csm-modal-container .pp-form-input {
    border-radius: 6px;
    border: 1px solid #d2d2d2;
  }
  .csm-modal-container textarea {
    height: 219px;
    border-radius: 6px;
  }
  .csm-modal-container .pp-form-label {
    margin-bottom: 0;
    font-weight: 600;
  }
  .pp-helper-text {
    color: #727272;
  }
  
  /* Responsive */
  @media (max-width: 768px) {
    .csm-modal-overlay {
      padding: var(--pp-sp-8);
    }
  
    .csm-modal-container {
      max-width: 100vw;
      height: 95vh;
      border-radius: 8px;
      max-height: 100vh;
      padding: 20px 10px;
    }
  
    .csm-modal-header {
      padding: var(--pp-sp-20) 12px;
    }
  
    .csm-modal-title {
      font-size: var(--pp-font-heading4);
    }
  
    /* Hide desktop tabs, show mobile stepper */
    .csm-modal-tabs {
      display: none;
    }
    .csm-mobile-tabs {
      display: grid;
              grid-template-columns: 40px 1fr 40px;
              justify-items: center;
              align-items: center;
              justify-content: space-between;
              margin-top: 10px;
    }
  
    .csm-modal-content {
      padding: var(--pp-sp-24) 12px;
      gap: var(--pp-sp-24);
    }
  
    .csm-modal-footer {
      padding: var(--pp-sp-20) var(--pp-sp-24);
      gap: var(--pp-sp-12);
    }
  
    .csm-btn-nav,
    .csm-btn-primary,
    .csm-btn-secondary {
      width: 100%;
      min-width: auto;
    }
  }
  
  @media (max-width: 480px) {
    .csm-modal-header {
      padding: 0 12px;
      display: flex;
      justify-content: space-between;
      flex-direction: row;
      padding-bottom: 10px;
      border-bottom: 1px solid #D2D2D2;
    }
  
    .csm-modal-content {
      padding: var(--pp-sp-20) 0px;
      gap: var(--pp-sp-20);
    }
  
    .csm-modal-footer {
      padding: var(--pp-sp-16) var(--pp-sp-20);
      display: grid;
      grid-template-columns: 1fr 1fr;
    }
  
    .csm-tab {
      padding: var(--pp-sp-10) var(--pp-sp-12);
      font-size: var(--pp-font-extra-small);
    }
  }
  