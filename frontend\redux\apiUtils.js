/**
 * Centralized API utility for making HTTP requests
 * Handles authentication tokens, error responses, and request/response formatting
 */
import axios from 'axios';
import { toast } from 'react-toastify';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
export const VITE_IMAGE_URL = import.meta.env.VITE_IMAGE_URL || 'http://localhost:5000';

/**
 * Custom error class for API errors
 */
export class ApiError extends Error {
  constructor(message, status, data = null) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
  }
}

/**
 * Get stored access token from localStorage
 */
const getAccessToken = () => {
  return localStorage.getItem('accessToken');
};

/**
 * Store access token in localStorage
 */
export const setAccessToken = (token) => {
  if (token) {
    localStorage.setItem('accessToken', token);
  } else {
    localStorage.removeItem('accessToken');
  }
};

/**
 * Clear all stored authentication data
 */
export const clearAuthData = () => {
  localStorage.removeItem('accessToken');
  localStorage.removeItem('user');
};

/**
 * Store user data in localStorage
 */
export const setUserData = (user) => {
  if (user) {
    localStorage.setItem('user', JSON.stringify(user));
  } else {
    localStorage.removeItem('user');
  }
};

/**
 * Get stored user data from localStorage
 */
export const getUserData = () => {
  const userData = localStorage.getItem('user');
  return userData ? JSON.parse(userData) : null;
};

// Create axios instance
const axiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  withCredentials: true, // Include cookies for refresh token
});

// Request interceptor to add auth token
axiosInstance.interceptors.request.use(
  (config) => {
    const token = getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
axiosInstance.interceptors.response.use(
  (response) => {
    return response.data; // Return only the data part
  },
  (error) => {
    let errorMessage = 'An error occurred';
    let statusCode = 0;
    let errorData = null;

    if (error.response) {
      // Server responded with error status
      statusCode = error.response.status;
      errorData = error.response.data;
      errorMessage = errorData?.message || `HTTP error! status: ${statusCode}`;

      // Show toast for certain errors
      if (statusCode >= 400 && statusCode !== 401) {
        toast.error(errorMessage);
      }
    } else if (error.request) {
      // Network error
      errorMessage = 'Network error or server unavailable';
      toast.error(errorMessage);
    } else {
      // Other error
      errorMessage = error.message || 'An unexpected error occurred';
      toast.error(errorMessage);
    }

    throw new ApiError(errorMessage, statusCode, errorData);
  }
);

/**
 * Make an authenticated API request using axios with in-flight de-duplication
 */
const inflightRequests = new Map();

const makeRequest = async (endpoint, options = {}) => {
  const method = (options.method || 'GET').toUpperCase();
  const key = `${method} ${endpoint}`;

  if (inflightRequests.has(key)) {
    return inflightRequests.get(key);
  }

  const reqPromise = (async () => {
    try {
      const response = await axiosInstance({
        url: endpoint,
        ...options,
      });
      return response;
    } catch (error) {
      throw error; // Re-throw as it's already processed by interceptor
    } finally {
      inflightRequests.delete(key);
    }
  })();

  inflightRequests.set(key, reqPromise);
  return reqPromise;
};

/**
 * API methods for different HTTP verbs
 */
export const api = {
  get: (endpoint, params = {}) => {
    return makeRequest(endpoint, {
      method: 'GET',
      params,
    });
  },

  post: (endpoint, data = {}) => {
    return makeRequest(endpoint, {
      method: 'POST',
      data,
    });
  },

  put: (endpoint, data = {}) => {
    return makeRequest(endpoint, {
      method: 'PUT',
      data,
    });
  },

  patch: (endpoint, data = {}) => {
    return makeRequest(endpoint, {
      method: 'PATCH',
      data,
    });
  },

  delete: (endpoint) => {
    return makeRequest(endpoint, { method: 'DELETE' });
  },

  // Special method for file uploads
  upload: (endpoint, formData) => {
    // Explicitly include Authorization header since some environments
    // may skip interceptors for multipart/form-data edge cases
    const token = getAccessToken();
    return makeRequest(endpoint, {
      method: 'POST',
      data: formData,
      headers: token ? { Authorization: `Bearer ${token}` } : undefined,
      // Don't set Content-Type manually - let the browser set it with boundary
    });
  },
};

/**
 * Authentication-specific API calls
 */
export const authApi = {
  register: (userData) => api.post('/auth/register', userData),
  login: (credentials) => api.post('/auth/login', credentials),
  logout: (data = {}) => api.post('/auth/logout', data),
  refreshToken: () => api.post('/auth/refresh'),
  getProfile: () => api.get('/auth/profile'),
  forgotPassword: (email) => api.post('/auth/forgot-password', { email }),
  resetPassword: (data) => api.post('/auth/reset-password', data),
  verifyEmail: (token) => api.post('/auth/verify-email', { token }),
  resendVerification: (email) => api.post('/auth/resend-verification', { email }),
  changePassword: (data) => api.post('/auth/change-password', data),
};

/**
 * User-specific API calls
 */
export const userApi = {
  updateProfile: (data) => api.put('/users/me', data),
  uploadProfileImage: (formData) => api.upload('/users/me/profile-image', formData),
};

/**
 * Vendor-specific API calls
 */
export const vendorApi = {
  getDashboard: () => api.get('/vendors/dashboard'),
  getProfile: () => api.get('/vendors/profile'),
  updateProfile: (data) => api.put('/vendors/profile', data),
  startOnboarding: (onboardingData) => api.post('/vendors/onboarding', onboardingData),
  getOnboardingStatus: () => api.get('/vendors/onboarding/status'),
  forceRefreshStatus: () => api.post('/vendors/onboarding/refresh-status'),
  getServices: () => api.get('/vendors/services'),
  createService: (serviceData) => api.post('/vendors/services', serviceData),
  updateService: (serviceId, serviceData) => api.put(`/vendors/services/${serviceId}`, serviceData),
  deleteService: (serviceId) => api.delete(`/vendors/services/${serviceId}`),
  getBookings: () => api.get('/vendors/bookings'),
  getBalance: () => api.get('/vendors/balance'),
  getAnalytics: () => api.get('/vendors/analytics'),

  // File upload endpoints
  uploadGovernmentId: (formData) => api.upload('/vendors/upload/government-id', formData),
  uploadSelfieVerification: (formData) => api.upload('/vendors/upload/selfie-verification', formData),
  uploadBankStatement: (formData) => api.upload('/vendors/upload/bank-statement', formData),
  uploadDocuments: (formData) => api.upload('/vendors/upload/documents', formData),
  uploadFeaturedImages: (formData) => api.upload('/vendors/upload/featured-images', formData),

  // Enhanced verification endpoints
  getVerificationStatus: () => api.get('/vendors/verification/status'),
  updateStripeAccount: (updateData) => api.post('/vendors/stripe/update-account', { updateData }),
  submitVerificationDocument: (formData) => api.upload('/vendors/verification/submit-document', formData),
};

// Payment API endpoints
export const paymentAPI = {
  // Payment methods management
  getPaymentMethods: () => api.get('/payments/methods'),
  addPaymentMethod: (data) => api.post('/payments/methods', data),
  removePaymentMethod: (paymentMethodId) => api.delete(`/payments/methods/${paymentMethodId}`),

  // Payment processing
  createPaymentIntent: (data) => api.post('/payments/intent', data),
  confirmPayment: (data) => api.post('/payments/confirm', data),

  // Payment history
  getPaymentHistory: (params) => api.get('/payments/history', { params }),
  getTransaction: (transactionId) => api.get(`/payments/transactions/${transactionId}`),

  // Refunds
  processRefund: (bookingId, data) => api.post(`/payments/bookings/${bookingId}/refund`, data),
};

// Additional aliases for backward compatibility
export { vendorApi as vendorAPI };
