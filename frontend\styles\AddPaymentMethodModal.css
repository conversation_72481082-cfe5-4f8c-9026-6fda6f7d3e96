.AddPaymentMethodModal-overlay {
  background: rgba(0,0,0,0.25);
  position: fixed;
  inset: 0;
  z-index: var(--pp-z-index-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-y: auto; /* enable vertical scroll if modal content is tall */
  padding: var(--pp-sp-24) var(--pp-sp-16); /* add side padding */
  -webkit-overflow-scrolling: touch; /* smooth scroll on iOS */
  z-index: 1000000;
}

.AddPaymentMethodModal-modal {
  background: var(--pp-clr-primary);
  border-radius: var(--pp-bor-rad-16);
  width: 100%;
  max-width: 600px;
  padding: var(--pp-sp-24) var(--pp-sp-48); /* increase vertical padding for better spacing */
  box-shadow: var(--pp-shdw-lg);
  position: relative;
  display: flex;
  flex-direction: column;
  max-height: 90vh; /* constrain height to viewport */
  overflow-y: auto; /* allow vertical scroll inside modal */
  scroll-behavior: smooth;
}

.AddPaymentMethodModal-close {
  position: absolute;
  top: var(--pp-sp-16);
  right: var(--pp-sp-20);
  background: none;
  border: none;
  font-size: 2rem;
  color: var(--pp-clr-text-main);
  cursor: pointer;
  border-radius: var(--pp-bor-rad-round);
  width: 2.5rem;
  height: 2.5rem;
}

.AddPaymentMethodModal-title {
  font-size: var(--pp-font-heading4);
  font-weight: 400;
}

.AddPaymentMethodModal-divider {
  height: 1px;
  background: var(--pp-clr-border-light);
  margin: var(--pp-sp-24) 0;
  border: none;
  width: 100%;
}

.AddPaymentMethodModal-form {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-16);
  width: 100%;
}

.AddPaymentMethodModal-cardNumberWrap {
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-8);
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8);
  padding: var(--pp-sp-8);
}

.AddPaymentMethodModal-cardIcon {
  object-fit: contain;
  border-radius: var(--pp-bor-rad-4);
  padding: var(--pp-sp-4);
}

.AddPaymentMethodModal-cardNumberInput {
  flex: 1;
  border: none;
  font-family: inherit;
  font-size: var(--pp-font-base2-font);
  background: transparent;
}

.AddPaymentMethodModal-row {
  display: flex;
  gap: var(--pp-sp-24);
  align-items: flex-end;
}

.AddPaymentMethodModal-expInputs {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8);
}

.AddPaymentMethodModal-slash {
  font-size: var(--pp-font-heading6);
  color: var(--pp-clr-text-gray);
}

.AddPaymentMethodModal-cvvTip {
  font-size: 1em;
  color: var(--pp-clr-blue-txt);
  cursor: help;
  vertical-align: middle;
}

.AddPaymentMethodModal-btn {
  margin-top: var(--pp-sp-12);
  min-width: 230px;
  font-size: var(--pp-font-base2-font);
}

@media (max-width: 650px) {
  .AddPaymentMethodModal-modal {
    padding: var(--pp-sp-20) var(--pp-sp-24);
    max-width: 98vw;
    min-width: 0;
  }
  .AddPaymentMethodModal-row {
    grid-template-columns: 1fr;
    gap: var(--pp-sp-12);
  }
}
@media (max-width: 350px) {
  .AddPaymentMethodModal-row{
    display: grid;
  }
}
