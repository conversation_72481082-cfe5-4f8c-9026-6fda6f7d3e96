import { useState, useEffect } from "react";
import { IoArrowUp } from "react-icons/io5";
import { scrollToTop, getLenisInstance } from "../src/hooks/useLenis";

export default function ScrollToTopButton() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const lenis = getLenisInstance();
    if (!lenis) return;

    const handleScroll = (lenis) => {
      // Show button when scrolled down more than 300px
      setIsVisible(lenis.scroll > 300);
    };

    lenis.on('scroll', handleScroll);

    return () => {
      // Cleanup is handled by Lenis instance
    };
  }, []);

  const handleClick = () => {
    scrollToTop({
      duration: 1.5,
      easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t))
    });
  };

  if (!isVisible) return null;

  return (
    <button
      onClick={handleClick}
      className="scroll-to-top-btn"
      aria-label="Scroll to top"
      type="button"
    >
      <IoArrowUp />
    </button>
  );
}
