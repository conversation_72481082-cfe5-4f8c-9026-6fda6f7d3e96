/* =========================
   ⏱️ Duration Inputs
   ========================= */

.di-duration-inputs {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-24);
}

.di-form-row {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-16);
}

.di-form-group {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-8);
}

.di-form-label {
  font-size: var(--pp-font-extra-small);
  font-weight: 500;
  color: var(--pp-clr-text-main);
  margin: 0;
}

.di-description {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  margin: 0;
  line-height: 1.5;
}

.di-duration-inputs-row {
  display: flex;
  gap: var(--pp-sp-16);
  align-items: center;
}

.di-input-group {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8);
  min-width: 120px;
}

.di-form-input {
  width: 100%;
  padding: var(--pp-sp-12) var(--pp-sp-16);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-8);
  font-size: var(--pp-font-extra-small);
  font-family: var(--pp-font-Metro-Sans);
  background: var(--pp-clr-primary);
  transition: all 0.3s ease;
  color: var(--pp-clr-text-main);
  text-align: center;
}

.di-form-input:focus {
  outline: none;
  border-color: var(--pp-clr-secondary);
}

.di-form-input::-webkit-outer-spin-button,
.di-form-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.di-form-input[type="number"] {
  appearance: textfield;
  -moz-appearance: textfield;
}

/* Custom Select Styling for Duration Inputs */
.di-form-select {
  width: 80px;
  min-width: 80px;
}

.di-form-select .custom-select-box {
  padding: var(--pp-sp-12) var(--pp-sp-16);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-8);
  font-size: var(--pp-font-extra-small);
  font-family: var(--pp-font-Metro-Sans);
  background: var(--pp-clr-primary);
  transition: all 0.3s ease;
  color: var(--pp-clr-text-main);
  text-align: center;
  min-height: 44px;
}

.di-form-select .custom-select-box:focus {
  outline: none;
  border-color: var(--pp-clr-secondary);
}

.di-unit {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  text-align: center;
  font-weight: 500;
  white-space: nowrap;
}

/* Responsive */
@media (max-width: 768px) {
  .di-duration-inputs {
    gap: var(--pp-sp-20);
  }

  .di-form-row {
    gap: var(--pp-sp-12);
  }

  .di-duration-inputs-row {
    gap: var(--pp-sp-12);
  }

  .di-input-group {
    min-width: 100px;
  }
}

@media (max-width: 480px) {
  .di-duration-inputs {
    gap: var(--pp-sp-16);
  }

  .di-form-row {
    gap: var(--pp-sp-10);
  }

  .di-duration-inputs-row {
    flex-direction: column;
    gap: var(--pp-sp-8);
    align-items: stretch;
  }

  .di-input-group {
    min-width: auto;
  }

  .di-form-input {
    padding: var(--pp-sp-10) var(--pp-sp-12);
  }
}
