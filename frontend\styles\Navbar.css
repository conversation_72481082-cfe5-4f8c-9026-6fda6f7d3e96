.c-navbar {
  display: flex;
  flex-direction: column;
  background-color: var(--pp-clr-primary);
  color: var(--pp-clr-text-main);
  position: sticky;
  top: 0;

  z-index: var(--pp-z-index-sticky);
}

.c-navbar__inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--pp-sp-16);
  padding: var(--pp-sp-16);
  padding-bottom: var(--pp-sp-16);
  width: 100%;
}

.c-navbar__logo {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8);
}
.c-navbar__logo-img {
  display: block;
  width: 90px;
  aspect-ratio: 1/1;
}

.c-navbar__nav {
  display: flex;
}

.c-navbar__links {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-32);
  list-style: none;
  margin: 0;
  justify-content: space-between;
  padding: 0;
}

.c-navbar__link,
.c-navbar__mobile-link {
  color: var(--pp-clr-text-main);
  font-weight: 500;
  font-size: var(--pp-font-base-font);
  text-decoration: none;
  transition: color 300ms ease;
}
.c-navbar__link:hover,
.c-navbar__mobile-link:hover {
  text-decoration: underline;
}

.c-navbar__actions {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-12);
}

.c-navbar__icon-btn,
.c-navbar__toggle,
.c-navbar__profile-btn {
  padding: var(--pp-sp-12);
  line-height: 1;
  font-size: var(--pp-font-heading5);
  background: transparent;
  border: none;
  cursor: pointer;
  color: var(--pp-clr-text-main);
  transition: color 300ms ease;
}

.c-navbar__profile-dropdown {
  position: relative;
  display: inline-block;
}

.c-navbar__profile-btn {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8);
}

.c-navbar__profile-avatar {
  font-size: var(--pp-font-heading4);
  margin-left: var(--pp-sp-8);
}

/* Desktop User Menu Dropdown */
.c-navbar__dropdown-menu {
  position: absolute;
  top: calc(100% + var(--pp-sp-8));
  right: 0;
  min-width: 220px;
  background: var(--pp-clr-primary);
  box-shadow: var(--pp-shdw-lg, 0 4px 6px -1px rgba(0, 0, 0, 0.1));
  border-radius: var(--pp-bor-rad-8, 8px);
  border: 1px solid var(--pp-clr-border-light, #e5e7eb);
  padding: var(--pp-sp-8, 8px) 0;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-2, 2px);
}

.c-navbar__dropdown-header {
  font-weight: 600;
  font-size: var(--pp-font-base-font);
  padding: var(--pp-sp-12, 12px) var(--pp-sp-24, 24px);
  border-bottom: var(--pp-bor-w-1, 1px) solid var(--pp-clr-border-light, #e5e7eb);
  color: var(--pp-clr-text-main, #333);
}

.c-navbar__dropdown-link {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8, 8px);
  font-size: var(--pp-font-base-font, 14px);
  color: var(--pp-clr-text-main, #333);
  text-decoration: none;
  padding: var(--pp-sp-12, 12px) var(--pp-sp-24, 24px);
  transition: background 0.2s;
  border: none;
  background: none;
  cursor: pointer;
  width: 100%;
  text-align: left;
}
.c-navbar__dropdown-link:hover {
  background: var(--pp-clr-border-light, #f3f4f6);
  text-decoration: underline;
}
.c-navbar__dropdown-icon {
  font-size: var(--pp-font-base2-font);
}

.c-navbar__vendor-balance {
  margin-left: auto;
  font-weight: 600;
  color: var(--pp-clr-success);
  font-size: var(--pp-font-base-font);
}
.c-navbar__badge {
  margin-left: var(--pp-sp-8);
  background: var(--pp-clr-feature-icon-green);
  color: var(--pp-clr-secondary);
  border-radius: var(--pp-bor-rad-round);
  padding: 0 var(--pp-sp-8);
  font-size: var(--pp-font-small-font);
  font-weight: 600;
}

/* Overlay and drawer for mobile menu */
.c-navbar__overlay {
  position: fixed;
  inset: 0;
  background-color: var(--pp-clr-overlay);
  opacity: 0;
  pointer-events: none;
  transition: opacity 300ms ease;
  z-index: var(--pp-z-index-modal);
}
.c-navbar__overlay.is-open {
  opacity: 1;
  pointer-events: auto;
}
.c-navbar__drawer {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 80%;
  background-color: var(--pp-clr-primary);
  box-shadow: var(--pp-shdw-lg);
  transform: translateX(100%);
  transition: transform 300ms ease;
  display: flex;
  flex-direction: column;
}
.c-navbar__overlay.is-open .c-navbar__drawer {
  transform: translateX(0%);
}

/* Mobile menu content */
.c-navbar__drawer-header {
  display: flex;
  justify-content: flex-end;
  padding: var(--pp-sp-12) var(--pp-sp-16);
  border-bottom: var(--pp-bor-w-1) solid var(--pp-clr-border);
}
.c-navbar__mobile-links {
  display: grid;
  gap: var(--pp-sp-8);
  padding: var(--pp-sp-12) var(--pp-sp-16);
  margin: 0;
  list-style: none;
}
.c-navbar__mobile-link {
  text-decoration: none;
  color: var(--pp-clr-text-main);
  font-family: var(--pp-font-Metro-Sans);
  font-size: var(--pp-font-base-font);
  padding: var(--pp-sp-8) 0;
  transition: color 300ms ease;
}
.c-navbar__mobile-link:hover {
  color: var(--pp-clr-text-gray);
}

/* Hide desktop profile on mobile, profile-mobile visible in drawer */
@media (max-width: 1024px) {
  .c-navbar__links {
    display: none;
  }
  .c-navbar__toggle {
    display: inline-flex;
  }

  .c-navbar__profile-desktop {
    display: none;
  }
  .c-navbar__profile-mobile {
    display: block;
    background: var(--pp-clr-primary);
    margin-bottom: var(--pp-sp-16);
    padding: var(--pp-sp-10) var(--pp-sp-16) 0 var(--pp-sp-16);
    border-bottom: var(--pp-bor-w-1) solid var(--pp-clr-border-light);
  }
}

/* Default: hide mobile profile */
.c-navbar__profile-mobile {
  display: none;
}

/* Desktop: show desktop profile */
@media (min-width: 1024px) {
  .c-navbar__links {
    display: flex;
  }
  .c-navbar__toggle {
    display: none;
  }
  .c-navbar__overlay {
    display: none;
  }
  .c-navbar__profile-desktop {
    display: block;
  }
  .c-navbar__profile-mobile {
    display: none;
  }
}

@media (max-width: 414px) {
  .c-navbar__actions {
    gap: var(--pp-sp-8);
  }
  .c-navbar__icon-btn,
  .c-navbar__toggle {
    padding: var(--pp-sp-8);
    font-size: var(--pp-font-heading6);
  }
}
