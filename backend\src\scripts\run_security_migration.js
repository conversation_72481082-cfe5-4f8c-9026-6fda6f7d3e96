#!/usr/bin/env node

/**
 * CRITICAL SECURITY MIGRATION RUNNER
 * 
 * This script runs the security migration to remove sensitive financial data
 * from the database for PCI DSS compliance.
 * 
 * Usage:
 *   node src/scripts/run_security_migration.js
 * 
 * IMPORTANT: Run this script IMMEDIATELY to ensure compliance
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { removeSensitiveFinancialData, verifySensitiveDataRemoval } = require('../migrations/001_remove_sensitive_financial_data');
const { paymentLogger } = require('../utils/logger');

// Import models to ensure they're registered
require('../models/VendorProfile');

async function runSecurityMigration() {
    try {
        console.log('🚨 CRITICAL SECURITY MIGRATION STARTING...');
        console.log('This migration will remove sensitive financial data from the database');
        console.log('for PCI DSS compliance.\n');

        // Connect to database
        console.log('Connecting to database...');
        await mongoose.connect(process.env.MONGODB_URI, {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });
        console.log('✅ Database connected\n');

        // Run the migration
        console.log('🔒 Running sensitive data removal migration...');
        const result = await removeSensitiveFinancialData();
        
        if (result.success) {
            console.log('✅ Migration completed successfully!');
        } else {
            console.log('⚠️  Migration completed with some errors');
        }
        
        console.log(`📊 Migration Results:`);
        console.log(`   - Total profiles checked: ${result.totalProfiles}`);
        console.log(`   - Profiles processed: ${result.processedCount}`);
        console.log(`   - Errors encountered: ${result.errorCount}\n`);

        // Verify the migration
        console.log('🔍 Verifying sensitive data removal...');
        const verificationPassed = await verifySensitiveDataRemoval();
        
        if (verificationPassed) {
            console.log('✅ VERIFICATION PASSED: No sensitive data found in database');
            console.log('🎉 Your database is now PCI DSS compliant!\n');
        } else {
            console.log('❌ VERIFICATION FAILED: Sensitive data still found in database');
            console.log('⚠️  Please review the logs and run the migration again\n');
            process.exit(1);
        }

        // Log final security status
        paymentLogger.info('SECURITY MIGRATION COMPLETED SUCCESSFULLY', {
            timestamp: new Date().toISOString(),
            totalProfiles: result.totalProfiles,
            processedCount: result.processedCount,
            errorCount: result.errorCount,
            verificationPassed,
            complianceStatus: 'PCI_DSS_COMPLIANT'
        });

        console.log('🔐 SECURITY STATUS: PCI DSS COMPLIANT');
        console.log('All sensitive financial data has been removed from local database.');
        console.log('Sensitive data is now stored securely with Stripe only.\n');

    } catch (error) {
        console.error('❌ CRITICAL ERROR: Security migration failed');
        console.error('Error:', error.message);
        console.error('\nThis is a critical security issue. Please fix the error and run again.');
        
        paymentLogger.error('SECURITY MIGRATION FAILED', {
            error: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString(),
            complianceStatus: 'NON_COMPLIANT'
        });
        
        process.exit(1);
    } finally {
        await mongoose.disconnect();
        console.log('Database connection closed.');
    }
}

// Handle process termination
process.on('SIGINT', async () => {
    console.log('\n⚠️  Migration interrupted by user');
    await mongoose.disconnect();
    process.exit(1);
});

process.on('SIGTERM', async () => {
    console.log('\n⚠️  Migration terminated');
    await mongoose.disconnect();
    process.exit(1);
});

// Run the migration
if (require.main === module) {
    runSecurityMigration();
}

module.exports = { runSecurityMigration };
