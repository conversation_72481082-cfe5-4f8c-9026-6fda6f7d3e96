import React, { useState } from "react";
import sampleImage from "../src/assets/images/character-category.png";
import { IoChevronDown, IoTimeOutline } from "react-icons/io5";
import pendingbadge from "../src/assets/images/Pendingbadge.svg";
import ppaymentbadge from "../src/assets/images/ppaymentbadge.svg";
import cancelledbadge from "../src/assets/images/cancelledbadge.svg";
import "../styles/VendorBookingTable.css";
const bookings = [
  {
    id: 1,
    date: { month: "Jul", day: "14" },
    time: "5:30pm – 9:30pm",
    bookable: "Captain <PERSON>",
    customer: "Candice Dietrich",
    address: "12500 S 725 W\nSalt Lake City, UT 85515",
    status: "Pending Approval",
    amount: "$386.57",
  },
  {
    id: 2,
    date: { month: "Aug", day: "19" },
    time: "4:30pm – 5:30pm",
    bookable: "<PERSON>, <PERSON> Trooper",
    customer: "<PERSON><PERSON>",
    address: "12500 S 725 W\nSalt Lake City, UT 85515",
    status: "Pending Approval",
    amount: "$150.00",
  },
  {
    id: 3,
    date: { month: "Jun", day: "7" },
    time: "4:30pm – 5:30pm",
    bookable: "Storm Trooper",
    customer: "Claire Ramson",
    address: "12500 S 725 W\nSalt Lake City, UT 85515",
    status: "Upcoming",
    amount: "$425.70",
  },
  {
    id: 4,
    date: { month: "Jun", day: "15" },
    time: "4:30pm – 5:30pm",
    bookable: "Batman",
    customer: "Jacquelyn Wuckert",
    address: "12500 S 725 W\nSalt Lake City, UT 85515",
    status: "Upcoming",
    amount: "$102.00",
  },
  {
    id: 5,
    date: { month: "Jun", day: "15" },
    time: "4:30pm – 5:30pm",
    bookable: "Superman, +2 more",
    customer: "Devan Stark",
    address: "12500 S 725 W\nSalt Lake City, UT 85515",
    status: "Pending Payment",
    amount: "$215.00",
  },
  {
    id: 6,
    date: { month: "Jun", day: "22" },
    time: "4:30pm – 5:30pm",
    bookable: "Iron Man",
    customer: "Claire Ramson",
    address: "12500 S 725 W\nSalt Lake City, UT 85515",
    status: "Upcoming",
    amount: "$78.00",
  },
  {
    id: 7,
    date: { month: "Jul", day: "3" },
    time: "4:30pm – 5:30pm",
    bookable: "Batman, Robin",
    customer: "Giovanna Grimes",
    address: "12500 S 725 W\nSalt Lake City, UT 85515",
    status: "Canceled",
    amount: "$274.00",
  },
  {
    id: 8,
    date: { month: "Aug", day: "27" },
    time: "4:30pm – 5:30pm",
    bookable: "Thor",
    customer: "Brody Wunsch",
    address: "12500 S 725 W\nSalt Lake City, UT 85515",
    status: "Upcoming",
    amount: "$680.50",
  },
];

// Table header configuration for reuse
const tableHeaders = [
  { label: "Date/Time", key: "dateTime" },
  { label: "Bookable", key: "bookable" },
  { label: "Customer", key: "customer" },
  { label: "Address", key: "address" },
  { label: "Status", key: "status" },
  { label: "Amount", key: "amount" },
];

const mobileHeaders = [
  { label: "Date/Time", key: "dateTime" },
  { label: "Amount", key: "amount" },
];

const VendorBookingTable = ({ onRowClick = () => {} }) => {
  const [viewMode, setViewMode] = useState("table"); // "table" or "calendar"
  const [filter, setFilter] = useState("All");

  // Get status badge configuration
  const getStatusConfig = (status) => {
    switch (status) {
      case "Pending Approval":
        return {
          icon: pendingbadge,
          mod: "pending-approval",
        };
      case "Pending Payment":
        return {
          icon: ppaymentbadge,
          mod: "pending-payment",
        };
      case "Canceled":
        return {
          icon: cancelledbadge,
          mod: "canceled",
        };
      case "Upcoming":
        // Remove icon for Upcoming, so nothing is rendered
        return {
          icon: null,
          mod: "upcoming",
        };
      default:
        return {
          icon: <IoTimeOutline />,
          mod: "pending-approval",
        };
    }
  };

  // Get date badge color based on status
  const getDateBadgeColor = (status) => {
    switch (status) {
      case "Pending Payment":
        return "green";
      case "Canceled":
        return "red";
      default:
        return "blue";
    }
  };

  const handleRowClick = (booking) => {
    const monthIndex =
      {
        Jan: 0,
        Feb: 1,
        Mar: 2,
        Apr: 3,
        May: 4,
        Jun: 5,
        Jul: 6,
        Aug: 7,
        Sep: 8,
        Oct: 9,
        Nov: 10,
        Dec: 11,
      }[booking.date.month] ?? new Date().getMonth();

    const date = new Date(
      new Date().getFullYear(),
      monthIndex,
      Number(booking.date.day)
    );
    const bookingData = {
      date: date.toISOString(),
      services: [
        {
          title: booking.bookable,
          time: booking.time,
          image: sampleImage,
          vendor: booking.bookable,
        },
      ],
    };
    onRowClick(bookingData);
  };

  // Filter bookings if filter is not "All"
  const filteredBookings =
    filter === "All" ? bookings : bookings.filter((b) => b.status === filter);

  return (
    <div className="vendor-booking-table__cover">
      <div className="vendor-booking-table">
        {/* Desktop Table View */}
        <div className="vendor-booking-table__desktop">
          <table
            className="vendor-booking-table__table"
            role="grid"
            aria-label="Bookings List"
          >
            <thead>
              <tr>
                {tableHeaders.map((header) => (
                  <th className="vendor-booking-table__th" key={header.key}>
                    <div className="flex items-center gap-1">
                      {header.label}
                      <IoChevronDown className="vendor-booking-table__sort-icon" />
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y-[2px] divide-[#D2D2D2] [&>*]:table-row p-1">
              {filteredBookings.map((booking) => {
                const statusConfig = getStatusConfig(booking.status);
                const dateBadgeColor = getDateBadgeColor(booking.status);

                return (
                  <tr
                    key={booking.id}
                    onClick={() => handleRowClick(booking)}
                    className="vendor-booking-table__row"
                  >
                    <td className="vendor-booking-table__td">
                      <div className="vendor-booking-table__date-cell">
                        <div
                          className={`vendor-booking-table__date-badge vendor-booking-table__date-badge--${dateBadgeColor}`}
                        >
                          <div className="vendor-booking-table__date-month">
                            {booking.date.month}
                          </div>
                          <div className="vendor-booking-table__date-day">
                            {booking.date.day}
                          </div>
                        </div>
                        <div className="vendor-booking-table__time">
                          {booking.time}
                        </div>
                      </div>
                    </td>
                    <td className="vendor-booking-table__td vendor-booking-table__bookable">
                      {booking.bookable}
                    </td>
                    <td className="vendor-booking-table__td">
                      {booking.customer}
                    </td>
                    <td className="vendor-booking-table__td">
                      <div className="vendor-booking-table__address">
                        {booking.address.split("\n").map((line, index) => (
                          <div key={index}>{line}</div>
                        ))}
                      </div>
                    </td>
                    <td className="vendor-booking-table__td">
                      <div
                        className={`vendor-booking-table__status-badge vendor-booking-table__status-badge--${statusConfig.mod}`}
                      >
                        <span
                          className={`vendor-booking-table__status-badge-icon vendor-booking-table__status-badge-icon--${statusConfig.mod}`}
                        >
                          {/* Only render the image if statusConfig.icon is a non-empty string (for image), or a React element */}
                          {typeof statusConfig.icon === "string" &&
                          statusConfig.icon ? (
                            <img src={statusConfig.icon} alt="" />
                          ) : typeof statusConfig.icon === "object" &&
                            statusConfig.icon !== null ? (
                            statusConfig.icon
                          ) : null}
                        </span>
                        <span className="vendor-booking-table__status-text">
                          {booking.status}
                        </span>
                      </div>
                    </td>
                    <td className="vendor-booking-table__td vendor-booking-table__amount">
                      {booking.amount}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        {/* Mobile/Tablet Card View */}
        <div className="vendor-booking-table__mobile">
          <div className="vendor-booking-table__mobile-header">
            {mobileHeaders.map((header) => (
              <span
                className="vendor-booking-table__mobile-header-text"
                key={header.key}
              >
                {header.label}
                <IoChevronDown className="vendor-booking-table__sort-icon" />
              </span>
            ))}
          </div>

          <div className="vendor-booking-table__mobile-cards">
            {filteredBookings.map((booking) => {
              const dateBadgeColor = getDateBadgeColor(booking.status);

              return (
                <div
                  key={booking.id}
                  onClick={() => handleRowClick(booking)}
                  className="vendor-booking-table__mobile-card"
                >
                  <div className="vendor-booking-table__mobile-card-content">
                    <div
                      className={`vendor-booking-table__mobile-date-badge vendor-booking-table__mobile-date-badge--${dateBadgeColor}`}
                    >
                      <div className="vendor-booking-table__mobile-date-month">
                        {booking.date.month}
                      </div>
                      <div className="vendor-booking-table__mobile-date-day">
                        {booking.date.day}
                      </div>
                    </div>

                    <div className="vendor-booking-table__mobile-info">
                      <div className="vendor-booking-table__mobile-bookable">
                        {booking.bookable}
                      </div>
                      <div className="vendor-booking-table__mobile-time">
                        {booking.time}
                      </div>
                    </div>
                  </div>

                  <div className="vendor-booking-table__mobile-amount">
                    {booking.amount}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default VendorBookingTable;
