/* =========================
   🔐 ForgotPassword Component Styles
   ========================= */

/* Main Container */
.forgot-password-page {
  display: grid;
  grid-template-columns: 1fr 1fr;
  height: 100vh;
  width: 100%;
  margin: 0;
  background: var(--pp-clr-primary);
}

/* Image Section */
.forgot-password-page__image-section {
  background: var(--pp-clr-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  height: 100%;
  width: 100%;
}

.forgot-password-page__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* Form Section */
.forgot-password-page__form-section {
  background: var(--pp-clr-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  width: 100%;
  height: 100%;
}

.forgot-password-page__form-wrapper {
  width: 100%;
  max-width: 550px;
}

.forgot-password-page__title {
  font-size: var(--pp-font-heading4);
  font-weight: 600;
  color: var(--pp-clr-text-main);
  margin-bottom: var(--pp-sp-10);
  text-align: left;
}

/* Bottom Link */
.forgot-password-page__bottom-link {
  margin-top: var(--pp-sp-20);
}

/* =========================
   📱 Responsive Design
   ========================= */

/* Tablet & Below (≤1024px) */
@media (max-width: 1024px) {
  .forgot-password-page {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }

  .forgot-password-page__image-section {
    height: var(--pp-auth-image-height-tablet);
    order: 1;
  }

  .forgot-password-page__form-section {
    order: 2;
    align-items: flex-start;
    padding: var(--pp-sp-60);
  }
}

/* Mobile (≤768px) */
@media (max-width: 768px) {
  .forgot-password-page__image-section {
    height: var(--pp-auth-image-height-mobile);
  }

  .forgot-password-page__form-section {
    padding: var(--pp-sp-40);
  }

  .forgot-password-page__title {
    font-size: var(--pp-font-heading5);
    margin-bottom: var(--pp-sp-10);
  }
}

/* Small Mobile (≤414px) */
@media (max-width: 414px) {
  .forgot-password-page__image-section {
    display: none;
  }

  .forgot-password-page__form-section {
    padding: var(--pp-sp-24);
  }

  .forgot-password-page__title {
    font-size: var(--pp-font-heading6);
    margin-bottom: var(--pp-sp-10);
  }
}
