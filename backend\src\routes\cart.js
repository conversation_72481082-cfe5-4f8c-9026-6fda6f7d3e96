const express = require('express');
const router = express.Router();
const CartController = require('../controllers/cartController');
const { auth } = require('../middlewares/auth');
const { validateRequest } = require('../middlewares/validation');
const { rateLimiting } = require('../middlewares/rateLimiting');
const Joi = require('joi');

// Validation schemas
const addToCartSchema = Joi.object({
  serviceId: Joi.string().hex().length(24).required(),
  eventDetails: Joi.object({
    date: Joi.date().min('now').required(),
    startTime: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).required(),
    duration: Joi.number().min(30).max(1440).required(), // 30 minutes to 24 hours
    attendeeCount: Joi.number().min(1).max(1000).required(),
    eventType: Joi.string().max(100),
    specialRequests: Joi.string().max(500)
  }).required(),
  selectedOptions: Joi.object({
    package: Joi.string(),
    customizations: Joi.array().items(Joi.string())
  }).required(),
  addOns: Joi.array().items(Joi.object({
    title: Joi.string().required(),
    price: Joi.number().min(0).required(),
    quantity: Joi.number().min(1).default(1)
  })),
  notes: Joi.string().max(500),
  location: Joi.object({
    address: Joi.string().required(),
    city: Joi.string().required(),
    state: Joi.string().required(),
    zipCode: Joi.string().required(),
    country: Joi.string().default('US'),
    lat: Joi.number().min(-90).max(90),
    lng: Joi.number().min(-180).max(180),
    venueNotes: Joi.string().max(300),
    parkingInfo: Joi.string().max(300),
    accessInstructions: Joi.string().max(300)
  }).required()
});

const updateCartItemSchema = Joi.object({
  eventDetails: Joi.object({
    date: Joi.date().min('now'),
    startTime: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
    duration: Joi.number().min(30).max(1440),
    attendeeCount: Joi.number().min(1).max(1000),
    eventType: Joi.string().max(100),
    specialRequests: Joi.string().max(500)
  }),
  selectedOptions: Joi.object({
    package: Joi.string(),
    customizations: Joi.array().items(Joi.string())
  }),
  addOns: Joi.array().items(Joi.object({
    title: Joi.string().required(),
    price: Joi.number().min(0).required(),
    quantity: Joi.number().min(1).default(1)
  })),
  notes: Joi.string().max(500),
  location: Joi.object({
    type: Joi.string().valid('venue', 'home', 'outdoor', 'other'),
    address: Joi.object({
      street: Joi.string(),
      city: Joi.string(),
      state: Joi.string(),
      zipCode: Joi.string(),
      country: Joi.string()
    }),
    lat: Joi.number().min(-90).max(90),
    lng: Joi.number().min(-180).max(180),
    accessInstructions: Joi.string().max(300)
  })
});

const promoCodeSchema = Joi.object({
  promoCode: Joi.string().min(3).max(20).uppercase().required()
});

// Protected routes (authentication required)
router.use(auth);

// Main cart operations
router.get('/', CartController.getCart);
router.post('/add', rateLimiting.moderate, validateRequest(addToCartSchema), CartController.addToCart);
router.delete('/clear', rateLimiting.moderate, CartController.clearCart);

// Individual item operations
router.put('/items/:itemId', rateLimiting.moderate, validateRequest(updateCartItemSchema), CartController.updateCartItem);
router.delete('/items/:itemId', rateLimiting.moderate, CartController.removeFromCart);

// Promo code operations
router.post('/promo-code', rateLimiting.moderate, validateRequest(promoCodeSchema), CartController.applyPromoCode);
router.delete('/promo-code', rateLimiting.moderate, CartController.removePromoCode);

// Checkout preparation
router.get('/checkout/summary', CartController.getCheckoutSummary);
router.post('/checkout/validate', CartController.validateCart);

// Save cart for later (persistence)
router.post('/save', rateLimiting.moderate, async (req, res) => {
  const customerId = req.user.id;
  const { name, description } = req.body;

  // This would typically save the current cart as a "saved cart" or "wishlist"
  // For now, just return success
  const { sendSuccess } = require('../utils/responseHelper');

  const { apiLogger } = require('../utils/logger');
  apiLogger.info('Cart saved for later', {
    customerId,
    cartName: name
  });

  return sendSuccess(res, 'Cart saved successfully', {
    savedAt: new Date(),
    name: name || 'My Saved Cart'
  });
});

// Load saved cart
router.get('/saved', async (req, res) => {
  // This would typically retrieve saved carts for the user
  // For now, return empty array
  const { sendSuccess } = require('../utils/responseHelper');

  return sendSuccess(res, 'Saved carts retrieved successfully', {
    savedCarts: [],
    total: 0
  });
});

// Quick add from service page
router.post('/quick-add/:serviceId', rateLimiting.moderate, async (req, res) => {
  const { serviceId } = req.params;
  const customerId = req.user.id;
  const {
    date,
    startTime,
    duration = 240,
    attendeeCount = 50,
    package: selectedPackage
  } = req.body;

  // Validate required fields
  if (!date || !startTime) {
    const { sendBadRequest } = require('../utils/responseHelper');
    return sendBadRequest(res, 'Date and start time are required for quick add');
  }

  // Get service details
  const Service = require('../models/Service');
  const service = await Service.findById(serviceId);

  if (!service || service.status !== 'active') {
    const { sendNotFound } = require('../utils/responseHelper');
    return sendNotFound(res, 'Service not found or not available');
  }

  // Create basic cart item with default values
  const quickAddData = {
    serviceId,
    eventDetails: {
      date: new Date(date),
      startTime,
      duration,
      attendeeCount,
      eventType: 'Party',
      specialRequests: ''
    },
    selectedOptions: {
      package: selectedPackage || service.pricing.packages?.[0]?.name || 'basic',
      customizations: []
    },
    addOns: [],
    notes: 'Added via quick add - please review details',
    location: {
      type: 'venue',
      address: {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'US'
      }
    }
  };

  // Use the regular add to cart logic
  req.body = quickAddData;
  return CartController.addToCart(req, res);
});

// Cart sharing (generate shareable link)
router.post('/share', rateLimiting.moderate, async (req, res) => {
  const customerId = req.user.id;
  const { expiresIn = 7 } = req.body; // Days

  // This would typically generate a shareable token/link
  // For now, just return a mock link
  const shareToken = `cart_${customerId}_${Date.now()}`;
  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + expiresIn);

  const { sendSuccess } = require('../utils/responseHelper');
  const { apiLogger } = require('../utils/logger');

  apiLogger.info('Cart shared', {
    customerId,
    shareToken,
    expiresAt
  });

  return sendSuccess(res, 'Shareable cart link generated', {
    shareUrl: `${process.env.FRONTEND_URL}/cart/shared/${shareToken}`,
    expiresAt,
    validForDays: expiresIn
  });
});

// Compare cart items
router.post('/compare', rateLimiting.moderate, async (req, res) => {
  const customerId = req.user.id;
  const { itemIds } = req.body;

  if (!Array.isArray(itemIds) || itemIds.length < 2) {
    const { sendBadRequest } = require('../utils/responseHelper');
    return sendBadRequest(res, 'Please select at least 2 items to compare');
  }

  const Cart = require('../models/Cart');
  const cart = await Cart.findOne({ customerId })
    .populate('items.serviceId', 'title pricing features location');

  if (!cart) {
    const { sendNotFound } = require('../utils/responseHelper');
    return sendNotFound(res, 'Cart not found');
  }

  const itemsToCompare = cart.items.filter(item =>
    itemIds.includes(item._id.toString())
  );

  if (itemsToCompare.length !== itemIds.length) {
    const { sendBadRequest } = require('../utils/responseHelper');
    return sendBadRequest(res, 'Some selected items were not found in your cart');
  }

  const comparison = itemsToCompare.map(item => ({
    itemId: item._id,
    serviceTitle: item.serviceId.title,
    eventDate: item.eventDetails.date,
    duration: item.eventDetails.duration,
    attendeeCount: item.eventDetails.attendeeCount,
    totalCost: item.pricing.total,
    selectedPackage: item.selectedOptions.package,
    addOnsCount: item.addOns.length,
    location: item.location.address.city + ', ' + item.location.address.state
  }));

  const { sendSuccess } = require('../utils/responseHelper');
  return sendSuccess(res, 'Cart items comparison generated', {
    comparison,
    comparedAt: new Date(),
    itemCount: comparison.length
  });
});

// Estimate taxes and fees
router.post('/estimate-fees', rateLimiting.moderate, async (req, res) => {
  const customerId = req.user.id;
  const { zipCode, state } = req.body;

  if (!zipCode || !state) {
    const { sendBadRequest } = require('../utils/responseHelper');
    return sendBadRequest(res, 'ZIP code and state are required for fee estimation');
  }

  const Cart = require('../models/Cart');
  const cart = await Cart.findOne({ customerId });

  if (!cart || cart.items.length === 0) {
    const { sendBadRequest } = require('../utils/responseHelper');
    return sendBadRequest(res, 'Cart is empty');
  }

  // Calculate estimated taxes and fees based on location
  const subtotal = cart.totals.subtotal;
  const stateConfig = getStateConfig(state);

  const estimatedTax = subtotal * (stateConfig.taxRate || 0.08); // Default 8%
  const processingFee = subtotal * 0.03; // 3% processing fee
  const serviceFee = 5.99; // Flat service fee

  const totalFees = estimatedTax + processingFee + serviceFee;
  const estimatedTotal = subtotal + totalFees;

  const { sendSuccess } = require('../utils/responseHelper');
  return sendSuccess(res, 'Fees estimated successfully', {
    subtotal,
    fees: {
      tax: {
        rate: stateConfig.taxRate || 0.08,
        amount: estimatedTax
      },
      processing: {
        rate: 0.03,
        amount: processingFee
      },
      service: serviceFee
    },
    totalFees,
    estimatedTotal,
    location: { zipCode, state }
  });
});

// Helper function for state tax configuration
function getStateConfig(state) {
  const stateTaxRates = {
    'CA': { taxRate: 0.095 },
    'NY': { taxRate: 0.08 },
    'TX': { taxRate: 0.0625 },
    'FL': { taxRate: 0.06 },
    'WA': { taxRate: 0.065 },
    // Add more states as needed
  };

  return stateTaxRates[state.toUpperCase()] || { taxRate: 0.08 };
}

module.exports = router;
