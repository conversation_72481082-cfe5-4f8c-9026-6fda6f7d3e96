const winston = require('winston');
const path = require('path');

// Custom log format
const logFormat = winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.errors({ stack: true }),
    winston.format.json(),
    winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
        const logEntry = {
            timestamp,
            level,
            message,
            ...meta
        };

        if (stack) {
            logEntry.stack = stack;
        }

        return JSON.stringify(logEntry);
    })
);

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, '../../logs');

// Winston logger configuration
const logger = winston.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: logFormat,
    defaultMeta: { service: 'party-pipeline-api' },
    transports: [
        // Error logs
        new winston.transports.File({
            filename: path.join(logsDir, 'error.log'),
            level: 'error',
            maxsize: 10 * 1024 * 1024, // 10MB
            maxFiles: 5,
            tailable: true
        }),

        // Combined logs
        new winston.transports.File({
            filename: path.join(logsDir, 'combined.log'),
            maxsize: 10 * 1024 * 1024, // 10MB
            maxFiles: 10,
            tailable: true
        }),

        // Debug logs (only in development)
        ...(process.env.NODE_ENV === 'development' ? [
            new winston.transports.File({
                filename: path.join(logsDir, 'debug.log'),
                level: 'debug',
                maxsize: 5 * 1024 * 1024, // 5MB
                maxFiles: 3,
                tailable: true
            })
        ] : [])
    ],

    // Handle uncaught exceptions
    exceptionHandlers: [
        new winston.transports.File({
            filename: path.join(logsDir, 'exceptions.log'),
            maxsize: 5 * 1024 * 1024,
            maxFiles: 3
        })
    ],

    // Handle unhandled promise rejections
    rejectionHandlers: [
        new winston.transports.File({
            filename: path.join(logsDir, 'rejections.log'),
            maxsize: 5 * 1024 * 1024,
            maxFiles: 3
        })
    ]
});

// Add console transport for development
if (process.env.NODE_ENV !== 'production') {
    logger.add(new winston.transports.Console({
        format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple(),
            winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
                const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
                const logMessage = `${timestamp} [${level}]: ${message}`;

                if (stack) {
                    return `${logMessage}\n${stack}${metaStr ? '\n' + metaStr : ''}`;
                }

                return `${logMessage}${metaStr ? '\n' + metaStr : ''}`;
            })
        )
    }));
}

// Custom logging methods for different contexts
const createContextLogger = (context) => {
    return {
        info: (message, meta = {}) => logger.info(message, { context, ...meta }),
        error: (message, meta = {}) => logger.error(message, { context, ...meta }),
        warn: (message, meta = {}) => logger.warn(message, { context, ...meta }),
        debug: (message, meta = {}) => logger.debug(message, { context, ...meta }),
        verbose: (message, meta = {}) => logger.verbose(message, { context, ...meta })
    };
};

// HTTP request logger
const httpLogger = (req, res, next) => {
    const start = Date.now();

    // Log request
    logger.info('HTTP Request', {
        method: req.method,
        url: req.url,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        userId: req.user?.id || 'anonymous',
        requestId: req.headers['x-request-id'] || 'unknown'
    });

    // Log response when finished
    res.on('finish', () => {
        const duration = Date.now() - start;
        const logLevel = res.statusCode >= 400 ? 'error' : 'info';

        logger[logLevel]('HTTP Response', {
            method: req.method,
            url: req.url,
            statusCode: res.statusCode,
            duration: `${duration}ms`,
            userId: req.user?.id || 'anonymous',
            requestId: req.headers['x-request-id'] || 'unknown'
        });
    });

    next();
};

// Database operation logger
const dbLogger = createContextLogger('database');

// Authentication logger
const authLogger = createContextLogger('authentication');

// Payment logger
const paymentLogger = createContextLogger('payment');

// Email logger
const emailLogger = createContextLogger('email');

// Booking logger
const bookingLogger = createContextLogger('booking');

// API logger
const apiLogger = createContextLogger('api');

// Security logger for suspicious activities
const securityLogger = createContextLogger('security');

// Performance logger
const performanceLogger = createContextLogger('performance');

// Audit logger for admin actions
const auditLogger = createContextLogger('audit');

// Helper function to log errors with stack trace
const logError = (error, context = 'general', additionalInfo = {}) => {
    logger.error('Error occurred', {
        context,
        message: error.message,
        stack: error.stack,
        name: error.name,
        ...additionalInfo
    });
};

// Helper function to log API requests
const logApiRequest = (req, operationType, additionalInfo = {}) => {
    apiLogger.info(`API ${operationType}`, {
        method: req.method,
        url: req.originalUrl,
        userId: req.user?.id,
        userRole: req.user?.role,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        ...additionalInfo
    });
};

// Helper function to log authentication events
const logAuthEvent = (event, userId, additionalInfo = {}) => {
    authLogger.info(`Auth ${event}`, {
        userId,
        ip: additionalInfo.ip,
        userAgent: additionalInfo.userAgent,
        timestamp: new Date().toISOString(),
        ...additionalInfo
    });
};

// Helper function to log payment events
const logPaymentEvent = (event, paymentData) => {
    paymentLogger.info(`Payment ${event}`, {
        amount: paymentData.amount,
        currency: paymentData.currency,
        customerId: paymentData.customerId,
        vendorId: paymentData.vendorId,
        bookingId: paymentData.bookingId,
        paymentIntentId: paymentData.paymentIntentId,
        timestamp: new Date().toISOString()
    });
};

// Helper function to log booking events
const logBookingEvent = (event, bookingData) => {
    bookingLogger.info(`Booking ${event}`, {
        bookingId: bookingData.bookingId,
        customerId: bookingData.customerId,
        vendorId: bookingData.vendorId,
        serviceId: bookingData.serviceId,
        status: bookingData.status,
        amount: bookingData.amount,
        eventDate: bookingData.eventDate,
        timestamp: new Date().toISOString()
    });
};

// Helper function to log security events
const logSecurityEvent = (event, details) => {
    securityLogger.warn(`Security Event: ${event}`, {
        ...details,
        timestamp: new Date().toISOString()
    });
};

// Helper function to log performance metrics
const logPerformance = (operation, duration, additionalInfo = {}) => {
    performanceLogger.info(`Performance: ${operation}`, {
        duration: `${duration}ms`,
        ...additionalInfo,
        timestamp: new Date().toISOString()
    });
};

// Helper function to log admin actions for audit
const logAuditEvent = (adminId, action, target, details = {}) => {
    auditLogger.info(`Audit: ${action}`, {
        adminId,
        action,
        target,
        ...details,
        timestamp: new Date().toISOString()
    });
};

// Express error logger middleware
const errorLogger = (err, req, res, next) => {
    logError(err, 'express', {
        method: req.method,
        url: req.originalUrl,
        userId: req.user?.id,
        ip: req.ip,
        body: req.body,
        params: req.params,
        query: req.query
    });
    next(err);
};

module.exports = {
    logger,
    createContextLogger,
    httpLogger,
    errorLogger,
    dbLogger,
    authLogger,
    paymentLogger,
    emailLogger,
    bookingLogger,
    apiLogger,
    securityLogger,
    performanceLogger,
    auditLogger,
    logError,
    logApiRequest,
    logAuthEvent,
    logPaymentEvent,
    logBookingEvent,
    logSecurityEvent,
    logPerformance,
    logAuditEvent
};
