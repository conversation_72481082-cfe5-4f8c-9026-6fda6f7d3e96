.custom-select-wrapper {
  position: relative;
  width: fit-content;
}

.custom-select-label {
  display: block;
  margin-bottom: 6px;
  font-size: 16px;
  font-weight: 600;
  color: black;
}

.custom-select-box {
  border: 1px solid #ccc;
  border-radius: 6px;
  padding: 10px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
  width: fit-content;
  gap: 8px;

}

.custom-select-box:hover {
  border-color: #888;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}



/* Icon styling */
.custom-select-left-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 16px;
  min-width: 16px;
}

.custom-select-right-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 16px;
  min-width: 16px;
  margin-left: auto;
}

.custom-select-text {
  font-size: var(--pp-font-extra-small);
  flex: 1;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Default dropdown arrow */
.custom-select-arrow {
  transition: transform 0.2s ease;
  color: #666;
}

.custom-select-arrow.open {
  transform: rotate(180deg);
}

/* Disabled state */
.custom-select-box:disabled {
  background: #f5f5f5;
  color: #999;
  cursor: not-allowed;
  border-color: #ddd;
}

.custom-select-box:disabled .custom-select-left-icon,
.custom-select-box:disabled .custom-select-right-icon {
  color: #ccc;
}

.custom-select-dropdown {
  position: absolute;
  top: 105%;
  left: 0;
  width: 100%;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 6px;
  max-height: 220px;
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 999;
  list-style: none;
  margin: 0;
  padding: 0;
}

.custom-select-option {
 margin: 4px 8px;
 padding: 4px 8px;

 border-radius: 4px;
  transition: background 0.2s ease;
  
}

.custom-select-option:last-child {
  border-bottom: none;
}

.custom-select-option:hover {
  background: #f8f9fa;
}

.custom-select-option.selected {
  background: #f8f9fa;
  color: black;
  font-weight: 500;
}

.custom-select-option.focused {
  background: #F4F4F4;
}

/* Responsive design */
@media (max-width: 768px) {
  .custom-select-box {
    min-width: 100px;
 
    gap: 6px;
  }
  
  .custom-select-left-icon,
  .custom-select-right-icon {
    font-size: 14px;
    min-width: 14px;
  }
  
 
}

