import React, { useState } from "react";
import { toast } from "react-toastify";
import { authApi } from "../redux/apiUtils";
import "../styles/CustomerProfile.css";

const CustomerSettings = () => {
  const [form, setForm] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState({});
  const [serverError, setServerError] = useState("");

  function handleChange(e) {
    const { name, value } = e.target;
    setForm((f) => ({ ...f, [name]: value }));
    // Clear inline server error when user edits any field
    if (serverError) setServerError("");
  }

  // Client-side validation mirroring backend strong password rules
  function validateForm() {
    const fieldErrors = {};
    const { currentPassword, newPassword, confirmPassword } = form;

    if (!currentPassword) {
      fieldErrors.currentPassword = "Current password is required";
    }
    if (!newPassword) {
      fieldErrors.newPassword = "New password is required";
    } else {
      if (newPassword.length < 8) {
        fieldErrors.newPassword = "Password must be at least 8 characters";
      } else if (newPassword.length > 128) {
        fieldErrors.newPassword = "Password cannot exceed 128 characters";
      } else {
        // At least one lowercase, one uppercase, one digit, one special character
        const strongRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&]).{8,128}$/;
        if (!strongRegex.test(newPassword)) {
          fieldErrors.newPassword =
            "Password must contain uppercase, lowercase, number, and special character";
        }
      }
    }
    if (!confirmPassword) {
      fieldErrors.confirmPassword = "Confirm password is required";
    } else if (newPassword !== confirmPassword) {
      fieldErrors.confirmPassword = "Passwords do not match";
    }

    setErrors(fieldErrors);
    return Object.keys(fieldErrors).length === 0;
  }

  async function handleSubmit(e) {
    e.preventDefault();

    // Clear previous errors before fresh validation + request
    setErrors({});
    setServerError("");

    const isValid = validateForm();
    if (!isValid) return;

    try {
      setIsSubmitting(true);
      const { currentPassword, newPassword, confirmPassword } = form;
      const res = await authApi.changePassword({
        currentPassword,
        newPassword,
        confirmPassword,
      });

      // API interceptor returns response.data
      const message = res?.message || "Password changed successfully";
      toast.success(message);
      setForm({ currentPassword: "", newPassword: "", confirmPassword: "" });
    } catch (err) {
      // Show backend errors inline
      const backendMsg = err?.data?.message || err?.message || "An error occurred";

      // If server returned field-level errors array, map them
      const serverErrorsArray = err?.data?.errors;
      if (Array.isArray(serverErrorsArray) && serverErrorsArray.length) {
        const mapped = {};
        serverErrorsArray.forEach((e) => {
          if (e?.field && e?.message) mapped[e.field] = e.message;
        });
        setErrors((prev) => ({ ...prev, ...mapped }));
      }

      // Common 400/401 “current password incorrect” -> show under currentPassword and focus it
      if (err?.status === 400 || err?.status === 401) {
        setErrors((prev) => ({ ...prev, currentPassword: backendMsg }));
        // Focus current password input for quick correction
        setTimeout(() => {
          const el = document.getElementById('currentPassword');
          if (el) el.focus();
        }, 0);
      } else {
        setServerError(backendMsg);
      }
    } finally {
      setIsSubmitting(false);
    }
  }

  const toggleCurrentPasswordVisibility = () => {
    setShowCurrentPassword((v) => !v);
  };
  const toggleNewPasswordVisibility = () => {
    setShowNewPassword((v) => !v);
  };
  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword((v) => !v);
  };

  return (
    <form
      className="CustomerAccountSettings-form"
      onSubmit={handleSubmit}
      autoComplete="off"
    >
      {/* Change Password Section */}
      <section className="CustomerAccountSettings-section">
        <h2 className="CustomerAccountSettings-sectionTitle">
          Change Password
        </h2>
        {serverError && (
          <div
            className="pp-alert pp-alert-danger"
            style={{
              marginBottom: 12,
              color: "var(--pp-danger, #d32f2f)",
              background: "#fdecea",
              border: "1px solid #f5c2c7",
              padding: "8px 12px",
              borderRadius: 6,
            }}
            role="alert"
          >
            {serverError}
          </div>
        )}
        <div className="CustomerAccountSettings-grid2">
          <div className="pp-form-group CustomerAccountSettings-grid2-colspan">
            <label className="pp-form-label" htmlFor="currentPassword">
              Current Password
            </label>
            <div style={{ position: "relative" }}>
              <input
                className="pp-form-input"
                type={showCurrentPassword ? "text" : "password"}
                id="currentPassword"
                placeholder="Current Password"
                name="currentPassword"
                value={form.currentPassword}
                onChange={(e) => {
                  handleChange(e);
                  if (errors.currentPassword) setErrors((err) => ({ ...err, currentPassword: undefined }));
                }}
                autoComplete="current-password"
              />
              <button
                type="button"
                onClick={toggleCurrentPasswordVisibility}
                className="login-page__password-toggle"
                aria-label={showCurrentPassword ? 'Hide password' : 'Show password'}
                style={{ position: 'absolute', right: 8, top: '50%', transform: 'translateY(-50%)', background: 'transparent', border: 'none', cursor: 'pointer', padding: 4 }}
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                >
                  {showCurrentPassword ? (
                    <>
                      <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" />
                      <line x1="1" y1="1" x2="23" y2="23" />
                    </>
                  ) : (
                    <>
                      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
                      <circle cx="12" cy="12" r="3" />
                    </>
                  )}
                </svg>
              </button>
            </div>
            {errors.currentPassword && (
              <small style={{ color: "var(--pp-danger, #d32f2f)" }}>{errors.currentPassword}</small>
            )}
          </div>
        </div>

        <div className="CustomerAccountSettings-grid2-colspan-password">
          <div className="pp-form-group">
            <label className="pp-form-label" htmlFor="newPassword">
              New Password
            </label>
            <div style={{ position: "relative" }}>
              <input
                className="pp-form-input"
                type={showNewPassword ? "text" : "password"}
                id="newPassword"
                placeholder="New Password"
                name="newPassword"
                value={form.newPassword}
                onChange={(e) => {
                  handleChange(e);
                  if (errors.newPassword) setErrors((err) => ({ ...err, newPassword: undefined }));
                }}
                autoComplete="new-password"
              />
              <button
                type="button"
                onClick={toggleNewPasswordVisibility}
                className="login-page__password-toggle"
                aria-label={showNewPassword ? 'Hide password' : 'Show password'}
                style={{ position: 'absolute', right: 8, top: '50%', transform: 'translateY(-50%)', background: 'transparent', border: 'none', cursor: 'pointer', padding: 4 }}
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                >
                  {showNewPassword ? (
                    <>
                      <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" />
                      <line x1="1" y1="1" x2="23" y2="23" />
                    </>
                  ) : (
                    <>
                      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
                      <circle cx="12" cy="12" r="3" />
                    </>
                  )}
                </svg>
              </button>
            </div>
            {errors.newPassword && (
              <small style={{ color: "var(--pp-danger, #d32f2f)" }}>{errors.newPassword}</small>
            )}
          </div>
          <div className="pp-form-group">
            <label className="pp-form-label" htmlFor="confirmPassword">
              Confirm New Password
            </label>
            <div style={{ position: "relative" }}>
              <input
                className="pp-form-input"
                type={showConfirmPassword ? "text" : "password"}
                id="confirmPassword"
                placeholder="Confirm New Password"
                name="confirmPassword"
                value={form.confirmPassword}
                onChange={(e) => {
                  handleChange(e);
                  if (errors.confirmPassword) setErrors((err) => ({ ...err, confirmPassword: undefined }));
                }}
                autoComplete="new-password"
              />
              <button
                type="button"
                onClick={toggleConfirmPasswordVisibility}
                className="login-page__password-toggle"
                aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}
                style={{ position: 'absolute', right: 8, top: '50%', transform: 'translateY(-50%)', background: 'transparent', border: 'none', cursor: 'pointer', padding: 4 }}
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                >
                  {showConfirmPassword ? (
                    <>
                      <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" />
                      <line x1="1" y1="1" x2="23" y2="23" />
                    </>
                  ) : (
                    <>
                      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
                      <circle cx="12" cy="12" r="3" />
                    </>
                  )}
                </svg>
              </button>
            </div>
            {errors.confirmPassword && (
              <small style={{ color: "var(--pp-danger, #d32f2f)" }}>{errors.confirmPassword}</small>
            )}
          </div>
        </div>
      </section>
      <div className="CustomerAccountSettings-divider"></div>
      <div className="CustomerAccountSettings-formActions">
        <button
          type="submit"
          className="pp-btn pp-btn-primary CustomerAccountSettings-saveBtn"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Changing..." : "Change Password"}
        </button>
      </div>
    </form>
  );
};

export default CustomerSettings;
