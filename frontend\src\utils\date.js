// Lightweight helpers for rendering dates stored in UTC
// These utilities assume backend stores dates in UTC (ISO or Date)

export const formatLocal = (date, options = {}) => {
    if (!date) return '';
    const d = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date;
    // Use Intl to format in the user's local timezone
    const {
        dateStyle = 'medium',
        timeStyle = 'short',
        locale = undefined
    } = options;
    return new Intl.DateTimeFormat(locale, { dateStyle, timeStyle }).format(d);
};

export const formatInTimezone = (date, timeZone, options = {}) => {
    if (!date || !timeZone) return '';
    const d = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date;
    const {
        dateStyle = 'medium',
        timeStyle = 'short',
        locale = undefined
    } = options;
    return new Intl.DateTimeFormat(locale, { dateStyle, timeStyle, timeZone }).format(d);
};

export const getTimezoneAbbr = (date = new Date(), timeZone) => {
    // Best-effort: format to parts and extract short name if available
    try {
        const parts = new Intl.DateTimeFormat(undefined, {
            timeZone,
            timeZoneName: 'short'
        }).formatToParts(date);
        const tz = parts.find(p => p.type === 'timeZoneName');
        return tz?.value || '';
    } catch {
        return '';
    }
};

// Common IANA zones examples: 'America/New_York' (ET), 'Asia/Kolkata' (IST)
export const ET = 'America/New_York';
export const IST = 'Asia/Kolkata';


