.vendor-verification {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.vendor-verification__loading,
.vendor-verification__error {
  text-align: center;
  padding: 60px 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.vendor-verification__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e9ecef;
}

.vendor-verification__header h1 {
  margin: 0;
  color: #2c3e50;
  font-size: 32px;
  font-weight: 700;
}

.verification-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
}

.verification-status--approved {
  background-color: #d4edda;
  color: #155724;
}

.verification-status--pending {
  background-color: #fff3cd;
  color: #856404;
}

.verification-status--suspended {
  background-color: #f8d7da;
  color: #721c24;
}

.verification-status__icon {
  font-size: 16px;
}

.verification-complete {
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 60px 40px;
  border-radius: 16px;
  margin-bottom: 30px;
}

.verification-complete__icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.verification-complete h2 {
  margin: 0 0 16px 0;
  font-size: 32px;
  font-weight: 700;
}

.verification-complete p {
  font-size: 18px;
  margin-bottom: 30px;
  opacity: 0.9;
}

.verification-complete__capabilities {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.capability {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  padding: 12px 20px;
  border-radius: 8px;
  font-weight: 500;
}

.capability__icon {
  font-size: 20px;
}

.verification-requirements {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.requirement-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.requirement-section--urgent {
  border-left: 4px solid #dc3545;
  background: #fff5f5;
}

.requirement-section h2 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
}

.requirement-section p {
  margin: 0 0 20px 0;
  color: #6c757d;
  font-size: 16px;
}

.requirement-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.requirement-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.requirement-item--overdue {
  background: #fff5f5;
  border-color: #dc3545;
}

.requirement-item__icon {
  font-size: 18px;
  flex-shrink: 0;
}

.requirement-item__text {
  flex: 1;
  font-weight: 500;
  color: #495057;
}

.requirement-item__badge {
  background: #dc3545;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.due-date-notice {
  background: #fff3cd;
  color: #856404;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-weight: 500;
}

.document-upload-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.document-upload-card {
  background: white;
  border: 2px dashed #dee2e6;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: border-color 0.3s ease;
}

.document-upload-card:hover {
  border-color: #007bff;
}

.document-upload-card__header {
  margin-bottom: 20px;
}

.document-upload-card__title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.document-upload-card__required {
  color: #dc3545;
  margin-left: 4px;
}

.document-upload-card__description {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
  line-height: 1.4;
}

.document-upload-card__upload {
  position: relative;
  margin-bottom: 16px;
}

.document-upload-card__input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.document-upload-card__label {
  display: inline-block;
  padding: 12px 24px;
  background: #007bff;
  color: white;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
  position: relative;
  overflow: hidden;
}

.document-upload-card__label:hover {
  background: #0056b3;
}

.document-upload-card__label--disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.document-upload-card__progress {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: rgba(255, 255, 255, 0.3);
}

.document-upload-card__progress-bar {
  height: 100%;
  background: white;
  transition: width 0.3s ease;
}

.document-upload-card__info {
  color: #6c757d;
  font-size: 12px;
  line-height: 1.3;
}

.document-upload-card__info p {
  margin: 2px 0;
}

.capability-status {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.capability-status h2 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
}

.capability-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.capability-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
  transition: all 0.3s ease;
}

.capability-item--enabled {
  background: #d4edda;
  border-color: #c3e6cb;
}

.capability-item--disabled {
  background: #f8d7da;
  border-color: #f5c6cb;
}

.capability-item__icon {
  font-size: 24px;
  flex-shrink: 0;
}

.capability-item__details h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.capability-item__details p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

.verification-help {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  text-align: center;
}

.verification-help h2 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
}

.verification-help p {
  margin: 0 0 20px 0;
  color: #6c757d;
  font-size: 16px;
}

.help-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .vendor-verification {
    padding: 16px;
  }

  .vendor-verification__header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .vendor-verification__header h1 {
    font-size: 24px;
  }

  .verification-complete {
    padding: 40px 20px;
  }

  .verification-complete__capabilities {
    flex-direction: column;
    gap: 16px;
  }

  .document-upload-grid {
    grid-template-columns: 1fr;
  }

  .capability-grid {
    grid-template-columns: 1fr;
  }

  .help-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .requirement-section,
  .capability-status,
  .verification-help {
    padding: 16px;
  }

  .requirement-section h2,
  .capability-status h2,
  .verification-help h2 {
    font-size: 20px;
  }

  .verification-complete__icon {
    font-size: 48px;
  }

  .verification-complete h2 {
    font-size: 24px;
  }
}
