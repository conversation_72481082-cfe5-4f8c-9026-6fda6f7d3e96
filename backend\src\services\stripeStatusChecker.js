const cron = require('node-cron');
const mongoose = require('mongoose');
const VendorProfile = require('../models/VendorProfile');
const StripeService = require('./stripeService');
const { paymentLogger } = require('../utils/logger');

/**
 * Periodic Stripe Account Status Checker
 * This runs every 30 minutes to check pending accounts and update their status
 */

class StripeStatusChecker {
    constructor() {
        this.isRunning = false;
    }

    /**
     * Check all pending vendor accounts and update their Stripe status
     */
    async checkPendingAccounts() {
        if (this.isRunning) {
            paymentLogger.info('Stripe status check already running, skipping...');
            return;
        }

        this.isRunning = true;

        try {
            paymentLogger.info('Starting periodic Stripe status check...');

            // Find all vendor profiles that are pending verification
            const pendingProfiles = await VendorProfile.find({
                stripeAccountId: { $exists: true },
                $or: [
                    { 'verification.status': 'pending' },
                    { stripePayoutsEnabled: false },
                    { stripeChargesEnabled: false }
                ],
                stripeOnboardingCompleted: true
            });

            paymentLogger.info(`Found ${pendingProfiles.length} pending vendor accounts to check`);

            let updatedCount = 0;
            let errorCount = 0;

            for (const vendorProfile of pendingProfiles) {
                try {
                    await this.checkSingleAccount(vendorProfile);
                    updatedCount++;
                } catch (error) {
                    errorCount++;
                    paymentLogger.error('Failed to check account status', {
                        vendorId: vendorProfile.userId,
                        stripeAccountId: vendorProfile.stripeAccountId,
                        error: error.message
                    });
                }
            }

            paymentLogger.info('Periodic Stripe status check completed', {
                totalChecked: pendingProfiles.length,
                updated: updatedCount,
                errors: errorCount
            });

        } catch (error) {
            paymentLogger.error('Periodic Stripe status check failed', {
                error: error.message
            });
        } finally {
            this.isRunning = false;
        }
    }

    /**
     * Check a single vendor account and update its status
     */
    async checkSingleAccount(vendorProfile) {
        try {
            // Get fresh status from Stripe
            const accountStatus = await StripeService.getVendorAccountStatus(vendorProfile.stripeAccountId);

            const previousStatus = {
                stripePayoutsEnabled: vendorProfile.stripePayoutsEnabled,
                stripeChargesEnabled: vendorProfile.stripeChargesEnabled,
                verificationStatus: vendorProfile.verification.status
            };

            // Check if status has changed
            const statusChanged = (
                vendorProfile.stripePayoutsEnabled !== accountStatus.status.payoutsEnabled ||
                vendorProfile.stripeChargesEnabled !== accountStatus.status.chargesEnabled
            );

            if (statusChanged) {
                paymentLogger.info('Account status changed during periodic check', {
                    vendorId: vendorProfile.userId,
                    stripeAccountId: vendorProfile.stripeAccountId,
                    previousStatus,
                    newStatus: {
                        stripePayoutsEnabled: accountStatus.status.payoutsEnabled,
                        stripeChargesEnabled: accountStatus.status.chargesEnabled
                    }
                });

                // Update the vendor profile
                vendorProfile.stripePayoutsEnabled = accountStatus.status.payoutsEnabled;
                vendorProfile.stripeChargesEnabled = accountStatus.status.chargesEnabled;

                // Update verification status
                if (accountStatus.status.chargesEnabled && accountStatus.status.payoutsEnabled) {
                    vendorProfile.verification.status = 'approved';
                    vendorProfile.verification.completedAt = new Date();
                    vendorProfile.verification.notes = 'Account fully verified and operational';

                    paymentLogger.info('🎉 Account approved during periodic check!', {
                        vendorId: vendorProfile.userId,
                        stripeAccountId: vendorProfile.stripeAccountId,
                        businessName: vendorProfile.businessName
                    });
                } else if (accountStatus.status.requiresAction) {
                    vendorProfile.verification.status = 'pending';
                    vendorProfile.verification.notes = `Additional verification required: ${accountStatus.status.requirements?.currently_due?.join(', ') || 'Unknown requirements'}`;
                }

                // Process verification requirements
                if (accountStatus.account) {
                    await StripeService.processVerificationRequirements(vendorProfile, accountStatus.account);
                }

                await vendorProfile.save();

                paymentLogger.info('Vendor profile updated during periodic check', {
                    vendorId: vendorProfile.userId,
                    stripeAccountId: vendorProfile.stripeAccountId,
                    wasApproved: accountStatus.status.chargesEnabled && accountStatus.status.payoutsEnabled
                });
            }

        } catch (error) {
            paymentLogger.error('Failed to check single account', {
                vendorId: vendorProfile.userId,
                stripeAccountId: vendorProfile.stripeAccountId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Start the periodic checker
     */
    start() {
        // Run every 30 minutes
        cron.schedule('*/30 * * * *', () => {
            this.checkPendingAccounts();
        });

        // Also run immediately on startup
        setTimeout(() => {
            this.checkPendingAccounts();
        }, 5000); // Wait 5 seconds after startup

        paymentLogger.info('Stripe status checker started - will run every 30 minutes');
    }

    /**
     * Stop the periodic checker
     */
    stop() {
        cron.destroy();
        paymentLogger.info('Stripe status checker stopped');
    }
}

module.exports = StripeStatusChecker;
