# Node modules
node_modules/
.pnp/
.pnp.js

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Build output
dist/
build/
tmp/
temp/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# dotenv environment variables files
.env
.env.local
.env.*.local
.env.production
.env.test

# OS files
.DS_Store
Thumbs.db

# Editor directories and files
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# TypeScript
*.tsbuildinfo

# Optional eslint cache
.eslintcache

# Parcel cache
.cache
.parcel-cache/

# Vite / Next / Nuxt / Remix / Gatsby (if ever used)
.next/
.nuxt/
.remix/
.cache/
.gatsby/

# Serverless
.serverless/

# Firebase
.firebase/

# Docker
docker-compose.override.yml
.env.docker

# Misc
*.local
*.log.*
coverage-report/

# Lockfiles you might want to track; uncomment if you DO NOT want to commit them
package-lock.json
# yarn.lock
# pnpm-lock.yaml
