import React, { useState } from "react";
import CustomDatePicker from "../customeCalender";
import CustomSelect from "../CustomeSelect";
import { RiDeleteBin6Line } from "react-icons/ri";
import "../../styles/BlackoutDates.css";

export default function BlackoutDates({ dates = [], onChange }) {
  const [newDate, setNewDate] = useState({
    from: "",
    to: "",
    fromTime: "08:00",
    toTime: "22:00",
    allDay: false,
  });

  const handleAddDate = () => {
    if (newDate.from && newDate.to) {
      onChange([...dates, { ...newDate, id: Date.now() }]);
      setNewDate({
        from: "",
        to: "",
        fromTime: "08:00",
        toTime: "22:00",
        allDay: false,
      });
    }
  };

  const handleRemoveDate = (id) => {
    onChange(dates.filter((date) => date.id !== id));
  };

  const handleUpdateDate = (id, field, value) => {
    onChange(
      dates.map((date) => (date.id === id ? { ...date, [field]: value } : date))
    );
  };

  const renderDateItem = (date) => (
    <div key={date.id} className="bd-date-item">
      <div className="bd-date-inputs">
        <div className="bd-date-group">
          <label className="bd-date-label">From</label>
          <div className="bd-date-time-row">
            <CustomDatePicker
              value={date.from}
              onChange={(d) => handleUpdateDate(date.id, "from", d)}
            />
            {!date.allDay && (
              <input
                type="time"
                className="bd-form-input"
                value={date.fromTime}
                onChange={(e) =>
                  handleUpdateDate(date.id, "fromTime", e.target.value)
                }
              />
            )}
          </div>
          <label className="bd-checkbox-wrapper">
            <input
              type="checkbox"
              className="bd-checkbox"
              checked={date.allDay}
              onChange={(e) =>
                handleUpdateDate(date.id, "allDay", e.target.checked)
              }
            />
            <span className="bd-checkbox-label">All Day</span>
          </label>
        </div>

        <div className="bd-date-group">
          <label className="bd-date-label">To</label>
          <div className="bd-date-time-row">
            <CustomDatePicker
              value={date.to}
              onChange={(d) => handleUpdateDate(date.id, "to", d)}
            />
            {!date.allDay && (
              <input
                type="time"
                className="bd-form-input"
                value={date.toTime}
                onChange={(e) =>
                  handleUpdateDate(date.id, "toTime", e.target.value)
                }
              />
            )}
          </div>
          <label className="bd-checkbox-wrapper">
            <input
              type="checkbox"
              className="bd-checkbox"
              checked={date.allDay}
              onChange={(e) =>
                handleUpdateDate(date.id, "allDay", e.target.checked)
              }
            />
            <span className="bd-checkbox-label">All Day</span>
          </label>
        </div>
      </div>

      <button
        type="button"
        className="bd-btn-remove"
        onClick={() => handleRemoveDate(date.id)}
        aria-label="Remove blackout date"
      >
        <RiDeleteBin6Line />
      </button>
    </div>
  );

  return (
    <div className="bd-blackout-dates">
      {dates.map(renderDateItem)}

      <div className="bd-new-date">
        <div className="bd-date-inputs">
          <div className="bd-date-group">
            <label className="bd-date-label">From</label>
            <div className="bd-date-time-row">
              <CustomDatePicker
                value={newDate.from}
                onChange={(d) => setNewDate({ ...newDate, from: d })}
              />
              {!newDate.allDay && (
                <input
                  type="time"
                  className="bd-form-input"
                  value={newDate.fromTime}
                  onChange={(e) =>
                    setNewDate({ ...newDate, fromTime: e.target.value })
                  }
                />
              )}
            </div>
            <label className="bd-checkbox-wrapper">
              <input
                type="checkbox"
                className="bd-checkbox"
                checked={newDate.allDay}
                onChange={(e) =>
                  setNewDate({ ...newDate, allDay: e.target.checked })
                }
              />
              <span className="bd-checkbox-label">All Day</span>
            </label>
          </div>

          <div className="bd-date-group">
            <label className="bd-date-label">To</label>
            <div className="bd-date-time-row">
              <CustomDatePicker
                value={newDate.to}
                onChange={(d) => setNewDate({ ...newDate, to: d })}
              />
              {!newDate.allDay && (
                <input
                  type="time"
                  className="bd-form-input"
                  value={newDate.toTime}
                  onChange={(e) =>
                    setNewDate({ ...newDate, toTime: e.target.value })
                  }
                />
              )}
            </div>
            <label className="bd-checkbox-wrapper">
              <input
                type="checkbox"
                className="bd-checkbox"
                checked={newDate.allDay}
                onChange={(e) =>
                  setNewDate({ ...newDate, allDay: e.target.checked })
                }
              />
              <span className="bd-checkbox-label">All Day</span>
            </label>
          </div>
        </div>
      </div>

      <button
        type="button"
        className="bd-btn-add"
        onClick={handleAddDate}
        disabled={!newDate.from || !newDate.to}
      >
        Add Black-out Dates
      </button>
    </div>
  );
}
