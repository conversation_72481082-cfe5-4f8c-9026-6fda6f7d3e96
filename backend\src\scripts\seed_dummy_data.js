/*
 Seeder: Inserts dummy data for Users (5 customers, 5 vendors), VendorProfiles (5),
 Services (2 per active Category), and Bookings (5).
 Usage:
   node src/scripts/seed_dummy_data.js
 or via npm script (added separately):
   npm run seed:dummy
*/

require('dotenv').config();
const mongoose = require('mongoose');
const connectDB = require('../config/database');

const User = require('../models/User');
const VendorProfile = require('../models/VendorProfile');
const Category = require('../models/Category');
const Service = require('../models/Service');
const Booking = require('../models/Booking');

function randInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function futureDate(daysAhead) {
  const d = new Date();
  d.setDate(d.getDate() + daysAhead);
  return d;
}

async function ensureCategories() {
  const existing = await Category.find({ status: 'active' });
  // Only use existing categories; do not create new ones in this seeder
  return existing;
}

async function createUsers() {
  const customers = [];
  const vendors = [];

  for (let i = 1; i <= 5; i++) {
    customers.push({
      email: `customer${i}@example.com`,
      password: 'Password123!',
      role: 'customer',
      firstName: `Customer${i}`,
      lastName: 'Demo',
      phone: `******-100${i}`,
      isVerified: true,
      addresses: [{
        type: 'home',
        street: `${100 + i} Main St`,
        city: 'Sample City',
        state: 'CA',
        zipCode: `9000${i}`,
        country: 'US',
      }],
    });
  }

  for (let i = 1; i <= 5; i++) {
    vendors.push({
      email: `vendor${i}@example.com`,
      password: 'Password123!',
      role: 'vendor',
      firstName: `Vendor${i}`,
      lastName: 'Demo',
      phone: `******-200${i}`,
      isVerified: true,
    });
  }

  // Avoid duplicate emails if re-running: upsert by email
  const createdCustomers = [];
  for (const c of customers) {
    let user = await User.findOne({ email: c.email }).select('+password');
    if (!user) user = await new User(c).save();
    createdCustomers.push(user);
  }

  const createdVendors = [];
  for (const v of vendors) {
    let user = await User.findOne({ email: v.email }).select('+password');
    if (!user) user = await new User(v).save();
    createdVendors.push(user);
  }

  return { customers: createdCustomers, vendors: createdVendors };
}

async function ensureVendorProfiles(vendors) {
  const profiles = [];
  for (let i = 0; i < vendors.length; i++) {
    const user = vendors[i];
    let profile = await VendorProfile.findOne({ userId: user._id });
    if (!profile) {
      profile = await VendorProfile.create({
        userId: user._id,
        businessName: `Demo Business ${i + 1}`,
        businessType: 'individual',
        description: 'We provide high-quality services for your events.',
        website: 'https://example.com',
        serviceAreas: [{ city: 'Sample City', state: 'CA', radius: 50 }],
        yearsInBusiness: randInt(1, 10),
        teamSize: randInt(1, 10),
        businessHours: Array.from({ length: 7 }).map((_, d) => ({
          dayOfWeek: d,
          isOpen: true,
          openTime: '09:00',
          closeTime: '17:00',
        })),
        verification: { status: 'approved', submittedAt: new Date(), reviewedAt: new Date() },
        stripeOnboardingCompleted: true,
        stripePayoutsEnabled: true,
        isActive: true,
      });
    }
    profiles.push(profile);
  }
  return profiles;
}

function baseAvailability() {
  return {
    schedule: Array.from({ length: 7 }).map((_, d) => ({
      dayOfWeek: d,
      isAvailable: true,
      timeSlots: [{ startTime: '09:00', endTime: '17:00' }],
    })),
    blackoutDates: [],
    minDuration: 60,
    maxDuration: 480,
    advanceBooking: 0,
    maxAdvanceBooking: 365,
  };
}

async function createServices(categories, vendors) {
  const createdServices = [];
  // Round-robin vendors
  let vIdx = 0;
  for (const cat of categories) {
    for (let s = 1; s <= 2; s++) {
      const vendor = vendors[vIdx % vendors.length];
      vIdx++;
      const title = `${cat.name} Service ${s}`;
      const existing = await Service.findOne({ title, categoryId: cat._id });
      if (existing) { createdServices.push(existing); continue; }

      const service = await Service.create({
        vendorId: vendor._id,
        categoryId: cat._id,
        title,
        description: `Professional ${cat.name.toLowerCase()} service ${s} for events.`,
        shortDescription: `Great ${cat.name.toLowerCase()} package` ,
        images: [{ url: 'https://picsum.photos/seed/' + encodeURIComponent(title) + '/800/600', alt: title, isMain: true }],
        pricing: {
          method: 'flat_rate',
          flatRate: randInt(200, 1000),
          depositRequired: false,
        },
        addOns: [
          { title: 'Extra Hour', pricingMethod: 'hourly', price: 100, description: 'Add extra time' },
          { title: 'Premium Package', pricingMethod: 'flat', price: 150, description: 'Upgrade package' },
        ],
        availability: baseAvailability(),
        travelFee: { costPerMile: 2, freeRadius: 10, maxDistance: 100 },
        location: { address: '123 Event St', city: 'Sample City', state: 'CA', zipCode: '90001' },
        serviceArea: { radius: 25, cities: ['Sample City'], states: ['CA'] },
        tags: [cat.name, 'event', 'demo'],
        features: ['Reliable', 'Affordable'],
        requirements: [],
        equipment: [],
        status: 'active',
        cancellationPolicy: { policy: 'moderate' },
      });
      createdServices.push(service);
    }
  }
  return createdServices;
}

async function createBookings(services, customers) {
  const created = [];
  const pickService = (i) => services[i % services.length];
  for (let i = 0; i < 5; i++) {
    const service = pickService(i);
    const vendorId = service.vendorId;
    const customer = customers[i % customers.length];

    const startHour = 10;
    const duration = 120; // minutes
    const startTime = `${String(startHour).padStart(2, '0')}:00`;
    const endTime = `${String(startHour + duration / 60).padStart(2, '0')}:00`;

    const total = service.pricing.flatRate + (i % 2 ? 50 : 0);
    const status = i % 2 === 0 ? 'confirmed' : 'pending';
    const paymentStatus = i % 2 === 0 ? 'paid' : 'pending';

    const exists = await Booking.findOne({ customerId: customer._id, serviceId: service._id });
    if (exists) { created.push(exists); continue; }

    const booking = await Booking.create({
      customerId: customer._id,
      vendorId: vendorId,
      serviceId: service._id,
      eventDetails: {
        date: futureDate(7 + i),
        startTime,
        endTime,
        duration,
        location: {
          address: '789 Party Ave', city: 'Sample City', state: 'CA', zipCode: '90005', country: 'US',
          coordinates: { lat: 34.05 + i * 0.01, lng: -118.24 - i * 0.01 },
        },
        attendees: 20 + i * 5,
        eventType: 'Birthday',
        theme: 'Casual',
      },
      serviceDetails: {
        selectedPackage: null,
        packageDetails: null,
        addOns: i % 2 ? [{ title: 'Premium Package', quantity: 1, price: 50, total: 50 }] : [],
        customizations: [],
        baseServiceCost: service.pricing.flatRate,
        addOnsCost: i % 2 ? 50 : 0,
        travelCost: 0,
        distance: 0,
        subtotal: total,
        taxes: Math.round(total * 0.08),
        fees: Math.round(total * 0.02),
        totalCost: Math.round(total * 1.1),
        deposit: { required: false },
      },
      status,
      paymentStatus,
      source: 'website',
    });

    created.push(booking);
  }
  return created;
}

async function main() {
  await connectDB();

  try {
    // 1) Ensure categories
    const categories = await ensureCategories();
    if (!categories || categories.length === 0) {
      console.error('No active categories found. Seeder will not create categories. Please add categories first.');
      return;
    }

    // 2) Create users
    const { customers, vendors } = await createUsers();

    // 3) Ensure vendor profiles
    await ensureVendorProfiles(vendors);

    // 4) Create services (2 per active category)
    const services = await createServices(categories, vendors);

    // 5) Create bookings (5)
    await createBookings(services, customers);

    console.log('Seeding completed successfully.');
  } catch (err) {
    console.error('Seeding failed:', err);
  } finally {
    await mongoose.connection.close();
    process.exit(0);
  }
}

main();
