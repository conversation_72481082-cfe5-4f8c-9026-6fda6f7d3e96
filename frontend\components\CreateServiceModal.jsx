import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { IoClose, IoChevronBack, IoChevronForward } from "react-icons/io5";
import { closeCreateModal } from "../redux/createServiceSlice";
import { categoryConfig } from "../redux/categoryConfig";
import CustomSelect from "./CustomeSelect";
import GeneralForm from "./common/GeneralForm";
import PhotoForm from "./common/PhotoForm";
import ReviewForm from "./common/ReviewForm";
import PricingForm from "../components/Dynamicservicepage/PricingForm";
import SchedulingForm from "../components/Dynamicservicepage/SchedulingForm";

import "../styles/CreateServiceModal.css";

const TABS = ["General", "Photos", "Pricing", "Scheduling", "Review"];

export default function CreateServiceModal() {
  const dispatch = useDispatch();
  const { isCreateModalOpen, selectedCategory, activeTab } = useSelector(
    (state) => state.createService
  );

  const [currentTab, setCurrentTab] = useState(activeTab);
  const [formData, setFormData] = useState({
    general: {},
    photos: [],
    pricing: {},
    scheduling: {},
    review: {},
  });

  // Reset form if modal opens or category changes
  useEffect(() => {
    if (isCreateModalOpen) {
      setCurrentTab(activeTab || TABS[0]);
      setFormData({
        general: {},
        photos: [],
        pricing: {},
        scheduling: {},
        review: {},
      });
    }
  }, [isCreateModalOpen, selectedCategory, activeTab]);

  if (!isCreateModalOpen) return null;

  // Get dynamic configs for selected category
  const pricingConfig = categoryConfig[selectedCategory]?.pricing || {};
  const schedulingConfig = categoryConfig[selectedCategory]?.scheduling || {};

  // Generic handler per section
  const handleChange = (section, data) => {
    setFormData((prev) => ({ ...prev, [section]: data }));
  };

  // Navigation control
  const currentIndex = TABS.indexOf(currentTab);

  // Generate dynamic title based on category
  const getModalTitle = () => {
    const categoryName =
      selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1);
    return `New ${categoryName} Service`;
  };

  const handleOverlayClick = (e) => {
    // Only close if clicking on the overlay, not the modal content
    if (e.target === e.currentTarget) {
      dispatch(closeCreateModal());
    }
  };

  return (
    <div
      className="csm-modal-overlay"
      role="dialog"
      aria-modal="true"
      onClick={handleOverlayClick}
    >
      <div className="csm-modal-container" aria-labelledby="modal-title">
        <div className="csm-modal-overflowconatiner">
          <header className="csm-modal-header">
            <h2 className="csm-modal-title" id="modal-title">
              {getModalTitle()}
            </h2>
            <button
              className="csm-btn-close"
              aria-label="Close"
              onClick={() => dispatch(closeCreateModal())}
            >
              <IoClose />
            </button>
          </header>

          {/* Desktop Tabs */}
          <nav className="csm-modal-tabs" role="tablist">
            {TABS.map((tab) => (
              <button
                key={tab}
                role="tab"
                aria-selected={currentTab === tab}
                className={`csm-tab${
                  currentTab === tab ? " csm-tab-active" : ""
                }`}
                onClick={() => setCurrentTab(tab)}
                type="button"
              >
                {tab}
              </button>
            ))}
          </nav>

          {/* Mobile Step Navigation with Dropdown */}
          <div className="csm-mobile-tabs">
            <button
              className="csm-mobile-nav-btn"
              onClick={() =>
                currentIndex > 0 && setCurrentTab(TABS[currentIndex - 1])
              }
              disabled={currentIndex === 0}
            >
              <IoChevronBack />
            </button>

            <div className="csm-mobile-dropdown-container">
              <CustomSelect
                value={currentTab}
                onChange={(selectedTab) => setCurrentTab(selectedTab)}
                options={TABS.map((tab, index) => ({
                  label: `${index + 1}. ${tab}`,
                  value: tab
                }))}
                className="csm-mobile-step-select"
                placeholder="Select Step"
              />
            </div>

            <button
              className="csm-mobile-nav-btn"
              onClick={() =>
                currentIndex < TABS.length - 1 &&
                setCurrentTab(TABS[currentIndex + 1])
              }
              disabled={currentIndex === TABS.length - 1}
            >
              <IoChevronForward />
            </button>
          </div>

          <section
            className="csm-modal-content"
            role="tabpanel"
            aria-labelledby={`tab-${currentTab}`}
          >
            {currentTab === "General" && (
              <GeneralForm
                formData={formData.general}
                onChange={(d) => handleChange("general", d)}
              />
            )}
            {currentTab === "Photos" && (
              <PhotoForm
                photos={formData.photos}
                onChange={(d) => handleChange("photos", d)}
              />
            )}
            {currentTab === "Pricing" && (
              <PricingForm
                formData={formData.pricing}
                onChange={(d) => handleChange("pricing", d)}
                config={pricingConfig}
                category={selectedCategory}
              />
            )}
            {currentTab === "Scheduling" && (
              <SchedulingForm
                formData={formData.scheduling}
                onChange={(d) => handleChange("scheduling", d)}
                config={schedulingConfig}
                pricingMethod={formData.pricing?.pricingMethod}
              />
            )}
            {currentTab === "Review" && (
              <ReviewForm
                formData={formData}
                onChange={(d) => handleChange("review", d)}
                onEditSection={(sectionKey) => {
                  const tabMap = {
                    general: "General",
                    photos: "Photos",
                    pricing: "Pricing",
                    scheduling: "Scheduling",
                  };
                  if (tabMap[sectionKey]) {
                    setCurrentTab(tabMap[sectionKey]);
                  }
                }}
              />
            )}
          </section>

          <footer className="csm-modal-footer">
            {currentTab !== TABS[0] && (
              <button
                type="button"
                onClick={() =>
                  currentIndex > 0 && setCurrentTab(TABS[currentIndex - 1])
                }
                className="csm-btn-nav"
              >
                Previous
              </button>
            )}

            {currentTab !== TABS[TABS.length - 1] ? (
              <button
                type="button"
                onClick={() =>
                  currentIndex < TABS.length - 1 &&
                  setCurrentTab(TABS[currentIndex + 1])
                }
                className="csm-btn-nav csm-btn-primary"
              >
                Next
              </button>
            ) : (
              <>
                <button type="button" className="csm-btn-secondary">
                  Save as Draft
                </button>
                <button type="button" className="csm-btn-primary">
                  Publish
                </button>
              </>
            )}
          </footer>
        </div>
      </div>
    </div>
  );
}
