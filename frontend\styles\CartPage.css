.cart-page-root {
  
  max-width: 1400px;
  margin: 0 auto;
  padding: 80px var(--pp-sp-24) 80px var(--pp-sp-24);
  background: var(--pp-clr-bg);
}
.cart-page-root h1 {
  font-family: var(--pp-font-Playfair-Display);
  font-size: var(--pp-font-heading2);
  font-weight: 700;
  color: var(--pp-clr-text-main);
  margin-bottom: var(--pp-sp-24);
}
.cartgridroot{
  display: grid;
  grid-template-columns: 1fr minmax(320px, 380px);
  gap: var(--pp-sp-40);
}
.cart-groups {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-32);
}
.cart-group-title {

  font-size: var(--pp-font-heading6);
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--pp-clr-secondary);
  text-decoration: underline;
}

/* DESKTOP TABLE */
.cart-table-header {
  display: grid;
  grid-template-columns: 2.3fr 1.1fr 1.1fr 1.1fr 1.2fr 0.5fr;
  gap: var(--pp-sp-16);
  font-weight: 600;
  
  
  padding-bottom: var(--pp-sp-8);
  border-bottom: 1px solid ;
}
.cart-table-row {
  display: grid;
  grid-template-columns: 2.3fr 1.1fr 1.1fr 1.1fr 1.2fr 0.5fr;
  gap: var(--pp-sp-16);
  align-items: center;
  padding: var(--pp-sp-16) 0;
  border-bottom: 1px solid #D2D2D2;
  background: var(--pp-clr-primary);
}
.cart-remove-btn {
  background: none;
  border: none;
  color: var(--pp-clr-secondary);
  font-size: var(--pp-font-heading6);
  cursor: pointer;
  align-self: center;
  width: var(--pp-sp-24);
  height: var(--pp-sp-24);
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Service cell desktop */
.cart-service-cell {
  display: flex;
  gap: var(--pp-sp-16);
  align-items: flex-start;
}
.cart-service-img {
  width: 58px;
  aspect-ratio: 1 /1;
  border-radius: var(--pp-bor-rad-8);
  object-fit: cover;
  background: var(--pp-clr-border-light);
}
.cart-service-details {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-4);
}
.cart-service-name {
  font-weight: 600;
  
  color: var(--pp-clr-secondary);
  text-decoration: underline;
  
}
.cart-service-time {
 
  font-weight: 400;
}

/* Desktop quantity select */


/* Summary card */
.cart-summary-card {
  background: var(--pp-clr-card-rentals-bg);
  border-radius: var(--pp-bor-rad-12);
  box-shadow: var(--pp-shdw-sm);
  padding: var(--pp-sp-32);
  min-width: 320px;
  font-size: var(--pp-font-base2-font);
  color: var(--pp-clr-text-main);
  display: flex;
  flex-direction: column;
  gap:10px;
  height: fit-content;
}
.cart-summary-heading {
  font-family: var(--pp-font-Metro-Sans);
  font-size: var(--pp-font-heading5);
  font-weight: 500;
  margin-bottom: var(--pp-font-heading5);
}
.cart-summary-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;

}
.cart-summary-address {
  font-size:14px;

  line-height: 1.4;
}
.cart-total-row {
  font-weight: 700;
  border-top: 1px solid ;
  margin-top: var(--pp-sp-16);
  padding-top: var(--pp-sp-16);
  font-size: var(--pp-font-heading6);
  display: flex;
  justify-content: space-between;
  gap: 10px;
  align-items: center;
}
.cart-checkout-btn {
  margin-top: var(--pp-sp-24);
  width: 100%;
  padding: var(--pp-sp-12) 0;
  border-radius: var(--pp-bor-rad-24);
  background: var(--pp-clr-secondary);
  color: var(--pp-clr-primary);
  font-family: var(--pp-font-Metro-Sans);
  font-size: var(--pp-font-extra-small);
  font-weight: 600;
  border: none;
  cursor: pointer;
  box-shadow: var(--pp-shdw-sm);
  transition: box-shadow 0.2s;
}
.cart-checkout-btn:hover {
  box-shadow: var(--pp-shdw-md);
}
@media (max-width: 414px) {
  .cart-page-root {
   
    padding: 20px var(--pp-sp-12) 80px var(--pp-sp-12);
  }
  .cartgridroot{
    grid-template-columns: 1fr;
}}
/* ========== MOBILE VERSION ========== */
@media (max-width: 414px) {
  /* Hide table header */
  .cart-table-header {
    display: none;
  }
  /* Mobile: each cart item stacks in three rows */
  .cart-table-row {
    display: flex;

    align-items: stretch;
    gap: 15px;
    padding: var(--pp-sp-16) 0;
    border-bottom: 1px solid var(--pp-clr-border-light);
    background: var(--pp-clr-primary);
  }
  /* Service image top row */
  .cart-service-img {
 

    margin-bottom: var(--pp-sp-8);
    align-self: flex-start;
  }
  /* Details middle row */
  .cart-service-details {
    position: relative;
    gap: 10px;
    margin-bottom: var(--pp-sp-8);
    width: 100%;
  }
  /* Third row: horizontal info bar */
  .cart-mobile-info-row {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: var(--pp-sp-16);
    width: 100%;
  }
  .cart-mobile-unit {
   
    font-weight: 400;
    margin-right: var(--pp-sp-8);
    min-width: 60px;
  }
  .cart-mobile-duration {
   
    font-weight: 400;
    min-width: 60px;
  }
  /* Mobile: horizontal quantity input */
  .cart-quantity-control {
    display: flex;
    align-items: center;
    justify-content: space-between;

    border: 1px solid var(--pp-clr-border);
    font-weight: 400;
  }
  .cart-quantity-btn {
    width: 100%;
    border: none;
    border-radius: var(--pp-bor-rad-8);
    color: var(--pp-clr-secondary);
    font-size: var(--pp-font-extra-small);
    cursor: pointer;
    font-weight: 600;
  }
  .cart-quantity-val {
    margin: 0;
    width: 100%;
    min-width: 16px;
    text-align: center;
    border-right: 1px solid var(--pp-clr-border);
    border-left: 1px solid var(--pp-clr-border);
  }
  .cart-remove-btn {
    top: 10px;
    right: 10px;
    position: absolute;
    font-size: 28px;
    font-weight: 500;
  }
  .cart-mobile-amount {
    margin-left: auto;
    font-size: var(--pp-font-extra-small);
    font-weight: 600;
    color: var(--pp-clr-secondary);
  }
  .cart-summary-card {
    padding: var(--pp-sp-16);
    font-size: var(--pp-font-small-font);
    margin-bottom: var(--pp-sp-40);
  }
  .cart-checkout-btn {
    padding: var(--pp-sp-12) 0;
    font-size: var(--pp-font-extra-small);
  }
}
@media (max-width: 412px) {
  .cart-summary-card {
    padding: var(--pp-sp-12);
  }
}
