import React, { useState, useEffect } from "react";
import "../styles/CustomerMessages.css";
import MessagesUI from "../components/messages/MessagesUI";
import thor from "../src/assets/images/thor.svg";
import vendorprofileicon from "../src/assets/images/vendorprofileicon.svg";
import myprofileicon from "../src/assets/images/myprofileicon.svg";

export default function MessagesPage() {
  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);

  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [selectedChatId, setSelectedChatId] = useState(null);
  const [input, setInput] = useState("");

  // Original vendor conversations
  const conversations = [
    {
      id: 1,
      name: "<PERSON><PERSON><PERSON>",
      preview: "Can you share the invoice?",
      time: "May 27",
    },
    {
      id: 2,
      name: "<PERSON>",
      preview: "No worries, thanks!",
      time: "May 27",
    },
    {
      id: 3,
      name: "<PERSON>",
      preview: "The party will be at Sunset Park...",
      time: "May 27",
    },
    {
      id: 4,
      name: "<PERSON>dice <PERSON>",
      preview: "Hi <PERSON>! Thanks for booking...",
      time: "May 27",
    },
    {
      id: 5,
      name: "Kyle Hines",
      preview: "The TX Charter has booked...",
      time: "May 27",
    },
    {
      id: 6,
      name: "Novella Wehner",
      preview: "Are you free this week?",
      time: "May 27",
    },
    {
      id: 7,
      name: "Cary Gleicher",
      preview: "Let me know availability.",
      time: "May 27",
    },
    {
      id: 8,
      name: "Esperanza Schaefer",
      preview: "Sent you the docs.",
      time: "May 27",
    },
    {
      id: 9,
      name: "Brody Wurusch",
      preview: "This Thurs works for me.",
      time: "May 27",
    },
  ];

  // Base message templates
  const baseMessages = [
    {
      id: "m1",
      from: "them",
      author: "Claire Ramson",
      date: "May 27",
      text: "The party will be at Sunset Park. We'll be at the farthest Northwest pavilion, just south of 1720N. Also do you have additional pictures of the characters?",
    },
    {
      id: "m2",
      from: "me",
      author: "Cleanup",
      date: "May 27",
      text: "Hi Claire! Thanks for booking with Party Pipeline! Thanks for the location info. I just emailed you some additional pictures of the characters that will be attending your event. Please let me know if there is anything else I can help with. We look forward to your event!",
    },
    {
      id: "m4",
      from: "me",
      author: "Cleanup",
      date: "May 27",
      text: "Hi Claire! Thanks for booking with Party Pipeline! Thanks for the location info. I just emailed you some additional pictures of the characters that will be attending your event. Please let me know if there is anything else I can help with. We look forward to your event!",
    },
    {
      id: "m5",
      from: "me",
      author: "Cleanup",
      date: "May 27",
      text: "Hi Claire! Thanks for booking with Party Pipeline! Thanks for the location info. I just emailed you some additional pictures of the characters that will be attending your event. Please let me know if there is anything else I can help with. We look forward to your event!",
    },
  ];

  // Build contacts array to match CustomerMessages design
  const [contacts, setContacts] = useState(
    conversations.map((c, idx) => {
      // Assign avatar similar to CustomerMessages assets
      const theirAvatar = idx % 3 === 2 ? vendorprofileicon : thor;
      const myAvatar = myprofileicon;

      // Build per-conversation messages so each chat shows different content
      const msgs = [
        {
          id: `c${c.id}-1`,
          sender: c.name,
          avatar: theirAvatar,
          date: c.time,
          text: c.preview,
          isUser: false,
        },
        {
          id: `c${c.id}-2`,
          sender: "You",
          avatar: myAvatar,
          date: c.time,
          text: idx % 2 === 0 ? "Thanks! I also sent you the event details via email." : "Got it. I will review and get back shortly.",
          isUser: true,
        },
      ];

      return {
        id: c.id,
        name: c.name,
        avatar: theirAvatar,
        lastMessage: c.preview,
        lastDate: c.time,
        unread: idx === 0 ? 1 : 0,
        messages: msgs,
      };
    })
  );

  const selected = contacts.find((c) => c.id === selectedChatId);
  const messages = selected?.messages ?? [];

  function handleOpenConversation(id) {
    setSelectedChatId(id);
    setIsSidebarOpen(false);
  }

  function handleBackToList() {
    setIsSidebarOpen(true);
  }

  function handleSendMessage(e) {
    e.preventDefault();
    if (!input.trim() || !selectedChatId) return;
    const newMsg = {
      id: messages.length + 1,
      sender: "Me",
      avatar: null,
      date: "",
      text: input.trim(),
      isUser: true,
    };
    setContacts((prev) =>
      prev.map((c) =>
        c.id === selectedChatId
          ? {
              ...c,
              messages: [...c.messages, newMsg],
              lastMessage: `You: ${input.trim()}`,
            }
          : c
      )
    );
    setInput("");
  }

  return (
    <MessagesUI
     
      contacts={contacts}
      selectedChatId={selectedChatId}
      isSidebarOpen={isSidebarOpen}
      onOpenConversation={handleOpenConversation}
      onBackToList={handleBackToList}
      messages={messages}
      composeAvatar={myprofileicon}
      inputValue={input}
      onInputChange={(e) => setInput(e.target.value)}
      onSend={handleSendMessage}
    />
  );
}
