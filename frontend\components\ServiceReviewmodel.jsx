import React from "react";
import "../styles/ServiceReviewmodel.css";
import { MdOutlineStarBorderPurple500 } from "react-icons/md";
import smileimg from "../src/assets/images/smileemoji.svg";
import { FaArrowRight } from "react-icons/fa6";
import { IoMdStar } from "react-icons/io";

// Example review data object
const reviewsData = [
  {
    author: "<PERSON>",
    rating: 5,
    date: "Aug 23, 2023",
    text: "Duis tristique maximus libero id sagittis. Nunc pharetra justo lacus, a dignissim sem molestie eget. Ut euismod leo vel ligula consequat ultrices. Mauris venenatis tincidunt nisi, non iaculis eros pulvinar in. Sed ultrices sodales.",
    showReadMore: true,
  },
  {
    author: "Niles Peppertrout",
    rating: 4,
    date: "Aug 7, 2023",
    text: "Mauris venenatis tincidunt nisi, non iaculis eros pulvinar in. Sed ultrices sodales orci sit amet auctor. Mauris ac turpis ultrices, tincidunt ante sit amet, lacinia diam.",
    showReadMore: false,
  },
  {
    author: "C<PERSON><PERSON>",
    rating: 5,
    date: "June 12, 2023",
    text: "Duis tristique maximus libero id sagittis. Nunc pharetra justo lacus, a dignissim sem molestie eget. Ut euismod leo vel ligula consequat ultrices. Mauris venenatis tincidunt nisi, non iaculis eros pulvinar in. Sed ultrices sodales.",
    showReadMore: true,
  },
  {
    author: "Sarah McFenson",
    rating: 4,
    date: "April 30, 2023",
    text: "Duis tristique maximus libero id sagittis. Nunc pharetra justo lacus, a dignissim sem molestie eget. Ut euismod leo vel ligula consequat ultrices. Mauris venenatis tincidunt nisi, non iaculis eros pulvinar in. Sed ultrices sodales.",
    showReadMore: true,
  },
  {
    author: "Matt Jenkins",
    rating: 3,
    date: "Jan 3, 2023",
    text: "Duis tristique maximus libero id sagittis.",
    showReadMore: false,
  },
];

const ServiceReviewDetails = ({ service, onClose }) => (
  <div className="pp-service-review-modal">
    <div className="pp-service-review-modal__backdrop" onClick={onClose}></div>
  
  <div className="pp-service-review-modal__card overflow-hidden">
  <div className="overflow-auto p-8">
      <button className="pp-service-review-modal__close" onClick={onClose}>
        ×
      </button>
      <div className="pp-service-review-modal__header">
        <h2><strong>87 Reviews</strong> for Bumble Photo Kiosk</h2>
        <div className="pp-service-review-modal__summary">
          <span className="pp-service-review-modal__rating ">4.9</span>
          <span className="flex">
            {[...Array(5)].map((_, i) =>
              i < 5 ? (
                <IoMdStar
                  key={i}
                  style={{
                    color: "var(--pp-clr-warning)",
                  }}
                  size={22}
                />
              ) : (
                <MdOutlineStarBorderPurple500
                  key={i}
                  style={{
                    color: "var(--pp-clr-border-dark)",
                  }}
                  size={22}
                />
              )
            )}
          </span>
        </div>
      </div>
      <div className="pp-service-review-modal__list">
        {reviewsData.map((review, idx) => (
          <div className="pp-service-review-modal__item" key={idx}>
            <div className="pp-service-review-modal__item-header">
              <span className="pp-service-review-modal__item-author">
                {review.author}
              </span>
              <div className="flex items-center gap-2 ">
                <span className="pp-service-review-modal__item-rating">
                  {[...Array(5)].map((_, i) =>
                    i < review.rating ? (
                      <IoMdStar
                        key={i}
                        style={{
                          color: "var(--pp-clr-warning)",
                        }}
                        size={18}
                      />
                    ) : (
                      <MdOutlineStarBorderPurple500
                        key={i}
                        style={{
                          color: "var(--pp-clr-border-dark)",
                        }}
                        size={18}
                      />
                    )
                  )}
                  
                </span>
                <span className="pp-service-review-modal__item-date">
                  {review.date}
                </span>
              </div>
            </div>
            <div className="pp-service-review-modal__item-text">
              {review.text}
              {review.showReadMore && (
                <>
                  {" "}
                  <span className="pp-service-review-modal__readmore">
                    Read more
                  </span>
                </>
              )}
            </div>
          </div>
        ))}
      </div>
    <div className="w-full border-b border-[#D2D2D2] pb-5">
    <button className="pp-service-review-modal__readmorebtn" type="button">
        Read More
      </button>
    </div>
      <form className="pp-service-review-modal__form">
        <label htmlFor="review" className="pp-label">
          Write a Review
        </label>
        {/* Star rating */}
        <div className="pp-service-review-modal__form-rating ">
          {[...Array(5)].map((_, i) => (
            <MdOutlineStarBorderPurple500
              key={i}
              size={32}
              style={{
                color: "var(--pp-clr-border-dark)",
                cursor: "pointer",
              }}
            />
          ))}
          <span className="pp-service-review-modal__form-clear">Clear</span>
        </div>
        <div className="flex items-center gap-2 w-[80%]">
          <div className="pp-service-review-modal__form-smile flex items-center">
            <img src={smileimg} alt="smile" className="w-8  aspect-square" />
          </div>
          <div className="flex items-center w-full relative">
            <input
              type="text"
              id="review"
              className="pp-input"
              rows={3}
              placeholder="Share your experience"
            />
            <button className="sendbtn" type="submit">
              <span role="img" aria-label="Send">
                <FaArrowRight />
              </span>
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
  </div>
);

export default ServiceReviewDetails;
