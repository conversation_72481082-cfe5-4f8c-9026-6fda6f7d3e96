.CustomerPaymentMethods-root {
    max-width: 1400px;
    margin: 0 auto;
   
    background: var(--pp-clr-bg);
    color: var(--pp-clr-text-main);
    font-family: var(--pp-font-Metro-Sans);
  }
  
  .CustomerPaymentMethods-title {
    font-size: var(--pp-font-heading4);
    font-weight: 700;
    margin-bottom: var(--pp-sp-24);
  }
  .CustomerPaymentMethods-tabs {
    display: flex;
    gap: var(--pp-sp-24);
    border-bottom: 1px solid var(--pp-clr-border-light);
    margin-bottom: var(--pp-sp-24);
  }
  .CustomerPaymentMethods-tab {
    font-size: var(--pp-font-extra-small);
    color: var(--pp-clr-text-gray);
    text-decoration: none;
    padding-bottom: var(--pp-sp-12);
    transition: color .18s;
  }
  .CustomerPaymentMethods-tab--active {
    color: var(--pp-clr-secondary);
    font-weight: 600;
    border-bottom: 2px solid var(--pp-clr-secondary);
  }
  
  
  
  .CustomerPaymentMethods-sectionTitle {
    font-size: var(--pp-font-heading5);
    font-weight: 600;
    margin-bottom: var(--pp-sp-24);
  }
  
  .CustomerPaymentMethods-table {
    display: flex;
    flex-direction: column;
    width: 100%;
    border-radius: var(--pp-bor-rad-8);
    overflow: hidden;
    box-shadow: none;
    border: 1px solid var(--pp-clr-border-light);
    background: var(--pp-clr-primary);
    margin-bottom: var(--pp-sp-32);
  }
  .CustomerPaymentMethods-row {
    display: grid;
    grid-template-columns: 1.6fr 1fr 2fr;
    align-items: center;
    gap: var(--pp-sp-16);
    padding: var(--pp-sp-16) var(--pp-sp-24);
    border-bottom: 1px solid var(--pp-clr-border-light);
  }
  .CustomerPaymentMethods-row:last-child {
    border-bottom: none;
  }
  
  .CustomerPaymentMethods-methodinfo {
    display: flex;
    align-items: center;
    gap: var(--pp-sp-12);
  }
  .CustomerPaymentMethods-methodicon {
    height: 40px;
    width: 40px;
    object-fit: contain;
    margin-right: var(--pp-sp-8);
  }
  .CustomerPaymentMethods-methodtype {
    font-size: var(--pp-font-small-font);
    font-weight: 600;
    color: var(--pp-clr-text-main);
  }
  .CustomerPaymentMethods-methodtext {
    font-size: var(--pp-font-extra-small);
    color: var(--pp-clr-text-gray);
  }
  
  .CustomerPaymentMethods-role {
   
    font-size: var(--pp-font-extra-small);
  }
  
  
  .CustomerPaymentMethods-actions {
    display: flex;
    gap: var(--pp-sp-16);
    align-items: center;
    justify-content: flex-end;
  }
  
  .CustomerPaymentMethods-verified {
    font-size: var(--pp-font-extra-small);
    display: flex;
    align-items: center;
    gap: var(--pp-sp-4);
    color: var(--pp-clr-text-main);
    font-weight: 500;
  }
  .CustomerPaymentMethods-verifieddot {
    display: flex;
    font-size: 1.1em;
    padding: 1px var(--pp-sp-4);
   
    border-radius: 50%;
   
    width: 25px;
    height: 25px;
    vertical-align: middle;
  }
  
  .CustomerPaymentMethods-secondaryBtn {
    border: 1px solid var(--pp-clr-border);
    background: var(--pp-clr-primary);
    color: var(--pp-clr-text-main);
    padding: var(--pp-sp-8) var(--pp-sp-20);
    border-radius: var(--pp-bor-rad-24);
    min-width: 25px;
    font-size: var(--pp-font-extra-small);
    font-weight: 500;
    cursor: pointer;
    transition: background .18s, border .18s;
    margin-right: 0;
  }
  .CustomerPaymentMethods-secondaryBtn:hover {
    border-color: var(--pp-clr-secondary);
  }
  
  .CustomerPaymentMethods-addBtn {
    margin-top: var(--pp-sp-24);
    min-width: 180px;
  }
  
  @media (max-width: 700px) {
    .CustomerPaymentMethods-root {
      padding: var(--pp-sp-12);
    }
    .CustomerPaymentMethods-addBtn{
        margin-top: var(--pp-sp-8);
    }
    .CustomerPaymentMethods-table {
    
      margin-bottom: var(--pp-sp-8);
    }
    .CustomerPaymentMethods-row {
      grid-template-columns: 1fr;
      grid-row-gap: var(--pp-sp-8);
      padding: var(--pp-sp-16) var(--pp-sp-8);
      border-bottom: 1px solid var(--pp-clr-border-light);
    }
    .CustomerPaymentMethods-actions {
      justify-content: flex-start;
      flex-wrap: wrap;
      gap: var(--pp-sp-12);
    }
  }
  