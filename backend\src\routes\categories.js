const express = require('express');
const router = express.Router();
const ServiceController = require('../controllers/serviceController');
const AdminController = require('../controllers/adminController');
const { auth, authorize } = require('../middlewares/auth');
const { validateRequest } = require('../middlewares/validation');
const { rateLimiting } = require('../middlewares/rateLimiting');
const Joi = require('joi');

// Validation schemas
const createCategorySchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  slug: Joi.string().min(2).max(100),
  description: Joi.string().max(500),
  parentId: Joi.string().hex().length(24),
  icon: Joi.string().max(100),
  featured: Joi.boolean().default(false),
  sortOrder: Joi.number().min(0).default(0),
  metadata: Joi.object({
    keywords: Joi.array().items(Joi.string()),
    metaTitle: Joi.string().max(200),
    metaDescription: Joi.string().max(300)
  }),
  pricing: Joi.object({
    suggestedMinPrice: Joi.number().min(0),
    suggestedMaxPrice: Joi.number().min(0),
    averagePrice: Joi.number().min(0)
  }),
  requirements: Joi.array().items(Joi.object({
    name: Joi.string().required(),
    description: Joi.string(),
    isRequired: Joi.boolean().default(false),
    options: Joi.array().items(Joi.string())
  })),
  attributes: Joi.array().items(Joi.object({
    name: Joi.string().required(),
    type: Joi.string().valid('text', 'number', 'boolean', 'select', 'multiselect').required(),
    options: Joi.array().items(Joi.string()),
    isRequired: Joi.boolean().default(false)
  }))
});

const updateCategorySchema = Joi.object({
  name: Joi.string().min(2).max(100),
  slug: Joi.string().min(2).max(100),
  description: Joi.string().max(500),
  parentId: Joi.string().hex().length(24).allow(null),
  icon: Joi.string().max(100),
  featured: Joi.boolean(),
  sortOrder: Joi.number().min(0),
  status: Joi.string().valid('active', 'inactive'),
  metadata: Joi.object({
    keywords: Joi.array().items(Joi.string()),
    metaTitle: Joi.string().max(200),
    metaDescription: Joi.string().max(300)
  }),
  pricing: Joi.object({
    suggestedMinPrice: Joi.number().min(0),
    suggestedMaxPrice: Joi.number().min(0),
    averagePrice: Joi.number().min(0)
  }),
  requirements: Joi.array().items(Joi.object({
    name: Joi.string().required(),
    description: Joi.string(),
    isRequired: Joi.boolean().default(false),
    options: Joi.array().items(Joi.string())
  })),
  attributes: Joi.array().items(Joi.object({
    name: Joi.string().required(),
    type: Joi.string().valid('text', 'number', 'boolean', 'select', 'multiselect').required(),
    options: Joi.array().items(Joi.string()),
    isRequired: Joi.boolean().default(false)
  }))
});

const searchCategoriesSchema = Joi.object({
  q: Joi.string().min(1).max(100),
  parentId: Joi.string().hex().length(24),
  featured: Joi.boolean(),
  status: Joi.string().valid('active', 'inactive'),
  page: Joi.number().min(1).default(1),
  limit: Joi.number().min(1).max(50).default(20),
  sortBy: Joi.string().valid('name', 'sortOrder', 'createdAt', 'serviceCount').default('sortOrder')
});

// =============================================================================
// PUBLIC ROUTES (No authentication required)
// =============================================================================

/**
 * Get all categories with optional filtering
 * GET /api/categories
 */
router.get('/', 
  rateLimiting.lenient, 
  validateRequest(searchCategoriesSchema, 'query'), 
  ServiceController.getCategories
);

/**
 * Get category by slug
 * GET /api/categories/:slug
 */
router.get('/:slug', 
  rateLimiting.lenient, 
  ServiceController.getCategoryBySlug
);

/**
 * Get featured categories
 * GET /api/categories/featured
 */
router.get('/featured', 
  rateLimiting.lenient, 
  async (req, res) => {
    try {
      const Category = require('../models/Category');
      const categories = await Category.findFeatured();
      
      const { sendSuccess } = require('../utils/responseHelper');
      return sendSuccess(res, 'Featured categories retrieved successfully', {
        categories
      });
    } catch (error) {
      const { sendServerError } = require('../utils/responseHelper');
      return sendServerError(res, 'Failed to retrieve featured categories');
    }
  }
);

/**
 * Get category hierarchy/tree
 * GET /api/categories/tree
 */
router.get('/tree', 
  rateLimiting.lenient, 
  async (req, res) => {
    try {
      const Category = require('../models/Category');
      const tree = await Category.buildTree();
      
      const { sendSuccess } = require('../utils/responseHelper');
      return sendSuccess(res, 'Category tree retrieved successfully', {
        categories: tree
      });
    } catch (error) {
      const { sendServerError } = require('../utils/responseHelper');
      return sendServerError(res, 'Failed to retrieve category tree');
    }
  }
);

/**
 * Search categories
 * GET /api/categories/search
 */
router.get('/search', 
  rateLimiting.moderate, 
  validateRequest(searchCategoriesSchema, 'query'), 
  async (req, res) => {
    try {
      const { q, parentId, featured, status, page, limit, sortBy } = req.query;
      const Category = require('../models/Category');
      
      let query = {};
      
      if (q) {
        query.$text = { $search: q };
      }
      
      if (parentId) {
        query.parentId = parentId;
      }
      
      if (featured !== undefined) {
        query.isFeatured = featured;
      }
      
      if (status) {
        query.status = status;
      } else {
        query.status = 'active'; // Default to active only
      }
      
      const skip = (page - 1) * limit;
      const sort = {};
      
      switch (sortBy) {
        case 'name':
          sort.name = 1;
          break;
        case 'createdAt':
          sort.createdAt = -1;
          break;
        case 'serviceCount':
          // This would require aggregation - simplified for now
          sort.name = 1;
          break;
        default:
          sort.sortOrder = 1;
          sort.name = 1;
      }
      
      const [categories, total] = await Promise.all([
        Category.find(query).sort(sort).skip(skip).limit(limit),
        Category.countDocuments(query)
      ]);
      
      const { sendSuccess } = require('../utils/responseHelper');
      return sendSuccess(res, 'Categories search completed', {
        categories,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      });
    } catch (error) {
      const { sendServerError } = require('../utils/responseHelper');
      return sendServerError(res, 'Failed to search categories');
    }
  }
);

// =============================================================================
// ADMIN ROUTES (Admin authentication required)
// =============================================================================

/**
 * Create new category
 * POST /api/categories
 */
router.post('/', 
  auth,
  authorize(['admin']),
  rateLimiting.moderate, 
  validateRequest(createCategorySchema), 
  AdminController.createCategory
);

/**
 * Update category
 * PUT /api/categories/:categoryId
 */
router.put('/:categoryId', 
  auth,
  authorize(['admin']),
  rateLimiting.moderate, 
  validateRequest(updateCategorySchema), 
  AdminController.updateCategory
);

/**
 * Delete category
 * DELETE /api/categories/:categoryId
 */
router.delete('/:categoryId', 
  auth,
  authorize(['admin']),
  rateLimiting.moderate, 
  AdminController.deleteCategory
);

/**
 * Get all categories for admin (including inactive)
 * GET /api/categories/admin/all
 */
router.get('/admin/all', 
  auth,
  authorize(['admin']),
  rateLimiting.moderate, 
  AdminController.getCategories
);

module.exports = router;
