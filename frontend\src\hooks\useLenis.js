import { useEffect } from 'react';
import Lenis from 'lenis';

let lenisInstance = null;

export const useLenis = () => {
  useEffect(() => {
    // Initialize Lenis only once
    if (!lenisInstance) {
      lenisInstance = new Lenis({
        duration: 1.2,
        easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
        direction: 'vertical',
        gestureDirection: 'vertical',
        smooth: true,
        mouseMultiplier: 1,
        smoothTouch: false,
        touchMultiplier: 2,
        infinite: false,
        autoResize: true,
        syncTouch: false,
        syncTouchLerp: 0.075,
        touchInertiaMultiplier: 35,
        wheelMultiplier: 1,
        normalizeWheel: true,
        prevent: (node) => {
          // Prevent smooth scroll on modal overlays and form inputs
          return (
            node.classList.contains('modal-overlay') ||
            node.classList.contains('dropdown') ||
            node.tagName === 'INPUT' ||
            node.tagName === 'TEXTAREA' ||
            node.tagName === 'SELECT' ||
            node.hasAttribute('data-lenis-prevent')
          );
        }
      });

      // Animation frame function
      function raf(time) {
        lenisInstance.raf(time);
        requestAnimationFrame(raf);
      }
      requestAnimationFrame(raf);
    }

    return () => {
      // Don't destroy on component unmount, keep it global
      // lenisInstance?.destroy();
      // lenisInstance = null;
    };
  }, []);

  return lenisInstance;
};

// Export the instance for external use
export const getLenisInstance = () => lenisInstance;

// Utility function to scroll to top
export const scrollToTop = (options = {}) => {
  if (lenisInstance) {
    lenisInstance.scrollTo(0, {
      duration: 1.2,
      easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
      ...options
    });
  }
};

// Utility function to scroll to element
export const scrollToElement = (target, options = {}) => {
  if (lenisInstance) {
    lenisInstance.scrollTo(target, {
      duration: 1.2,
      easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
      offset: 0,
      ...options
    });
  }
};
