.CustomerAccountSettings-root {
  min-height: 100vh;
  background: var(--pp-clr-bg);
  color: var(--pp-clr-text-main);
  font-family: var(--pp-font-Metro-Sans);
  display: flex;
  flex-direction: column;
  margin-bottom: 1.5rem;
}

.CustomerAccountSettings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--pp-sp-24) var(--pp-sp-32);
  background: var(--pp-clr-primary);
  min-height: 55px;
  border-bottom: 1px solid var(--pp-clr-border-light);
}
.CustomerAccountSettings-logo {
  font-family: var(--pp-font-Playfair-Display);
  font-size: var(--pp-font-heading4);
  font-weight: bold;
}
.CustomerAccountSettings-nav {
  display: flex;
  gap: var(--pp-sp-32);
  font-size: var(--pp-font-base2-font);
}
.CustomerAccountSettings-nav a {
  color: var(--pp-clr-text-main);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s;
}
.CustomerAccountSettings-nav a:hover {
  color: var(--pp-clr-blue-txt);
}
.CustomerAccountSettings-userbtn {
  background: var(--pp-clr-border-light);
  border: none;
  border-radius: var(--pp-bor-rad-round);
  font-size: 32px;
  cursor: pointer;
  padding: var(--pp-sp-4) var(--pp-sp-8);
}

.CustomerAccountSettings-content {
  width: 100%;
  max-width: 1400px;
}

.CustomerAccountSettings-title {
  font-size: var(--pp-font-heading4);
  font-weight: 400;
}

.CustomerAccountSettings-tabs {
  display: flex;
  gap: var(--pp-sp-24);
  border-bottom: 1px solid var(--pp-clr-border-light);
  margin-bottom: var(--pp-sp-24);
}

.CustomerAccountSettings-tab {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  text-decoration: none;
  padding-bottom: var(--pp-sp-12);
  transition: color 0.18s;
}
.CustomerAccountSettings-tab--active {
  color: var(--pp-clr-secondary);
  font-weight: 600;
  border-bottom: 2px solid var(--pp-clr-secondary);
}

.CustomerAccountSettings-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-16);
}

.CustomerAccountSettings-section {
  width: 100%;
}
.CustomerAccountSettings-sectionTitle {
  font-size: var(--pp-font-heading5);
  font-weight: 600;
  margin-bottom: var(--pp-sp-16);
}

.CustomerAccountSettings-grid2 {
  display: grid;
  grid-template-columns: 255px 255px;
  gap: var(--pp-sp-16) var(--pp-sp-32);
  margin-bottom: var(--pp-sp-16);
  height: fit-content;
}

.Customerphotocontainer {
  display: grid;
  grid-template-columns: 600px 300px;
  justify-content: flex-start;
  gap: var(--pp-font-heading4);
}

/* 🔹 Profile Photo Upload */
.customerprofilephoto {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-8);
  padding: 40px 20px;
  text-align: center;
  background: #fff;
  max-width: 350px;
}

.profilephoto-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--pp-clr-text-main);
  margin-bottom: 0.75rem;
}

.profilephoto-preview {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin-bottom: 1rem;
}

.profilephoto-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.CustomerAccountSettings-divider {
  width: 100%;
  height: 1px;
  background: var(--pp-clr-border-light);
}

.CustomerAccountSettings-formActions {
  display: flex;
  justify-content: flex-start;
}
.CustomerAccountSettings-saveBtn {
  margin-right: var(--pp-sp-16);
  min-width: 130px;
}

/* Footer styling */
.CustomerAccountSettings-footer {
  margin-top: auto;
  width: 100%;
  background: var(--pp-clr-bg);
  border-top: 1px solid var(--pp-clr-border-light);
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: var(--pp-sp-32) var(--pp-sp-32);
}

.CustomerAccountSettings-grid2-colspan-password {
  display: flex;

  gap: var(--pp-sp-16) var(--pp-sp-32);
}
.CustomerAccountSettings-root .pp-form-group {
  width: 255px !important;
}
@media (max-width: 992px) {
  .Customerphotocontainer {
    display: flex;
    flex-direction: column-reverse;
  }
}
/* 🔹 Responsive adjustments */
@media (max-width: 700px) {
  .CustomerAccountSettings-header,
  .CustomerAccountSettings-footer {
    padding: var(--pp-sp-12);
  }

  .CustomerAccountSettings-grid2 {
    grid-template-columns: 1fr;
    gap: var(--pp-sp-10);
  }
  .CustomerAccountSettings-grid2-colspan {
    grid-column: 1/2;
  }
  .CustomerAccountSettings-section {
    margin-bottom: 0px;
  }
  .CustomerAccountSettings-grid2 {
    margin-bottom: var(--pp-sp-10);
  }

  .customerprofilephoto {
    margin-top: 1rem;
  }
}
@media (max-width: 600px) {
  .CustomerAccountSettings-tabs {
    border-bottom: none;
  }
}
@media (max-width: 480px) {
  .CustomerAccountSettings-grid2 {
    gap: var(--pp-sp-8);
  }
  .CustomerAccountSettings-grid2-colspan-password {
    grid-template-columns: 1fr;
    display: grid;
    gap: var(--pp-sp-8);
  }
  .CustomerAccountSettings-root .pp-form-group {
    width: 100% !important;
  }
}

/* Selected file info styling */
.selected-file-info {
  font-size: 0.875rem;
  color: var(--pp-clr-text-gray);
  margin-top: 0.5rem;
  font-style: italic;
  max-width: 100%;
  text-align: center;
  /* Prefer 2-line clamp with ellipsis where supported */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2; /* standard property for compatibility */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  /* Fallback to wrapping long tokens */
  overflow-wrap: anywhere;
}

/* Upload button disabled state */
.upload-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Save button disabled state */
.CustomerAccountSettings-saveBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
