

/* Main Container */
.signup-page {
  display: grid;
  grid-template-columns: 1fr 1fr;
  height: 100vh;
  width: 100%;
  margin: 0;
  background: var(--pp-clr-primary);
}

/* Image Section */
.signup-page__image-section {
  background: var(--pp-clr-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  height: 100%;
  width: 100%;
}

.signup-page__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* Form Section */
.signup-page__form-section {
  background: var(--pp-clr-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  width: 100%;
  height: 100%;
}

.signup-page__form-wrapper {
  width: 100%;
  max-width: 550px;
}

.signup-page__title {
  font-size: var(--pp-font-heading4);
  font-weight: 600;
  color: var(--pp-clr-text-main);
  margin-bottom: var(--pp-sp-10);
  text-align: left;
}

/* Form Layout */
.signup-page__form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--pp-sp-16);
  margin-bottom: var(--pp-sp-10);
}

.signup-page__form-row .pp-form-group {
  margin-bottom: 0;
}

/* Password Fields Container */
.signup-page__password-container {
  position: relative;
}

.signup-page__password-toggle {
  position: absolute;
  right: var(--pp-sp-16);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--pp-sp-4);
  color: var(--pp-clr-text-gray);
  transition: color 300ms ease;
}

.signup-page__password-toggle:hover {
  color: var(--pp-clr-text-main);
}

.signup-page__password-toggle:focus {
  outline: none;
  color: var(--pp-clr-secondary);
}

/* Bottom Link */
.signup-page__bottom-link {
  margin-top: var(--pp-sp-20);
}

/* =========================
   🧩 Role Selection Cards
   ========================= */
.pp-role-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--pp-sp-16);
}

.pp-role-option {
  display: block;
}

.pp-visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.pp-role-card {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-8);
  padding: 12px 24px;
  border-radius: var(--pp-bor-rad-12);
  border: 1px solid var(--pp-clr-border);
  background: var(--pp-clr-primary);
  box-shadow: var(--pp-shdw-sm);
  transition: transform 300ms ease, box-shadow 300ms ease,
    border-color 300ms ease;
}

/* Focus state using adjacent sibling for broader support */
.pp-role-option input:focus-visible + .pp-role-card {
  box-shadow: var(--pp-shdw-md);
  border-color: var(--pp-clr-secondary);
}

.pp-role-option:hover .pp-role-card {
  transform: translateY(-2px);
  box-shadow: var(--pp-shdw-md);
}

.pp-role-option:active .pp-role-card {
  transform: translateY(0);
}

.pp-role-option input:checked + .pp-role-card {
 
  box-shadow: var(--pp-shdw-md);
  background-color: #F4F4F4;
}

.pp-role-card__title {
  font-size: var(--pp-font-heading6);
  font-weight: 600;
  color: var(--pp-clr-text-main);
}

.pp-role-card__desc {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
}

/* =========================
   📱 Responsive Design
   ========================= */

/* Tablet & Below (≤1024px) */
@media (max-width: 1024px) {
  .signup-page {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }

  .signup-page__image-section {
    height: var(--pp-auth-image-height-tablet);
    order: 1;
  }

  .signup-page__form-section {
    order: 2;
    align-items: flex-start;
    padding: var(--pp-sp-60);
  }
}

/* Mobile (≤768px) */
@media (max-width: 768px) {
  .signup-page__image-section {
    height: var(--pp-auth-image-height-mobile);
  }

  .signup-page__form-section {
    padding: var(--pp-sp-40);
  }

  .signup-page__title {
    font-size: var(--pp-font-heading5);
    margin-bottom: var(--pp-sp-10);
  }


}
@media (max-width: 600px) {
  .signup-page__form-row {
    grid-template-columns: 1fr;
    gap: var(--pp-sp-20);
  }
  .pp-role-card{
    padding: 12px;
    height: 100%;
  }
}
/* Small Mobile (≤414px) */
@media (max-width: 414px) {
  .signup-page__image-section {
    display: none;
  }

  .signup-page__form-section {
    padding: var(--pp-sp-24);
  }

  .signup-page__title {
    font-size: var(--pp-font-heading6);
    margin-bottom: var(--pp-sp-10);
  }


  
.signup-page__form-row {
 
  gap: var(--pp-sp-10);

}
}
