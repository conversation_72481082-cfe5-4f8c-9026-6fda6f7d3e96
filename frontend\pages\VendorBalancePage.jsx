import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { FiCheckCircle, FiRefreshCw } from "react-icons/fi";
import { FaUniversity, FaPaypal } from "react-icons/fa";
import { useAuth } from "../redux/useAuth";
import { vendorAPI } from "../redux/apiUtils";
import { MdClose } from "react-icons/md"; // Import Close icon
import "../styles/VendorBalancePage.css";
import Ticicon from "../src/assets/images/Ticicon.svg"


const initialAccounts = [
  {
    id: 1,
    type: "bank",
    icon: <FaUniversity style={{ fontSize: "24px" }} />,
    name: "Checking",
    details: "Checking ••••5155",
    tier: "Primary",
    verified: true,
  },
  {
    id: 2,
    type: "paypal",
    icon: <FaPaypal style={{ fontSize: "24px", color: "#0c6cf2" }} />,
    name: "PayPal",
    details: "PayPal (e•••••g*@*.com )",
    tier: "Secondary",
    verified: true,
  },
];

export default function AccountsPage() {
  const dispatch = useDispatch();
  const { user, isAuthenticated } = useAuth();

  // State management
  const [accounts, setAccounts] = useState(initialAccounts);
  const [primaryId, setPrimaryId] = useState(1);
  const [balance, setBalance] = useState({ available: [], pending: [] });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch balance data from API
  const fetchBalance = async () => {
    try {
      setError(null);
      const response = await vendorAPI.getBalance();
      if (response.data?.success) {
        setBalance(response.data.data);
      }
    } catch (err) {
      console.error('Failed to fetch balance:', err);
      setError(err.response?.data?.message || 'Failed to load balance');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Refresh balance
  const handleRefreshBalance = async () => {
    setRefreshing(true);
    await fetchBalance();
  };

  // Load balance on component mount
  useEffect(() => {
    if (isAuthenticated && user?.role === 'vendor') {
      fetchBalance();
    } else {
      setLoading(false);
    }
  }, [isAuthenticated, user]);
  const [showModal, setShowModal] = useState(false);

  const handleUnlink = (id) => {
    setAccounts((accs) => accs.filter((acc) => acc.id !== id));
  };

  const handleMakePrimary = (id) => {
    setPrimaryId(id);
    setAccounts((accs) =>
      accs.map((acc) =>
        acc.id === id
          ? { ...acc, tier: "Primary" }
          : { ...acc, tier: "Secondary" }
      )
    );
  };

  // Calculate total available balance
  const totalAvailable = balance.available?.reduce((sum, b) => sum + b.amount, 0) || 0;
  const totalPending = balance.pending?.reduce((sum, b) => sum + b.amount, 0) || 0;

  // Show loading state
  if (loading) {
    return (
      <div className="acc-root">
        <div className="acc-balance-bloc">
          <div className="acc-balance-label">Loading Balance...</div>
          <div className="acc-balance-value">--</div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="acc-root">
        <div className="acc-balance-bloc">
          <div className="acc-balance-label">Balance</div>
          <div className="acc-balance-value">Error</div>
          <div style={{ color: 'red', fontSize: '14px', marginTop: '8px' }}>
            {error}
          </div>
          <button
            className="pp-btn-primary acc-main-btn"
            onClick={handleRefreshBalance}
            disabled={refreshing}
          >
            {refreshing ? 'Refreshing...' : 'Retry'}
          </button>
        </div>
      </div>
    );
  }

  const handleAddAccountType = (type) => {
    setShowModal(false);
    window.alert(
      `Selected account type: ${
        type === "bank" ? "Checking Account" : "PayPal"
      }`
    );
  };

  return (
    <div className="acc-root">
      <div className="acc-balance-bloc">
        <div className="acc-balance-label">
          Available Balance
          <button
            onClick={handleRefreshBalance}
            disabled={refreshing}
            style={{
              background: 'none',
              border: 'none',
              marginLeft: '8px',
              cursor: refreshing ? 'not-allowed' : 'pointer',
              opacity: refreshing ? 0.6 : 1
            }}
            title="Refresh balance"
          >
            <FiRefreshCw className={refreshing ? 'rotating' : ''} />
          </button>
        </div>
        <div className="acc-balance-value">
          ${totalAvailable.toFixed(2)}
        </div>
        {totalPending > 0 && (
          <div style={{ fontSize: '14px', color: '#666', marginTop: '4px' }}>
            Pending: ${totalPending.toFixed(2)}
          </div>
        )}
        <button
          className="pp-btn-primary acc-main-btn"
          onClick={() => window.alert("Transfer Funds feature coming soon!")}
          disabled={totalAvailable <= 0}
        >
          Transfer Funds
        </button>
      </div>

      <div className="acc-linked-bloc">
        <div className="acc-accounts-title">Linked Accounts</div>
        <div className="acc-accounts-tablewrap">
          <div className="acc-accounts-table">
            {accounts.map((acc, i) => (
              <div
                key={acc.id}
                className={`acc-table-row${
                  i === accounts.length - 1 ? " last" : ""
                }`}
              >
                <div className="acc-col acc-icon">{acc.icon}</div>
                <div className="acc-col acc-account">
                  <div className="acc-account-name">{acc.name}</div>
                  <div className="acc-account-details">{acc.details}</div>
                </div>
                <div className="acc-col acc-type">{acc.tier}</div>
             <div className="flex gap-3 items-center justify-end">
             <div className="acc-col acc-status">
                  {acc.verified && (
                    <>
                    <img src={Ticicon} alt="icon" className="acc-verified-icon"  /> Verified
                   
                    </>
                  )}
                </div>
                <div className="acc-col acc-actions">
                  {acc.tier === "Secondary" && (
                    <button
                      className="pp-btn-secondary acc-action-btn"
                      type="button"
                      onClick={() => handleMakePrimary(acc.id)}
                    >
                      Make Primary
                    </button>
                  )}
                  <button
                    className="pp-btn-secondary acc-action-btn"
                    type="button"
                    onClick={() => handleUnlink(acc.id)}
                  >
                    Unlink
                  </button>
                </div>
             </div>
              </div>
            ))}
          </div>
        </div>
        <button
          className="pp-btn-primary acc-main-btn"
          onClick={() => setShowModal(true)}
        >
          Add New Account
        </button>
      </div>

      {showModal && (
        <div className="acc-modal-backdrop" onClick={() => setShowModal(false)}>
          <div className="acc-modal" onClick={(e) => e.stopPropagation()}>
            <div className="acc-modal-header">
              <div className="acc-modal-title">New Account</div>
              <button
                className="acc-modal-close-btn"
                onClick={() => setShowModal(false)}
                aria-label="Close modal"
                type="button"
              >
                <MdClose size={24} />
              </button>
            </div>
            <div className="acc-modal-subtitle">Choose Account Type</div>
            <div className="acc-modal-options">
              <button
                className="acc-modal-option"
                onClick={() => handleAddAccountType("bank")}
                type="button"
              >
                <FaUniversity className="acc-modal-option-icon" />
                <div>
                  <div className="acc-modal-option-title">Checking Account</div>
                  <div className="acc-modal-option-desc">
                    Typically 3-5 business day transfers. No fee.
                  </div>
                </div>
              </button>
              <button
                className="acc-modal-option"
                onClick={() => handleAddAccountType("paypal")}
                type="button"
              >
                <FaPaypal
                  className="acc-modal-option-icon"
                  style={{ color: "#0c6cf2" }}
                />
                <div>
                  <div className="acc-modal-option-title">PayPal</div>
                  <div className="acc-modal-option-desc">
                    Instant transfers. 3% fee.
                  </div>
                </div>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
