import React from "react";
import CustomSelect from "../../components/CustomeSelect";

export default function AvailabilityInputs({ value = {}, onChange }) {
  const handleChange = (field, val) => {
    onChange({ ...value, [field]: val });
  };

  const handleToggle = (field) => {
    onChange({ ...value, [field]: !value[field] });
  };

  const handleDayToggle = (day) => {
    const currentDays = value.availableDays || [];
    const newDays = currentDays.includes(day)
      ? currentDays.filter((d) => d !== day)
      : [...currentDays, day];
    onChange({ ...value, availableDays: newDays });
  };

  const daysOfWeek = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ];

  return (
    <div className="ai-availability-inputs">
      <div className="ai-form-group">
        <label className="ai-form-label">Maximum Simultaneous Bookings</label>
        <p className="ai-description">
          How many of these services can be booked at the same time or on
          different events?
        </p>
        <input
          type="number"
          className="ai-form-input"
          min="1"
          placeholder=" "
          value={value.maxSimultaneous || ""}
          onChange={(e) => handleChange("maxSimultaneous", e.target.value)}
        />
      </div>

      <div className="ai-form-group">
        <label className="ai-form-label">Minimum Booking Lead Time</label>
        <p className="ai-description">
          Minimum amount of time a customer can book into the future.
        </p>
        <div className="ai-input-with-unit">
          <input
            type="number"
            className="ai-form-input"
            min="0"
            value={value.minLeadTime || "0"}
            onChange={(e) => handleChange("minLeadTime", e.target.value)}
          />
          <CustomSelect
            className="ai-unit-select"
            value={value.minLeadTimeUnit || "days"}
            onChange={(v) => handleChange("minLeadTimeUnit", v)}
            options={[
              { label: "Hour(s)", value: "hours" },
              { label: "Day(s)", value: "days" },
              { label: "Week(s)", value: "weeks" },
              { label: "Month(s)", value: "months" },
            ]}
          />
        </div>
        <label className="ai-checkbox-wrapper">
          <input
            type="checkbox"
            className="ai-checkbox"
            checked={value.noMinLeadTime || false}
            onChange={() => handleToggle("noMinLeadTime")}
          />
          <span className="ai-checkbox-label">No minimum lead time</span>
        </label>
      </div>

      <div className="ai-form-group">
        <label className="ai-form-label">Maximum Booking Lead Time</label>
        <p className="ai-description">
          Maximum amount of time a customer can book into the future.
        </p>
        <div className="ai-input-with-unit">
          <input
            type="number"
            className="ai-form-input"
            min="0"
            value={value.maxLeadTime || "0"}
            onChange={(e) => handleChange("maxLeadTime", e.target.value)}
          />
          <CustomSelect
            className="ai-unit-select"
            value={value.maxLeadTimeUnit || "months"}
            onChange={(v) => handleChange("maxLeadTimeUnit", v)}
            options={[
              { label: "Hour(s)", value: "hours" },
              { label: "Day(s)", value: "days" },
              { label: "Week(s)", value: "weeks" },
              { label: "Month(s)", value: "months" },
            ]}
          />
        </div>
        <label className="ai-checkbox-wrapper">
          <input
            type="checkbox"
            className="ai-checkbox"
            checked={value.noMaxLeadTime || false}
            onChange={() => handleToggle("noMaxLeadTime")}
          />
          <span className="ai-checkbox-label">No maximum lead time</span>
        </label>
      </div>

      <div className="ai-form-group">
        <label className="ai-form-label">Minimum Time Between Bookings</label>
        <p className="ai-description">
          How much time would you like between bookings?
        </p>
        <div className="ai-duration-inputs-row">
          <div className="ai-input-group">
            <input
              type="number"
              className="ai-form-input"
              min="0"
              value={value.minTimeBetweenHours || "0"}
              onChange={(e) =>
                handleChange("minTimeBetweenHours", e.target.value)
              }
            />
            <span className="ai-unit">Hour(s)</span>
          </div>
          <div className="ai-input-group">
            <input
              type="number"
              className="ai-form-input"
              min="0"
              max="59"
              value={value.minTimeBetweenMinutes || "30"}
              onChange={(e) =>
                handleChange("minTimeBetweenMinutes", e.target.value)
              }
            />
            <span className="ai-unit">Minutes</span>
          </div>
        </div>
      </div>

      <div className="ai-form-group">
        <label className="ai-form-label">Available Hours</label>
        <div className="ai-time-inputs">
          <div className="ai-time-input">
            <label className="ai-time-label">From</label>
            <input
              type="time"
              className="ai-form-input"
              value={value.availableFrom || "08:00"}
              onChange={(e) => handleChange("availableFrom", e.target.value)}
            />
          </div>
          <div className="ai-time-input">
            <label className="ai-time-label">To</label>
            <input
              type="time"
              className="ai-form-input"
              value={value.availableTo || "22:00"}
              onChange={(e) => handleChange("availableTo", e.target.value)}
            />
          </div>
        </div>
      </div>

      <div className="ai-form-group">
        <label className="ai-form-label">Available Days</label>
        <div className="ai-days-grid">
          {daysOfWeek.map((day) => (
            <label key={day} className="ai-day-checkbox">
              <input
                type="checkbox"
                className="ai-checkbox"
                checked={(value.availableDays || []).includes(day)}
                onChange={() => handleDayToggle(day)}
              />
              <span className="ai-day-label">{day}</span>
            </label>
          ))}
        </div>
      </div>
    </div>
  );
}
