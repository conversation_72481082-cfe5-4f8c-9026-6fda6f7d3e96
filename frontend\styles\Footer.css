.c-footer {
  background-color: var(--pp-clr-secondary);
  color: var(--pp-clr-primary);
  padding: 5rem 0;
}

.c-footer__top {
  display: grid;
  grid-template-columns: 1fr 1.2fr auto;
  gap: var(--pp-sp-32);
  align-items: start;
}

.c-footer__brand {
  display: grid;
  gap: var(--pp-sp-16);
}

.c-footer__logo {
  display: block;
  width: var(--pp-logo-width);
  height: auto;
  aspect-ratio: 1/1;
}

.c-footer__tagline {
  margin: 0;
  color: var(--pp-clr-primary);
  font-family: var(--pp-font-Metro-Sans);
  font-size: var(--pp-font-small-font);
  font-weight: 300;
}

.c-footer__cta {
  display: inline-flex;
  align-items: center;
  gap: var(--pp-sp-8);
  color: var(--pp-clr-primary);
  text-decoration: none;
  font-family: var(--pp-font-Metro-Sans);
  font-size: var(--pp-font-small-font);
  border-radius: var(--pp-bor-rad-24);
  box-shadow: var(--pp-shdw-sm);
  transition: transform var(--pp-trans-duration-fast) var(--pp-trans-ease);
}

.c-footer__cta:hover {
  transform: scale(1.02);
}

.c-footer__cta-icon {
  font-size: var(--pp-font-heading6);
}

.c-footer__nav {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: var(--pp-sp-24);
}

.c-footer__col {
  display: grid;
  gap: var(--pp-sp-12);
}

.c-footer__heading {
  margin: 0;
  font-family: var(--pp-font-Metro-Sans);
  font-size: var(--pp-font-heading6);
  font-weight: 600;
}

.c-footer__links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  gap: var(--pp-sp-8);
  font-weight: 300;
}

.c-footer__link {
  color: var(--pp-clr-primary);
  text-decoration: none;
  font-family: var(--pp-font-Metro-Sans);
  font-size: var(--pp-font-small-font);
}

.c-footer__link:hover {
  text-decoration: underline;
}

.c-footer__social {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-12);
}

.c-footer__social-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: var(--pp-sp-40);
  height: var(--pp-sp-40);
  color: var(--pp-clr-secondary);
  background-color: var(--pp-clr-primary);
  border-radius: var(--pp-bor-rad-round);
  text-decoration: none;
  font-size: var(--pp-font-heading6);
  transition: transform var(--pp-trans-duration-fast) var(--pp-trans-ease);
}

.c-footer__social-btn:hover {
  transform: scale(1.05);
}

.c-footer__divider {
  margin: 100px auto 40px auto;
  height: 1px;
  background-color: var(--pp-clr-text-gray);
}

.c-footer__bottom {
  display: flex;
  align-items: center;

  gap: var(--pp-sp-16);
  justify-content: center;
}

.c-footer__legal {
  display: flex;
  align-items: center;
  gap: 50px;
  font-family: var(--pp-font-Metro-Sans);
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
}

.c-footer__legal-link {
  color: var(--pp-clr-text-gray);
  text-decoration: none;
}

.c-footer__legal-link:hover {
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .c-footer {
    padding: 3rem 0;
  }

  .c-footer__divider {
    margin: 80px auto 32px auto;
  }
  .c-footer__top {
    gap: 24px;
    grid-template-columns: 1fr 1.2fr;
  }
}

@media (max-width: 768px) {
  .c-footer {
    padding: 3rem 0;
  }

  .c-footer__top {
    gap: 24px;
    grid-template-columns: 1fr 1.2fr;
  }

  .c-footer__nav {
    gap: 20px;
  }

  .c-footer__divider {
    margin: 60px auto 24px auto;
  }
}

@media (max-width: 640px) {
  .c-footer__bottom {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--pp-sp-24);
    flex-direction: column-reverse;
  }

  .c-footer__divider {
    margin: 40px auto 24px auto;
  }

  .c-footer__legal {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--pp-sp-12);
  }
}

@media (max-width: 414px) {
  .c-footer {
    padding: 3rem 0;
  }

  .c-footer__top {
    gap: var(--pp-sp-20);
    grid-template-columns: 1fr;
  }

  .c-footer__nav {
    gap: 16px;
  }

  .c-footer__social {
    gap: 8px;
  }

  .c-footer__social-btn {
    width: 32px;
    height: 32px;
  }
}
@media (max-width: 768px) {
  body.pp-chat-active .c-footer {
    display: none;
  }
}
