import React, { useState, useRef } from 'react';
import { FiUpload, FiX, FiFile, FiImage, FiCheck, FiAlertCircle } from 'react-icons/fi';
import { toast } from 'react-toastify';
import { api } from '../redux/apiUtils';

/**
 * File Upload Component for document uploads
 * Supports image preview, validation, and progress tracking
 */
const FileUpload = ({
  uploadType = 'documents', // 'government-id', 'selfie-verification', 'bank-statement', 'documents'
  accept = 'image/*,application/pdf',
  maxSize = 10 * 1024 * 1024, // 10MB default
  onUploadSuccess,
  onUploadError,
  label = 'Upload File',
  description = '',
  required = false,
  disabled = false,
  className = ''
}) => {
  const [file, setFile] = useState(null);
  const [preview, setPreview] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [uploaded, setUploaded] = useState(false);
  const [error, setError] = useState('');
  const fileInputRef = useRef(null);

  const validateFile = (selectedFile) => {
    if (!selectedFile) return false;

    // Check file size
    if (selectedFile.size > maxSize) {
      const maxSizeMB = Math.round(maxSize / (1024 * 1024));
      setError(`File size must be less than ${maxSizeMB}MB`);
      return false;
    }

    // Check file type based on upload type
    const allowedTypes = {
      'government-id': ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'],
      'selfie-verification': ['image/jpeg', 'image/jpg', 'image/png'],
      'bank-statement': ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'],
      'documents': ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
    };

    const allowed = allowedTypes[uploadType] || allowedTypes.documents;
    if (!allowed.includes(selectedFile.type)) {
      setError('Invalid file type. Please select a valid document or image file.');
      return false;
    }

    setError('');
    return true;
  };

  const handleFileSelect = (event) => {
    const selectedFile = event.target.files[0];
    if (!selectedFile) return;

    if (!validateFile(selectedFile)) {
      return;
    }

    setFile(selectedFile);
    setUploaded(false);

    // Create preview for images
    if (selectedFile.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => setPreview(e.target.result);
      reader.readAsDataURL(selectedFile);
    } else {
      setPreview(null);
    }
  };

  const handleUpload = async () => {
    if (!file) return;

    setUploading(true);
    setError('');

    try {
      const formData = new FormData();
      formData.append(uploadType === 'government-id' ? 'governmentId' : 
                     uploadType === 'selfie-verification' ? 'selfie' :
                     uploadType === 'bank-statement' ? 'bankStatement' : 'documents', file);

      const endpoint = `/vendors/upload/${uploadType}`;
      const response = await api.upload(endpoint, formData);

      setUploaded(true);
      toast.success('File uploaded successfully!');
      
      if (onUploadSuccess) {
        onUploadSuccess(response.document || response.documents[0]);
      }
    } catch (error) {
      const errorMessage = error.message || 'Upload failed. Please try again.';
      setError(errorMessage);
      toast.error(errorMessage);
      
      if (onUploadError) {
        onUploadError(error);
      }
    } finally {
      setUploading(false);
    }
  };

  const handleRemove = () => {
    setFile(null);
    setPreview(null);
    setUploaded(false);
    setError('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const getFileIcon = () => {
    if (!file) return <FiUpload />;
    if (file.type.startsWith('image/')) return <FiImage />;
    return <FiFile />;
  };

  return (
    <div className={`file-upload-container ${className}`}>
      <div className="file-upload-label">
        {label} {required && <span className="required">*</span>}
        {description && <p className="file-upload-description">{description}</p>}
      </div>

      <div className={`file-upload-area ${error ? 'error' : ''} ${uploaded ? 'success' : ''}`}>
        {!file ? (
          <div 
            className="file-upload-dropzone"
            onClick={() => !disabled && fileInputRef.current?.click()}
          >
            <FiUpload size={24} />
            <p>Click to select file or drag and drop</p>
            <p className="file-upload-hint">
              {uploadType === 'government-id' && 'Upload your government ID (passport, driver\'s license, or state ID)'}
              {uploadType === 'selfie-verification' && 'Upload a clear photo of yourself for verification'}
              {uploadType === 'bank-statement' && 'Upload a recent bank statement (optional)'}
              {uploadType === 'documents' && 'Upload supporting documents'}
            </p>
          </div>
        ) : (
          <div className="file-upload-preview">
            {preview ? (
              <img src={preview} alt="Preview" className="file-preview-image" />
            ) : (
              <div className="file-preview-icon">
                {getFileIcon()}
                <span>{file.name}</span>
              </div>
            )}
            
            <div className="file-upload-actions">
              {!uploaded && !uploading && (
                <button 
                  type="button"
                  onClick={handleUpload}
                  className="pp-btn-primary file-upload-btn"
                  disabled={disabled}
                >
                  <FiUpload /> Upload
                </button>
              )}
              
              {uploading && (
                <div className="file-upload-progress">
                  <div className="spinner"></div>
                  <span>Uploading...</span>
                </div>
              )}
              
              {uploaded && (
                <div className="file-upload-success">
                  <FiCheck color="green" />
                  <span>Uploaded successfully</span>
                </div>
              )}
              
              <button 
                type="button"
                onClick={handleRemove}
                className="file-upload-remove"
                disabled={uploading}
              >
                <FiX />
              </button>
            </div>
          </div>
        )}
      </div>

      {error && (
        <div className="file-upload-error">
          <FiAlertCircle />
          <span>{error}</span>
        </div>
      )}

      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleFileSelect}
        style={{ display: 'none' }}
        disabled={disabled}
      />
    </div>
  );
};

export default FileUpload;
