import React from "react";
import { Link } from "react-router-dom";
import "../../styles/CustomerEventCard.css";
import { RiDeleteBin6Line } from "react-icons/ri";
function getMonthDay(dateStr) {
  if (!dateStr) return ["", ""];
  const date = new Date(dateStr);
  return [date.toLocaleString("default", { month: "short" }), String(date.getDate()).padStart(2, '0')];
}

const EventCard = ({ event, onServiceClick }) => {
  const [month, day] = getMonthDay(event.date);

  // Component to render multiple images with overlap effect
  const ImageStack = ({ images }) => {
    if (!images || images.length === 0) return null;

    if (images.length === 1) {
      return (
        <div className="pp-event-service-img-one">
          <img
            style={{objectFit: "cover" , height: "100%",width: "100%"}}
            className=""
            src={images[0]}
            alt=""
            loading="lazy"
            decoding="async"
          />

        </div>
      );
    }

    if (images.length === 2) {
      return (
        <div className="pp-event-service-img">
          <img
            className="pp-event-service-img-inner"
            src={images[0]}
            alt=""
            loading="lazy"
            decoding="async"
          />
          <img
            className="pp-event-service-img-inner"
            src={images[1]}
            alt=""
            loading="lazy"
            decoding="async"
          />
        </div>
      );
    }



    return (
      <div className="pp-event-service-img-three">
        <div >
          <img
            style={{objectFit: "cover" , height: "100%",width: "100%"}}
            src={images[0]}
            alt=""
            loading="lazy"
            decoding="async"
          />
          </div>
          <div style={{display: "grid" , gap: "3px"}}>
          <img
            className=""
            src={images[1]}
            style={{objectFit: "cover" , height: "100%",width: "100%" }}
            alt=""
            loading="lazy"
            decoding="async"
          />
        
        
          <img
            className=""
            src={images[2]}
            style={{objectFit: "cover" , height: "100%",width: "100%"}}
            alt=""
            loading="lazy"
            decoding="async"
          />
        </div>

      </div>
    );
  };

  return (
    <div className="pp-event-row">
      {/* Left: Event Title & Date */}
      <div className="pp-event-row-left-main">
        <div className="pp-event-row-left">
          {event.title && (
            <>
              <div className="pp-event-row-title">{event.title}</div>
              <Link to={event.editUrl} className="pp-event-row-edit">Edit Event</Link>
            </>
          )}
        </div>
      </div>
      <div className="pp-event-row-right-main">
        {/* Right: Services or Empty */}
        <div className="pp-event-row-content">
          {event.services && event.services.length > 0 ? (
            event.services.map((svc, i) => (
              <React.Fragment key={i}>
                <div
                  className={`pp-event-service-row${svc.highlight ? " pp-event-service-row--highlight" : ""}`}
                  onClick={() => onServiceClick && onServiceClick(svc, event)}
                  style={{
                    cursor: 'pointer',
                    transition: 'background-color 0.2s ease',
                    ':hover': {
                      backgroundColor: 'rgba(0, 0, 0, 0.02)'
                    }
                  }}
                >
                  {/* Date */}
                  {event.date && (
                    <div className="pp-event-row-dateblock">
                      <span className="pp-event-row-month">{month}</span>
                      <span className="pp-event-row-day">{day}</span>
                    </div>
                  )}

                  {/* Image Stack */}
                  <ImageStack images={svc.images} />

                  {/* Details */}
                  <div className="pp-event-service-details-main">
                    <div className="pp-event-service-title">{svc.title}</div>
                    <div className="pp-event-service-details">
                      <div className="pp-event-service-meta">
                        <span>{svc.dateDetails}</span>
                        <span>{svc.time}</span>
                      </div>
                      <div className="pp-event-service-vendor-main">
                        <div className="pp-event-service-vendor">{svc.vendor}</div>
                        <div className="pp-event-service-price">{svc.price}</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Divider between services */}
                {i < event.services.length - 1 && (
                  <div className="pp-event-service-divider" />
                )}
              </React.Fragment>
            ))
          ) : (
            <div className="pp-event-service-row">
            <div className="pp-event-service-row-inner" >
              <div>
              <span className="pp-event-row-noservice">{event.noServicesText}</span>
              
              <Link to={event.bookNowUrl || "#"} className="pp-event-row-booknow">
                Book now
              </Link>
              </div>
              <div>
              <RiDeleteBin6Line/>
              </div>
              
             
             
            </div>
            </div>
            
            
            
          )}
        </div>
      </div>
    </div>
  );
};

export default EventCard;
