import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { FiInfo, FiCheckCircle, FiUpload } from "react-icons/fi";

// Validation utility functions
const validateEmail = (email) => {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(String(email).toLowerCase());
};

const validatePhone = (phone) => {
  const re = /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/;
  return re.test(phone);
};

const validateSSN = (ssn) => {
  const re = /^\d{9}$/;
  return re.test(ssn);
};

const validateAge = (dateOfBirth) => {
  if (!dateOfBirth || !dateOfBirth.day || !dateOfBirth.month || !dateOfBirth.year) return false;
  const today = new Date();
  const birthDate = new Date(dateOfBirth.year, dateOfBirth.month - 1, dateOfBirth.day);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age >= 18;
};

const validatePostalCode = (postalCode) => {
  const re = /^\d{5}(-\d{4})?$/;
  return re.test(postalCode);
};

import {
  FaUserTie,
  FaUniversity,
  FaFileUpload,
  FaCheck,
} from "react-icons/fa";
import { useAuth } from "../redux/useAuth";
import {
  startVendorOnboarding,
  getOnboardingStatus,
  forceRefreshStatus,
  selectOnboardingLoading,
  selectOnboardingError,
  selectOnboardingStatus,
  clearOnboardingError
} from "../redux/vendorSlice";
import { vendorApi } from "../redux/apiUtils";
import USStateSelect from "../components/USStateSelect";
import FileUpload from "../components/FileUpload";
import "../styles/VendorOnboarding.css";
import "../styles/FileUpload.css";
import CustomSelect from "../components/CustomeSelect";
import CustomDatePicker from "../components/customeCalender";

// Helper functions for verification status handling
const getVerificationStatusClass = (onboardingStatus) => {
  if (onboardingStatus.requirements?.disabled_reason) {
    return 'error'; // Account suspended/failed
  } else if (onboardingStatus.requirements?.past_due?.length > 0) {
    return 'error'; // Past due requirements
  } else if (onboardingStatus.requirements?.currently_due?.length > 0) {
    return 'warning'; // Additional verification needed
  } else if (onboardingStatus.chargesEnabled && onboardingStatus.payoutsEnabled) {
    return 'success'; // Fully verified
  } else if (onboardingStatus.onboardingCompleted) {
    return 'success'; // Onboarding completed, pending verification
  } else {
    return 'success'; // Default success
  }
};

const getVerificationMessage = (onboardingStatus) => {
  if (onboardingStatus.requirements?.disabled_reason) {
    return '⚠️ Account verification failed. Redirecting to dashboard to resolve issues...';
  } else if (onboardingStatus.requirements?.past_due?.length > 0) {
    return '⚠️ Some verification items are past due. Redirecting to dashboard to resolve...';
  } else if (onboardingStatus.requirements?.currently_due?.length > 0) {
    return '⚠️ Additional verification required. Redirecting to dashboard...';
  } else if (onboardingStatus.chargesEnabled && onboardingStatus.payoutsEnabled) {
    return '🎉 Onboarding completed successfully! Account fully verified. Redirecting to your vendor dashboard...';
  } else if (onboardingStatus.onboardingCompleted) {
    return '🎉 Onboarding completed! Stripe is reviewing your account. This usually takes a few minutes. We\'ll check automatically and redirect you once approved...';
  } else {
    return '🎉 Onboarding completed successfully! Redirecting to your vendor dashboard...';
  }
};

const getVerificationDetails = (onboardingStatus) => {
  if (onboardingStatus.requirements?.disabled_reason) {
    return (
      <div style={{ marginTop: '0.5rem', fontSize: '0.9rem', color: '#d32f2f' }}>
        <strong>Issue:</strong> {onboardingStatus.requirements.disabled_reason}
        <br />
        <strong>Action Required:</strong> Please check your dashboard for specific requirements to resolve this issue.
      </div>
    );
  } else if (onboardingStatus.requirements?.past_due?.length > 0) {
    return (
      <div style={{ marginTop: '0.5rem', fontSize: '0.9rem', color: '#d32f2f' }}>
        <strong>Past Due Items:</strong> {onboardingStatus.requirements.past_due.join(', ')}
        <br />
        <strong>Action Required:</strong> Please submit the required information immediately to avoid account restrictions.
      </div>
    );
  } else if (onboardingStatus.requirements?.currently_due?.length > 0) {
    return (
      <div style={{ marginTop: '0.5rem', fontSize: '0.9rem', color: '#ed6c02' }}>
        <strong>Required Items:</strong> {onboardingStatus.requirements.currently_due.join(', ')}
        <br />
        <strong>Action Required:</strong> Please provide the additional information to complete verification.
      </div>
    );
  } else if (onboardingStatus.requirements?.pending_verification?.length > 0) {
    return (
      <div style={{ marginTop: '0.5rem', fontSize: '0.9rem', color: '#666' }}>
        <strong>Pending Verification:</strong> {onboardingStatus.requirements.pending_verification.join(', ')}
        <br />
        <strong>Note:</strong> Some verification items are still being processed by Stripe. You can access your dashboard while verification completes.
      </div>
    );
  }
  return null;
};

// Company onboarding removed; only individual is supported
const businessTypes = [
  { label: "Individual", value: "individual", icon: <FaUserTie /> },
];

const countries = [
  { code: "US", label: "United States", flag: "🇺🇸" },
  { code: "CA", label: "Canada", flag: "🇨🇦" },
  { code: "GB", label: "United Kingdom", flag: "🇬🇧" },
  // Add more countries as needed
];

function Stepper({ currentStep, steps }) {
  return (
    <div className="vo-stepper">
      {steps.map((step, i) => {
        const isActive = i + 1 === currentStep;
        const isComplete = i + 1 < currentStep;
        return (
          <div
            key={step}
            className={`vo-step ${isActive ? "active" : ""} ${
              isComplete ? "complete" : ""
            }`}
            aria-current={isActive ? "step" : undefined}
          >
            <div className="vo-step-indicator">
              {isComplete ? <FiCheckCircle size={20} /> : i + 1}
            </div>
            <div className="vo-step-label">{step}</div>
            {i < steps.length - 1 && <div className="vo-step-line" />}
          </div>
        );
      })}
    </div>
  );
}

export default function VendorOnboarding() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { user, isAuthenticated } = useAuth();

  const onboardingLoading = useSelector(selectOnboardingLoading);
  const onboardingError = useSelector(selectOnboardingError);
  const onboardingStatus = useSelector(selectOnboardingStatus);

  // Simplified steps for individual-only onboarding
  const steps = [
    "Personal Information",
    "Individual Information",
    "Bank Account Information",
    "Compliance & Agreements",
    "Document Upload",
  ];

  const [currentStep, setCurrentStep] = useState(1);

  // Business type selection
  const [businessType, setBusinessType] = useState("individual");

  // Step 1: Personal Information (for individuals)
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [dateOfBirth, setDateOfBirth] = useState({
    day: "",
    month: "",
    year: ""
  });
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [ssn, setSsn] = useState("");
  const [personalWebsite, setPersonalWebsite] = useState("");
  const [industry, setIndustry] = useState("");
  const [website, setWebsite] = useState("");
  const [personalAddress, setPersonalAddress] = useState({
    line1: "",
    city: "",
    state: "",
    postalCode: "",
    country: "US"
  });

  // Step 2: Identification Documents
  const [governmentId, setGovernmentId] = useState(null);
  const [selfieVerification, setSelfieVerification] = useState(null);
  
  // Document upload states
  const [uploadingDocuments, setUploadingDocuments] = useState(false);
  const [uploadedDocuments, setUploadedDocuments] = useState({
    governmentId: null,
    selfieVerification: null,
    bankStatement: null,
    representatives: []
  });

  // Company business info removed

  // Step 4: Bank Account Information
  const [bankAccount, setBankAccount] = useState({
    accountHolderName: "",
    accountHolderType: "individual",
    routingNumber: "",
    accountNumber: ""
  });
  const [bankStatement, setBankStatement] = useState(null);

  // Step 5: Compliance
  const [tosAgree, setTosAgree] = useState(false);
  const [infoAccurate, setInfoAccurate] = useState(false);
  const [mcc, setMcc] = useState("7299"); // Default MCC for event planning services
  const [additionalNotes, setAdditionalNotes] = useState("");

  // Additional state (signature placeholder retained for future use)
  const [signature, setSignature] = useState(null);
  
  // Representatives/Owners state
  const [representatives, setRepresentatives] = useState([
    {
      firstName: "",
      lastName: "",
      dob: "",
      email: "",
      phone: "",
      address: "",
      idDoc: null,
    },
  ]);

  // --- FIX: Step 3 handlers for typing and file upload ---
  const handleRepChange = (index, field, value) => {
    setRepresentatives((prev) =>
      prev.map((rep, i) => (i === index ? { ...rep, [field]: value } : rep))
    );
  };

  const handleRepFileChange = (index, file) => {
    setRepresentatives((prev) =>
      prev.map((rep, i) => (i === index ? { ...rep, idDoc: file } : rep))
    );
  };

  const handleAddOwner = () => {
    setRepresentatives((prev) => [
      ...prev,
      {
        firstName: "",
        lastName: "",
        dob: "",
        email: "",
        phone: "",
        address: "",
        idDoc: null,
      },
    ]);
  };

  // Document upload functions - store files locally for now
  const handleGovernmentIdUpload = (file) => {
    if (file) {
      setGovernmentId(file);
      setUploadedDocuments(prev => ({ 
        ...prev, 
        governmentId: {
          name: file.name,
          size: file.size,
          type: file.type,
          file: file
        }
      }));
    }
  };

  const handleSelfieVerificationUpload = (file) => {
    if (file) {
      setSelfieVerification(file);
      setUploadedDocuments(prev => ({ 
        ...prev, 
        selfieVerification: {
          name: file.name,
          size: file.size,
          type: file.type,
          file: file
        }
      }));
    }
  };

  const handleBankStatementUpload = (file) => {
    if (file) {
      setBankStatement(file);
      setUploadedDocuments(prev => ({ 
        ...prev, 
        bankStatement: {
          name: file.name,
          size: file.size,
          type: file.type,
          file: file
        }
      }));
    }
  };

  // Simple validation for demo, extend with more comprehensive rules
  const validateStep = () => {
    switch (currentStep) {
      case 1: // Account Basics - Basic info for both individual and company
        if (!email.trim()) return { valid: false, error: "Email is required" };
        if (!validateEmail(email)) return { valid: false, error: "Please enter a valid email address" };
        if (!phone.trim()) return { valid: false, error: "Phone number is required" };
        if (!validatePhone(phone)) return { valid: false, error: "Please enter a valid phone number" };
        return { valid: true };

      case 2: // Individual Info
        if (businessType === "individual") {
          if (!firstName.trim()) return { valid: false, error: "First name is required" };
          if (!lastName.trim()) return { valid: false, error: "Last name is required" };
          if (!validateAge(dateOfBirth)) return { valid: false, error: "You must be at least 18 years old" };
          if (!ssn.trim()) return { valid: false, error: "SSN is required" };
          if (!validateSSN(ssn)) return { valid: false, error: "Please enter valid 9-digit SSN" };
          if (!personalWebsite?.trim()) return { valid: false, error: "Business website is required by Stripe" };
          if (!personalAddress.line1.trim()) return { valid: false, error: "Street address is required" };
          if (!personalAddress.city.trim()) return { valid: false, error: "City is required" };
          if (!personalAddress.state || !personalAddress.state.trim()) return { valid: false, error: "State is required" };
          if (!personalAddress.postalCode.trim()) return { valid: false, error: "Postal code is required" };
          if (!validatePostalCode(personalAddress.postalCode)) return { valid: false, error: "Please enter a valid postal code" };
        }
        return { valid: true };
      case 3:
        if (!bankAccount.accountHolderName.trim()) return { valid: false, error: "Account holder name is required" };
        if (!bankAccount.accountNumber.trim()) return { valid: false, error: "Account number is required" };
        if (!bankAccount.routingNumber.trim()) return { valid: false, error: "Routing number is required" };
        return { valid: true };

      case 4:
        if (!tosAgree) return { valid: false, error: "You must agree to the Terms of Service" };
        if (!infoAccurate) return { valid: false, error: "You must confirm the information is accurate" };
        return { valid: true };

      case 5:
        // Document upload validation
        if (!uploadedDocuments.governmentId) return { valid: false, error: "Government ID document is required" };
        if (!uploadedDocuments.selfieVerification) return { valid: false, error: "Selfie verification is required" };
        return { valid: true };

      default:
        return { valid: false, error: "Invalid step" };
    }
  };

  // State for validation errors
  const [validationError, setValidationError] = useState("");
  const [refreshingStatus, setRefreshingStatus] = useState(false);

  // Handle force refresh of status
  const handleForceRefresh = async () => {
    try {
      setRefreshingStatus(true);
      await dispatch(forceRefreshStatus()).unwrap();
      // Also refresh the onboarding status
      await dispatch(getOnboardingStatus()).unwrap();
    } catch (error) {
      console.error('Failed to refresh status:', error);
    } finally {
      setRefreshingStatus(false);
    }
  };


  // Handle redirect after verification completion
  useEffect(() => {
    if (onboardingStatus && onboardingStatus.onboardingCompleted) {
      // Check if account is fully verified (both charges and payouts enabled)
      const isFullyVerified = onboardingStatus.chargesEnabled && onboardingStatus.payoutsEnabled;
      
      if (isFullyVerified) {
        console.log('✅ Account fully verified, redirecting to vendor dashboard...');
        // Redirect to vendor dashboard after onboarding completion
        setTimeout(() => {
          navigate('/vendor');
        }, 2000);
      } else {
        console.log('⚠️ Account not fully verified yet:', {
          onboardingCompleted: onboardingStatus.onboardingCompleted,
          chargesEnabled: onboardingStatus.chargesEnabled,
          payoutsEnabled: onboardingStatus.payoutsEnabled,
          requiresAction: onboardingStatus.requiresAction
        });
        
        // If onboarding is completed but not fully verified, show a message
        // and set up periodic checking
        if (onboardingStatus.onboardingCompleted && !isFullyVerified) {
          console.log('🔄 Setting up periodic status check for delayed verification...');
          
          // Check status every 30 seconds for the first 5 minutes
          const checkInterval = setInterval(async () => {
            try {
              await dispatch(getOnboardingStatus()).unwrap();
            } catch (error) {
              console.error('Failed to check status:', error);
            }
          }, 30000); // Check every 30 seconds
          
          // Clear interval after 5 minutes
          setTimeout(() => {
            clearInterval(checkInterval);
            console.log('⏰ Stopped periodic status checking after 5 minutes');
          }, 300000); // 5 minutes
          
          // Cleanup function
          return () => {
            clearInterval(checkInterval);
          };
        }
      }
    }
  }, [onboardingStatus, navigate, dispatch]);

  const handleNext = () => {
    const validation = validateStep();
    if (validation.valid) {
      setValidationError("");
      setCurrentStep((s) => Math.min(s + 1, steps.length));
    } else {
      setValidationError(validation.error);
    }
  };

  const handlePrev = () => {
    setValidationError("");
    setCurrentStep((s) => Math.max(s - 1, 1));
  };

  const handleFinalSubmit = async () => {
    const validation = validateStep();
    if (!validation.valid) {
      setValidationError(validation.error);
      return;
    }

    try {
      setValidationError("");
      setUploadingDocuments(true);

      // Get client IP and user agent for compliance
      const ipAddress = await fetch('https://api.ipify.org?format=json')
        .then(res => res.json())
        .then(data => data.ip)
        .catch(() => '127.0.0.1');

      // First, submit the onboarding data to create Stripe account
      const onboardingData = {
        businessType: 'individual',
        personalInfo: {
          firstName,
          lastName,
          dateOfBirth: {
            day: parseInt(dateOfBirth.day, 10),
            month: parseInt(dateOfBirth.month, 10),
            year: parseInt(dateOfBirth.year, 10)
          },
          email,
          phone,
          ssn,
          website: personalWebsite,
          address: personalAddress
        },
        bankAccount,
        compliance: {
          tosAgree,
          infoAccurate,
          ipAddress,
          userAgent: navigator.userAgent,
          mcc: mcc || ''
        },
        additionalNotes: additionalNotes || ''
      };

      const result = await dispatch(startVendorOnboarding(onboardingData)).unwrap();

      // If onboarding was successful, upload documents to Stripe
      if (result.data?.onboardingCompleted || result.data?.vendorProfile?.stripeAccountId) {
        try {
          // Upload documents to Stripe
          const documentUploadPromises = [];

          if (uploadedDocuments.governmentId?.file) {
            const formData = new FormData();
            formData.append('document', uploadedDocuments.governmentId.file);
            formData.append('documentType', 'government_id');
            documentUploadPromises.push(vendorApi.submitVerificationDocument(formData));
          }

          if (uploadedDocuments.selfieVerification?.file) {
            const formData = new FormData();
            formData.append('document', uploadedDocuments.selfieVerification.file);
            formData.append('documentType', 'selfie_verification');
            documentUploadPromises.push(vendorApi.submitVerificationDocument(formData));
          }

          if (uploadedDocuments.bankStatement?.file) {
            const formData = new FormData();
            formData.append('document', uploadedDocuments.bankStatement.file);
            formData.append('documentType', 'bank_statement');
            documentUploadPromises.push(vendorApi.submitVerificationDocument(formData));
          }

          // Wait for all document uploads to complete
          if (documentUploadPromises.length > 0) {
            await Promise.all(documentUploadPromises);
          }

          setValidationError("");
          // Update onboarding status to trigger redirect
          dispatch(getOnboardingStatus());
        } catch (docError) {
          console.error('Document upload failed:', docError);
          setValidationError(`Onboarding completed but document upload failed: ${docError.message}. Please try uploading documents again from your vendor dashboard.`);
        }
      }
    } catch (error) {
      console.error('Onboarding failed:', error);
      setValidationError(error.message || 'Onboarding failed. Please try again.');
    } finally {
      setUploadingDocuments(false);
    }
  };

  // For file uploads and signature later extend with proper handlers

  return (
    <div className="pp-max-container">
      <div className="vo-root">
        <Stepper currentStep={currentStep} steps={steps} />

        {(onboardingError || validationError) && (
          <div className="pp-form-error" role="alert" style={{ marginBottom: '1rem' }}>
            {onboardingError || validationError}
          </div>
        )}

        {onboardingStatus && onboardingStatus.onboardingCompleted && (
          <div className={`pp-form-${getVerificationStatusClass(onboardingStatus)}`} role="alert" style={{ marginBottom: '1rem' }}>
            {getVerificationMessage(onboardingStatus)}
            {getVerificationDetails(onboardingStatus)}
            
            {/* Manual refresh button */}
            <div style={{ marginTop: '1rem' }}>
              <button
                type="button"
                onClick={handleForceRefresh}
                disabled={refreshingStatus}
                style={{
                  padding: '0.5rem 1rem',
                  backgroundColor: '#007bff',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: refreshingStatus ? 'not-allowed' : 'pointer',
                  fontSize: '0.9rem',
                  opacity: refreshingStatus ? 0.6 : 1
                }}
              >
                {refreshingStatus ? 'Refreshing...' : '🔄 Force Refresh Status'}
              </button>
              <div style={{ fontSize: '0.8rem', color: '#666', marginTop: '0.25rem' }}>
                Click to manually sync with Stripe
              </div>
            </div>
          </div>
        )}

        <div className="vo-card">
          {currentStep === 1 && (
            <div className="vo-step-content">
              <h2>Account Basics</h2>
              {/* Business type selection removed: individual only */}
              <div className="vo-input-group">
                <label>Email *</label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  required
                />
              </div>
              <div className="vo-input-group">
                <label>Country</label>
                <CustomSelect
                  value={personalAddress.country}
                  onChange={(value) => setPersonalAddress({...personalAddress, country: value})}
                  options={countries.map((c) => ({
                    label: `${c.flag} ${c.label}`,
                    value: c.code,
                  }))}
                  className="vo-form-select"
                />
              </div>
              <div className="vo-input-group">
                <label>Phone Number *</label>
                <input
                  type="tel"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  placeholder="Enter your phone number"
                  required
                />
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div className="vo-step-content">
              <h2>Individual Info</h2>
              {
                <>
                  <div className="vo-input-2col">
                    <div className="vo-input-group">
                      <label>First Name *</label>
                      <input
                        type="text"
                        value={firstName}
                        onChange={(e) => setFirstName(e.target.value)}
                        placeholder="Enter your first name"
                        required
                      />
                    </div>
                    <div className="vo-input-group">
                      <label>Last Name *</label>
                      <input
                        type="text"
                        value={lastName}
                        onChange={(e) => setLastName(e.target.value)}
                        placeholder="Enter your last name"
                        required
                      />
                    </div>
                  </div>
                  <div className="vo-input-group">
                    <label>
                      Date of Birth {""}
                      <FiInfo
                        className="vo-info-icon"
                        title="Required for identity verification (must be 18+)"
                      />
                    </label>
                    <CustomDatePicker
                      value={dateOfBirth.day && dateOfBirth.month && dateOfBirth.year ? 
                        new Date(dateOfBirth.year, dateOfBirth.month - 1, dateOfBirth.day) : null}
                      onChange={(date) => {
                        if (date instanceof Date) {
                          setDateOfBirth({
                            day: date.getDate().toString(),
                            month: (date.getMonth() + 1).toString(),
                            year: date.getFullYear().toString()
                          });
                        }
                      }}
                      placeholder="Select date of birth"
                    />
                  </div>
                  <div className="vo-input-group">
                    <label>Street Address *</label>
                    <input
                      type="text"
                      value={personalAddress.line1}
                      onChange={(e) => setPersonalAddress(prev => ({ ...prev, line1: e.target.value }))}
                      placeholder="Enter your street address"
                      required
                    />
                  </div>
                  <div className="vo-input-3col">
                    <div className="vo-input-group">
                      <label>City *</label>
                      <input
                        type="text"
                        value={personalAddress.city}
                        onChange={(e) => setPersonalAddress(prev => ({ ...prev, city: e.target.value }))}
                        placeholder="Enter city"
                        required
                      />
                    </div>
                    <div className="vo-input-group">
                      <label>State *</label>
                      <USStateSelect
                        value={personalAddress.state}
                        onChange={(e) => setPersonalAddress(prev => ({ ...prev, state: e.target.value }))}
                        required
                      />
                    </div>
                    <div className="vo-input-group">
                      <label>Postal Code *</label>
                      <input
                        type="text"
                        value={personalAddress.postalCode}
                        onChange={(e) => setPersonalAddress(prev => ({ ...prev, postalCode: e.target.value }))}
                        placeholder="Enter postal code"
                        required
                      />
                    </div>
                  </div>
                  <div className="vo-input-group">
                    <label>Email</label>
                    <input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      disabled
                    />
                  </div>
                  <div className="vo-input-group">
                    <label>Phone Number</label>
                    <input
                      type="tel"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                    />
                  </div>
                  <div className="vo-input-group">
                    <label>
                      SSN *{" "}
                      <FiInfo
                        className="vo-info-icon"
                        title="Required for identity verification"
                      />
                    </label>
                    <input
                      type="text"
                      maxLength="9"
                      value={ssn}
                      onChange={(e) => {
                        const value = e.target.value.replace(/\D/g, ''); // Remove non-digits
                        if (value.length <= 9) {
                          setSsn(value);
                        }
                      }}
                      placeholder="123456789"
                      required
                    />
                  </div>
                  <div className="vo-input-group">
                    <label>Business Website URL *</label>
                    <input
                      type="text"
                      value={personalWebsite}
                      onChange={(e) => setPersonalWebsite(e.target.value)}
                      placeholder="https://yourbusiness.com"
                      required
                    />
                  </div>
                </>
              }
            </div>
          )}

          {currentStep === 3 && (
            <div className="vo-step-content">
              <h2>Bank Details</h2>
              <div className="vo-input-group">
                <label>Account Holder Name *</label>
                <input
                  type="text"
                  value={bankAccount.accountHolderName}
                  onChange={(e) => setBankAccount(prev => ({ ...prev, accountHolderName: e.target.value }))}
                  placeholder="Enter account holder name"
                  required
                />
              </div>
              <div className="vo-input-group">
                <label>Bank Account Number *</label>
                <input
                  type="text"
                  value={bankAccount.accountNumber}
                  onChange={(e) => setBankAccount(prev => ({ ...prev, accountNumber: e.target.value }))}
                  placeholder="Enter account number"
                  required
                />
              </div>
              <div className="vo-input-group">
                <label>Routing Number *</label>
                <input
                  type="text"
                  value={bankAccount.routingNumber}
                  onChange={(e) => setBankAccount(prev => ({ ...prev, routingNumber: e.target.value }))}
                  placeholder="Enter routing number"
                  required
                />
              </div>
              <div className="vo-input-group">
                <label>Bank Statement (Optional)</label>
                <input
                  type="file"
                  onChange={(e) => handleBankStatementUpload(e.target.files && e.target.files[0])}
                  accept=".pdf,.jpg,.jpeg,.png"
                  className="w-full"
                />
                <small className="vo-help-text">Upload a recent bank statement for verification (PDF, JPG, PNG)</small>
                {uploadedDocuments.bankStatement && (
                  <div className="vo-upload-success">
                    <FiCheckCircle className="vo-success-icon" />
                    Bank statement selected: {uploadedDocuments.bankStatement.name}
                  </div>
                )}
              </div>
            </div>
          )}

          

          {currentStep === 4 && (
            <div className="vo-step-content">
              <h2>Compliance & Agreements</h2>
              <label className="vo-checkbox-label">
                <input
                  type="checkbox"
                  checked={tosAgree}
                  onChange={(e) => setTosAgree(e.target.checked)}
                />
                I agree to Stripe's Terms of Service
              </label>
              <label className="vo-checkbox-label">
                <input
                  type="checkbox"
                  checked={infoAccurate}
                  onChange={(e) => setInfoAccurate(e.target.checked)}
                />
                I confirm the information provided is accurate
              </label>
              <div className="vo-input-group">
                <label>
                  Business Category (MCC){" "}
                  <FiInfo
                    className="vo-info-icon"
                    title="Merchant Category Code - describes your business type for payment processing"
                  />
                </label>
                <select
                  value={mcc}
                  onChange={(e) => setMcc(e.target.value)}
                >
                  <option value="7299">7299 - Event Planning Services</option>
                  <option value="7298">7298 - Party Supply Rental</option>
                  <option value="7999">7999 - Entertainment Services</option>
                  <option value="5943">5943 - Stationery & Party Supplies</option>
                  <option value="7941">7941 - Professional Sports Clubs</option>
                </select>
              </div>
              {/* Optional signature upload */}
              <div className="vo-input-group">
                <label>
                  Signature / e-sign (optional){" "}
                  <FaFileUpload className="vo-upload-icon" />
                </label>
                <input
                  type="file"
                  onChange={(e) => setSignature(e.target.files[0])}
                  className="w-full"
                />
              </div>
            </div>
          )}

          {currentStep === 5 && (
            <div className="vo-step-content">
              <h2>Document Upload</h2>
              <p className="vo-step-description">
                Please upload the required documents for verification. All documents must be clear and readable.
              </p>
              
              <div className="vo-document-upload-section">
                <div className="vo-input-group">
                  <label>
                    Government ID Document * <FiUpload className="vo-upload-icon" />
                  </label>
                  <input
                    type="file"
                    onChange={(e) => handleGovernmentIdUpload(e.target.files && e.target.files[0])}
                    accept=".pdf,.jpg,.jpeg,.png"
                    className="w-full"
                    required
                  />
                  <small className="vo-help-text">
                    Upload a clear photo of your driver's license, passport, or state ID (PDF, JPG, PNG)
                  </small>
                  {uploadedDocuments.governmentId && (
                    <div className="vo-upload-success">
                      <FiCheckCircle className="vo-success-icon" />
                      Document selected: {uploadedDocuments.governmentId.name}
                    </div>
                  )}
                </div>

                <div className="vo-input-group">
                  <label>
                    Selfie Verification * <FiUpload className="vo-upload-icon" />
                  </label>
                  <input
                    type="file"
                    onChange={(e) => handleSelfieVerificationUpload(e.target.files && e.target.files[0])}
                    accept=".jpg,.jpeg,.png"
                    className="w-full"
                    required
                  />
                  <small className="vo-help-text">
                    Take a clear selfie holding your ID next to your face (JPG, PNG)
                  </small>
                  {uploadedDocuments.selfieVerification && (
                    <div className="vo-upload-success">
                      <FiCheckCircle className="vo-success-icon" />
                      Selfie verification selected: {uploadedDocuments.selfieVerification.name}
                    </div>
                  )}
                </div>

                <div className="vo-input-group">
                  <label>
                    Additional Documents (Optional) <FiUpload className="vo-upload-icon" />
                  </label>
                  <input
                    type="file"
                    onChange={(e) => {
                      // Handle additional documents if needed
                    }}
                    accept=".pdf,.jpg,.jpeg,.png"
                    className="w-full"
                    multiple
                  />
                  <small className="vo-help-text">
                    Upload any additional verification documents (business license, insurance, etc.)
                  </small>
                </div>
              </div>

              {uploadingDocuments && (
                <div className="vo-uploading-indicator">
                  <div className="vo-spinner"></div>
                  Processing onboarding and uploading documents...
                </div>
              )}
            </div>
          )}

          <div className="vo-buttons">
            {currentStep > 1 && (
              <button
                className="pp-btn-secondary"
                type="button"
                onClick={handlePrev}
              >
                Back
              </button>
            )}
            {currentStep < steps.length ? (
              <button
                className="pp-btn-primary"
                type="button"
                disabled={!validateStep().valid}
                onClick={handleNext}
              >
                Next
              </button>
            ) : (
              <button
                className="pp-btn-primary"
                type="button"
                disabled={!validateStep().valid || onboardingLoading || uploadingDocuments}
                onClick={handleFinalSubmit}
              >
                {onboardingLoading || uploadingDocuments ? 'Processing...' : 'Complete Onboarding & Upload Documents'}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
