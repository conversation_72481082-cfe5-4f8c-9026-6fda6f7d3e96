.nero-heroes {
  width: 100%;
  min-height: 100vh;
  background: var(--pp-clr-bg);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--pp-sp-32) 0;
}

/* Banner Section */
.nero-heroes__banner {
  width: 100%;
  max-width: 1400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: var(--pp-section-padding);
  gap: var(--pp-sp-32);
  position: relative;
}

.nero-heroes__banner-img {
  width: 100%;
  height: auto;
  aspect-ratio: 5 / 1.4;
  object-fit: cover;
  border-radius: var(--pp-bor-rad-16);
  box-shadow: var(--pp-shdw-lg);
  background: var(--pp-clr-card-rentals-bg);
}

.nero-heroes__title {
  font-family: var(--pp-font-Playfair-Display);
  font-size: var(--pp-font-heading2);
  font-weight: 700;
  color: var(--pp-clr-text-main);
  text-align: center;
  padding: var(--pp-sp-12) var(--pp-sp-24);
  border-radius: var(--pp-bor-rad-12);
  background: var(--pp-clr-primary);
  box-shadow: var(--pp-shdw-md);
  margin-top: -var(--pp-sp-32);
  letter-spacing: 0.01em;
  position: absolute;
  bottom: 0%;
  transform: translateY(50%);
}

/* Product Grid Section */
.nero-heroes__products-grid {
  width: 100%;
  max-width: 1400px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: var(--pp-sp-24);
  padding: var(--pp-sp-16) 0;
}

@media (max-width: 1024px) {
  .nero-heroes__banner,
  .nero-heroes__products-grid {
    max-width: 96vw;
    gap: var(--pp-sp-16);
  }
  .nero-heroes__title {
    font-size: var(--pp-font-heading3);
    padding: var(--pp-sp-8) var(--pp-sp-16);
  }
  .nero-heroes__banner {
    margin-bottom: 80px;
  }
}

@media (max-width: 768px) {
  .nero-heroes__products-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: var(--pp-sp-12);
  }
  .nero-heroes__banner {
    margin-bottom: 60px;
  }
}
@media (max-width: 600px) {
  .nero-heroes__banner {
  }
  .nero-heroes__banner-img {
    display: none;
  }
  .nero-heroes__title {
    font-size: var(--pp-font-heading5);
    padding: var(--pp-sp-8);
    position: relative;
  }
}
@media (max-width: 414px) {
  .nero-heroes__products-grid {
    grid-template-columns: 1fr;
    gap: var(--pp-sp-8);
  }
}
