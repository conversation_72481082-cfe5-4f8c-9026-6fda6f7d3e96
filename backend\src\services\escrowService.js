const mongoose = require('mongoose');
const { paymentLogger } = require('../utils/logger');
const StripeService = require('./stripeService');
const Booking = require('../models/Booking');
const Transaction = require('../models/Transaction');
const VendorProfile = require('../models/VendorProfile');

/**
 * Escrow Service
 * Handles automatic escrow release triggers and payout management
 */
class EscrowService {
    /**
     * Check and process automatic escrow releases
     * This should be called by a scheduled job (cron)
     */
    static async processAutomaticReleases() {
        try {
            paymentLogger.info('Starting automatic escrow release processing');

            // Find bookings eligible for automatic release
            const eligibleBookings = await this.findEligibleBookings();
            
            let processedCount = 0;
            let errorCount = 0;

            for (const booking of eligibleBookings) {
                try {
                    await this.processEscrowRelease(booking);
                    processedCount++;
                } catch (error) {
                    errorCount++;
                    paymentLogger.error('Failed to process escrow release', {
                        bookingId: booking._id,
                        error: error.message
                    });
                }
            }

            paymentLogger.info('Automatic escrow release processing completed', {
                eligibleBookings: eligibleBookings.length,
                processedCount,
                errorCount
            });

            return {
                success: errorCount === 0,
                eligibleBookings: eligibleBookings.length,
                processedCount,
                errorCount
            };

        } catch (error) {
            paymentLogger.error('Automatic escrow release processing failed', {
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }

    /**
     * Find bookings eligible for automatic escrow release
     */
    static async findEligibleBookings() {
        const now = new Date();
        const releaseDelay = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

        return await Booking.find({
            status: 'completed',
            'paymentDetails.status': 'completed',
            'escrowDetails.status': 'held',
            'escrowDetails.autoReleaseDate': { $lte: now },
            // Additional safety check - ensure booking was completed at least 7 days ago
            completedAt: { $lte: new Date(now.getTime() - releaseDelay) }
        }).populate('vendorId customerId serviceId');
    }

    /**
     * Process escrow release for a specific booking
     */
    static async processEscrowRelease(booking) {
        try {
            paymentLogger.info('Processing escrow release', {
                bookingId: booking._id,
                vendorId: booking.vendorId._id,
                amount: booking.paymentDetails.vendorAmount
            });

            // Get vendor profile
            const vendorProfile = await VendorProfile.findOne({ 
                userId: booking.vendorId._id 
            });

            if (!vendorProfile || !vendorProfile.stripeAccountId) {
                throw new Error('Vendor Stripe account not found');
            }

            // Create transfer to vendor
            const transferResult = await StripeService.createTransfer({
                amount: booking.paymentDetails.vendorAmount,
                destination: vendorProfile.stripeAccountId,
                metadata: {
                    bookingId: booking._id.toString(),
                    vendorId: booking.vendorId._id.toString(),
                    customerId: booking.customerId._id.toString(),
                    type: 'escrow_release',
                    releaseType: 'automatic'
                }
            });

            // Update booking escrow status
            booking.escrowDetails.status = 'released';
            booking.escrowDetails.releasedAt = new Date();
            booking.escrowDetails.releaseType = 'automatic';
            booking.escrowDetails.stripeTransferId = transferResult.transferId;

            await booking.save();

            // Create transaction record
            await this.createEscrowReleaseTransaction(booking, transferResult);

            paymentLogger.info('Escrow release completed successfully', {
                bookingId: booking._id,
                transferId: transferResult.transferId,
                amount: booking.paymentDetails.vendorAmount
            });

            return {
                success: true,
                transferId: transferResult.transferId,
                amount: booking.paymentDetails.vendorAmount
            };

        } catch (error) {
            paymentLogger.error('Escrow release failed', {
                bookingId: booking._id,
                error: error.message
            });
            
            // Update booking with error status
            booking.escrowDetails.status = 'release_failed';
            booking.escrowDetails.lastError = error.message;
            booking.escrowDetails.lastErrorAt = new Date();
            await booking.save();

            throw error;
        }
    }

    /**
     * Manual escrow release (for admin or dispute resolution)
     */
    static async manualEscrowRelease(bookingId, adminUserId, reason) {
        try {
            const booking = await Booking.findById(bookingId)
                .populate('vendorId customerId serviceId');

            if (!booking) {
                throw new Error('Booking not found');
            }

            if (booking.escrowDetails.status !== 'held') {
                throw new Error('Escrow is not in held status');
            }

            paymentLogger.info('Processing manual escrow release', {
                bookingId,
                adminUserId,
                reason,
                amount: booking.paymentDetails.vendorAmount
            });

            // Get vendor profile
            const vendorProfile = await VendorProfile.findOne({ 
                userId: booking.vendorId._id 
            });

            if (!vendorProfile || !vendorProfile.stripeAccountId) {
                throw new Error('Vendor Stripe account not found');
            }

            // Create transfer to vendor
            const transferResult = await StripeService.createTransfer({
                amount: booking.paymentDetails.vendorAmount,
                destination: vendorProfile.stripeAccountId,
                metadata: {
                    bookingId: booking._id.toString(),
                    vendorId: booking.vendorId._id.toString(),
                    customerId: booking.customerId._id.toString(),
                    type: 'escrow_release',
                    releaseType: 'manual',
                    adminUserId: adminUserId.toString(),
                    reason
                }
            });

            // Update booking escrow status
            booking.escrowDetails.status = 'released';
            booking.escrowDetails.releasedAt = new Date();
            booking.escrowDetails.releaseType = 'manual';
            booking.escrowDetails.releasedBy = adminUserId;
            booking.escrowDetails.releaseReason = reason;
            booking.escrowDetails.stripeTransferId = transferResult.transferId;

            await booking.save();

            // Create transaction record
            await this.createEscrowReleaseTransaction(booking, transferResult);

            paymentLogger.info('Manual escrow release completed successfully', {
                bookingId,
                transferId: transferResult.transferId,
                adminUserId,
                amount: booking.paymentDetails.vendorAmount
            });

            return {
                success: true,
                transferId: transferResult.transferId,
                amount: booking.paymentDetails.vendorAmount
            };

        } catch (error) {
            paymentLogger.error('Manual escrow release failed', {
                bookingId,
                adminUserId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Create transaction record for escrow release
     */
    static async createEscrowReleaseTransaction(booking, transferResult) {
        const transaction = new Transaction({
            bookingId: booking._id,
            customerId: booking.customerId._id,
            vendorId: booking.vendorId._id,
            transactionType: 'payout',
            stripeTransferId: transferResult.transferId,
            amount: booking.paymentDetails.vendorAmount,
            currency: 'USD',
            status: 'completed',
            description: `Escrow release for booking ${booking._id}`,
            vendorPayout: booking.paymentDetails.vendorAmount,
            netAmount: booking.paymentDetails.vendorAmount,
            feeBreakdown: {
                platformFee: 0, // Already deducted during initial payment
                stripeFee: 0,
                processingFee: 0,
                otherFees: 0
            },
            metadata: {
                escrowRelease: true,
                releaseType: booking.escrowDetails.releaseType,
                originalPaymentIntentId: booking.paymentDetails.paymentIntentId
            }
        });

        await transaction.save();
        return transaction;
    }

    /**
     * Get escrow status for a booking
     */
    static async getEscrowStatus(bookingId) {
        const booking = await Booking.findById(bookingId);
        
        if (!booking) {
            throw new Error('Booking not found');
        }

        return {
            status: booking.escrowDetails?.status || 'unknown',
            heldAmount: booking.paymentDetails?.vendorAmount || 0,
            autoReleaseDate: booking.escrowDetails?.autoReleaseDate,
            releasedAt: booking.escrowDetails?.releasedAt,
            releaseType: booking.escrowDetails?.releaseType,
            stripeTransferId: booking.escrowDetails?.stripeTransferId
        };
    }

    /**
     * Set up escrow for a new booking
     */
    static async setupEscrow(booking) {
        const autoReleaseDate = new Date();
        autoReleaseDate.setDate(autoReleaseDate.getDate() + 7); // 7 days from now

        booking.escrowDetails = {
            status: 'held',
            heldAmount: booking.paymentDetails.vendorAmount,
            autoReleaseDate,
            createdAt: new Date()
        };

        await booking.save();

        paymentLogger.info('Escrow setup completed', {
            bookingId: booking._id,
            heldAmount: booking.paymentDetails.vendorAmount,
            autoReleaseDate
        });

        return booking.escrowDetails;
    }
}

module.exports = EscrowService;
