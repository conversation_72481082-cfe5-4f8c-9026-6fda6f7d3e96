#!/usr/bin/env node

/**
 * Automatic Escrow Release Job
 * 
 * This script processes automatic escrow releases for completed bookings.
 * It should be run as a scheduled job (e.g., daily via cron).
 * 
 * Usage:
 *   node src/scripts/process_escrow_releases.js
 * 
 * Cron example (run daily at 2 AM):
 *   0 2 * * * /usr/bin/node /path/to/backend/src/scripts/process_escrow_releases.js
 */

require('dotenv').config();
const mongoose = require('mongoose');
const EscrowService = require('../services/escrowService');
const { paymentLogger } = require('../utils/logger');

// Import models to ensure they're registered
require('../models/Booking');
require('../models/Transaction');
require('../models/VendorProfile');
require('../models/User');

async function runEscrowReleaseJob() {
    try {
        console.log('🔄 Starting automatic escrow release job...');
        console.log(`Timestamp: ${new Date().toISOString()}\n`);

        // Connect to database
        console.log('Connecting to database...');
        await mongoose.connect(process.env.MONGODB_URI, {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });
        console.log('✅ Database connected\n');

        // Process automatic releases
        console.log('🔍 Processing automatic escrow releases...');
        const result = await EscrowService.processAutomaticReleases();
        
        if (result.success) {
            console.log('✅ Escrow release job completed successfully!');
        } else {
            console.log('⚠️  Escrow release job completed with some errors');
        }
        
        console.log(`📊 Job Results:`);
        console.log(`   - Eligible bookings found: ${result.eligibleBookings}`);
        console.log(`   - Successfully processed: ${result.processedCount}`);
        console.log(`   - Errors encountered: ${result.errorCount}\n`);

        // Log final status
        paymentLogger.info('ESCROW RELEASE JOB COMPLETED', {
            timestamp: new Date().toISOString(),
            eligibleBookings: result.eligibleBookings,
            processedCount: result.processedCount,
            errorCount: result.errorCount,
            success: result.success
        });

        if (result.processedCount > 0) {
            console.log(`💰 Successfully released escrow for ${result.processedCount} booking(s)`);
        }

        if (result.errorCount > 0) {
            console.log(`❌ Failed to process ${result.errorCount} escrow release(s)`);
            console.log('Please check the logs for details.');
            process.exit(1);
        }

        console.log('🎉 Escrow release job completed successfully!\n');

    } catch (error) {
        console.error('❌ CRITICAL ERROR: Escrow release job failed');
        console.error('Error:', error.message);
        console.error('Stack:', error.stack);
        
        paymentLogger.error('ESCROW RELEASE JOB FAILED', {
            error: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString()
        });
        
        process.exit(1);
    } finally {
        await mongoose.disconnect();
        console.log('Database connection closed.');
    }
}

// Handle process termination
process.on('SIGINT', async () => {
    console.log('\n⚠️  Job interrupted by user');
    await mongoose.disconnect();
    process.exit(1);
});

process.on('SIGTERM', async () => {
    console.log('\n⚠️  Job terminated');
    await mongoose.disconnect();
    process.exit(1);
});

// Run the job
if (require.main === module) {
    runEscrowReleaseJob();
}

module.exports = { runEscrowReleaseJob };
