/* Scoped summary card */
.pp-checkout-page .pp-summary-card {
  border-radius: var(--pp-bor-rad-12);
  background: #f6f6f6;
  box-shadow: var(--pp-shdw-sm);
  padding: var(--pp-sp-24);
}

.pp-checkout-page .pp-summary-card__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--pp-sp-16);
}

.pp-checkout-page .pp-summary-card__title {
  margin: 0;
  font-size: 28px;
}

.pp-checkout-page .pp-summary-card__items {
  
  font-size: var(--pp-font-extra-small);
}

.pp-checkout-page .pp-summary-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: var(--pp-sp-8) 0;
}

.pp-checkout-page .pp-summary-address {
  
  font-size: var(--pp-font-extra-small);
  margin-top: var(--pp-sp-8);
}

.pp-checkout-page .pp-summary-divider {
  height: 1px;
  background: var(--pp-clr-border);
  margin: var(--pp-sp-16) 0;
}

.pp-summary-label{
  font-weight: 600;
  font-size: var(--pp-font-extra-small);
}
.pp-summary-value{
  font-size: var(--pp-font-extra-small);
}

.pp-checkout-page .pp-summary-row--total .pp-summary-label,
.pp-checkout-page .pp-summary-row--total .pp-summary-value {
  font-size: var(--pp-font-small-font);
  font-weight: 700;
}

@media (max-width: 800px) {
  .pp-checkout-page .pp-summary-card__title{
    font-size: var(--pp-font-heading5);
  }
}

@media (max-width: 400px) {


  
  .pp-checkout-page .pp-summary-card {
    padding: var(--pp-sp-16);
    width: 100%;
  }
}