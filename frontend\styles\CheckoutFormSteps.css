/* Scoped to Checkout page */
.pp-checkout-page .pp-steps {
  display: flex;
  flex-wrap: wrap;
  gap: var(--pp-sp-24);
  padding: 0;
  margin: 0;
  list-style: none;
  border-bottom: 1px solid var(--pp-clr-border-light);
}

.pp-checkout-page .pp-step {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8);
  color: var(--pp-clr-text-gray);
  font-size: var(--pp-font-extra-small);
  padding: var(--pp-sp-8) 0;
  font-weight: 600;
}

.pp-checkout-page .pp-step__index {
  font-weight: 600;
}

.pp-checkout-page .pp-step--active {
  color: var(--pp-clr-secondary);
  border-bottom: 1px solid var(--pp-clr-secondary);
}

/* Mobile dropdown styling (only visible under 480px) */
@media (max-width: 480px) {
}
