/* =========================
   📄 Contact Page Wrapper
   ========================= */
.pp-contact-page {
  background: var(--pp-clr-bg);
  color: var(--pp-clr-text-main);
  padding-top: var(--pp-sp-60);
  padding-bottom: var(--pp-sp-60);
  max-width: var(--pp-sp-1200);
  margin: 0 auto;
}

/* =========================
     🎉 Banner Section
     ========================= */
.pp-contact-banner {
  display: grid;
  position: relative;
  justify-items: center;
  gap: var(--pp-sp-24);
  margin-top: 80px;
}

.pp-contact-banner-img {
  max-width: 100%;
  width: 100%;
  border-radius: var(--pp-bor-rad-16);
}
.pp-contact-title-main {
  font-size: var(--pp-font-heading3);
  font-family: var(--pp-font-Playfair-Display);
  font-weight: 600;
  border-radius: var(--pp-bor-rad-16) var(--pp-bor-rad-16) 0 0;
  padding: var(--pp-sp-24) var(--pp-sp-24) 0 var(--pp-sp-24);
  text-align: center;
  margin-top: var(--pp-sp-20);
  position: absolute;
  background-color: white;
  bottom: -5%;
}
.pp-contact-title {
  font-size: var(--pp-font-heading3);
  font-family: var(--pp-font-Playfair-Display);
  font-weight: 600;
  text-align: center;
  margin-top: var(--pp-sp-20);
}
.contactpage-info {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-16);
}

.contactpage-days {
  display: grid;
  gap: var(--pp-sp-16);
}
.contactpage-details {
  display: grid;
  margin-top: var(--pp-sp-32);
  gap: var(--pp-sp-16);
}
.pp-contact-info-span {
  font-size: var(--pp-font-base-font);
  font-weight: 500;
}
.robotcontainer {
  display: flex;
  gap: 10px;
  align-items: center;
  border-radius: 12px;
  margin: 10px 0;
}
/* =========================
     📱 Responsive
     ========================= */
@media (max-width: 768px) {
  .pp-contact-page {
    padding-top: var(--pp-sp-40);
    padding-bottom: var(--pp-sp-60);
  }

  .pp-contact-title {
    font-size: var(--pp-font-heading4);
  }
  .pp-contact-banner {
    margin-top: var(--pp-sp-40);
  }
  .pp-contact-title-main {
    padding: 10px 10px 0 10px;
  }
}
@media (max-width: 480px) {
  .pp-contact-title-main {
    position: relative;

    padding: 0;
    margin: 32px 0 40px 0;
  }
  .pp-contact-banner-img {
    display: none;
  }
  .pp-contact-banner {
    margin: 0;
  }
  .pp-contact-form {
    gap: 10px;
  }
  .pp-contact-page .pp-form-row {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  .pp-form-field {
    gap: 0;
  }
  .contactpage-days {
    gap: var(--pp-sp-8);
  }
  .contactpage-details {
    margin-top: var(--pp-sp-8);
    gap: var(--pp-sp-8);
  }
}
