import { useSelector, useDispatch } from 'react-redux';
import { useEffect, useCallback } from 'react';
import {
  registerUser,
  loginUser,
  logoutUser,
  getUserProfile,
  refreshAccessToken,
  clearError,
  clearRegistrationSuccess,
  initializeAuth,
  selectAuth,
  selectUser,
  selectIsAuthenticated,
  selectAuthLoading,
  selectAuthError,
  selectRegistrationSuccess,
  selectRequiresVerification,
  selectVendorOnboardingStatus,
} from './authSlice';

/**
 * Custom hook for authentication functionality
 * Provides easy access to auth state and actions
 */
export const useAuth = () => {
  const dispatch = useDispatch();
  const auth = useSelector(selectAuth);
  const user = useSelector(selectUser);
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const loading = useSelector(selectAuthLoading);
  const error = useSelector(selectAuthError);
  const registrationSuccess = useSelector(selectRegistrationSuccess);
  const requiresVerification = useSelector(selectRequiresVerification);
  const vendorOnboardingStatus = useSelector(selectVendorOnboardingStatus);

  // Initialize auth state on mount
  useEffect(() => {
    dispatch(initializeAuth());
  }, [dispatch]);

  // Auth actions
  const register = useCallback(async (userData) => {
    return dispatch(registerUser(userData)).unwrap();
  }, [dispatch]);

  const login = useCallback(async (credentials) => {
    return dispatch(loginUser(credentials)).unwrap();
  }, [dispatch]);

  const logout = useCallback(async (logoutData = {}) => {
    return dispatch(logoutUser(logoutData)).unwrap();
  }, [dispatch]);

  const getProfile = useCallback(async () => {
    return dispatch(getUserProfile()).unwrap();
  }, [dispatch]);

  const refreshToken = useCallback(async () => {
    return dispatch(refreshAccessToken()).unwrap();
  }, [dispatch]);

  const clearAuthError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  const clearRegSuccess = useCallback(() => {
    dispatch(clearRegistrationSuccess());
  }, [dispatch]);

  // Helper functions
  const isVendor = useCallback(() => {
    return user?.role === 'vendor';
  }, [user]);

  const isCustomer = useCallback(() => {
    return user?.role === 'customer';
  }, [user]);

  const isAdmin = useCallback(() => {
    return user?.role === 'admin';
  }, [user]);

  const hasRole = useCallback((role) => {
    return user?.role === role;
  }, [user]);

  return {
    // State
    auth,
    user,
    isAuthenticated,
    loading,
    error,
    registrationSuccess,
    requiresVerification,
    vendorOnboardingStatus,

    // Actions
    register,
    login,
    logout,
    getProfile,
    refreshToken,
    clearAuthError,
    clearRegSuccess,

    // Helper functions
    isVendor,
    isCustomer,
    isAdmin,
    hasRole,
  };
};
