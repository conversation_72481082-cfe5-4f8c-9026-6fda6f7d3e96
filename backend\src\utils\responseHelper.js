/**
 * Standardized API response helpers
 * Provides consistent response format across all endpoints
 */

class ApiResponse {
    constructor(success, message, data = null, meta = null) {
        this.success = success;
        this.message = message;
        this.data = data;
        this.meta = meta;
        this.timestamp = new Date().toISOString();
    }
}

class ApiError extends Error {
    constructor(message, statusCode = 500, code = null, details = null) {
        super(message);
        this.name = 'ApiError';
        this.statusCode = statusCode;
        this.code = code;
        this.details = details;
        this.isOperational = true;
    }
}

// Success response helpers
const sendSuccess = (res, message = 'Success', data = null, statusCode = 200, meta = null) => {
    const response = new ApiResponse(true, message, data, meta);
    return res.status(statusCode).json(response);
};

const sendCreated = (res, message = 'Resource created successfully', data = null, meta = null) => {
    return sendSuccess(res, message, data, 201, meta);
};

const sendUpdated = (res, message = 'Resource updated successfully', data = null, meta = null) => {
    return sendSuccess(res, message, data, 200, meta);
};

const sendDeleted = (res, message = 'Resource deleted successfully', data = null, meta = null) => {
    return sendSuccess(res, message, data, 200, meta);
};

// Error response helpers
const sendError = (res, message = 'An error occurred', statusCode = 500, code = null, details = null) => {
    const response = new ApiResponse(false, message, null, {
        code,
        details,
        statusCode
    });
    return res.status(statusCode).json(response);
};

const sendBadRequest = (res, message = 'Bad request', details = null) => {
    return sendError(res, message, 400, 'BAD_REQUEST', details);
};

const sendUnauthorized = (res, message = 'Unauthorized access') => {
    return sendError(res, message, 401, 'UNAUTHORIZED');
};

const sendForbidden = (res, message = 'Access forbidden') => {
    return sendError(res, message, 403, 'FORBIDDEN');
};

const sendNotFound = (res, message = 'Resource not found') => {
    return sendError(res, message, 404, 'NOT_FOUND');
};

const sendConflict = (res, message = 'Resource already exists', details = null) => {
    return sendError(res, message, 409, 'CONFLICT', details);
};

const sendValidationError = (res, message = 'Validation failed', errors = []) => {
    return sendError(res, message, 422, 'VALIDATION_ERROR', errors);
};

const sendTooManyRequests = (res, message = 'Too many requests') => {
    return sendError(res, message, 429, 'TOO_MANY_REQUESTS');
};

const sendInternalError = (res, message = 'Internal server error') => {
    return sendError(res, message, 500, 'INTERNAL_ERROR');
};

const sendServiceUnavailable = (res, message = 'Service unavailable') => {
    return sendError(res, message, 503, 'SERVICE_UNAVAILABLE');
};

// Paginated response helper
const sendPaginated = (res, data, pagination, message = 'Data retrieved successfully') => {
    const meta = {
        pagination: {
            currentPage: pagination.page,
            totalPages: pagination.totalPages,
            totalItems: pagination.totalItems,
            itemsPerPage: pagination.limit,
            hasNextPage: pagination.page < pagination.totalPages,
            hasPreviousPage: pagination.page > 1
        }
    };

    return sendSuccess(res, message, data, 200, meta);
};

// List response helper (for non-paginated lists)
const sendList = (res, data, message = 'List retrieved successfully', totalCount = null) => {
    const meta = {
        totalCount: totalCount || (Array.isArray(data) ? data.length : 0),
        itemCount: Array.isArray(data) ? data.length : 0
    };

    return sendSuccess(res, message, data, 200, meta);
};

// File upload response helper
const sendFileUploaded = (res, fileData, message = 'File uploaded successfully') => {
    return sendSuccess(res, message, {
        filename: fileData.filename,
        originalName: fileData.originalname,
        mimetype: fileData.mimetype,
        size: fileData.size,
        url: fileData.url || fileData.path,
        uploadedAt: new Date().toISOString()
    }, 201);
};

// Async wrapper for route handlers to catch errors
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};

// Format validation errors from Joi
const formatJoiErrors = (joiError) => {
    return joiError.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value
    }));
};

// Format Mongoose validation errors
const formatMongooseErrors = (mongooseError) => {
    const errors = [];

    if (mongooseError.code === 11000) {
        // Duplicate key error
        const field = Object.keys(mongooseError.keyValue)[0];
        const value = mongooseError.keyValue[field];
        errors.push({
            field: field,
            message: `${field} '${value}' already exists`,
            value: value
        });
    } else if (mongooseError.errors) {
        // Validation errors
        Object.keys(mongooseError.errors).forEach(field => {
            const error = mongooseError.errors[field];
            errors.push({
                field: field,
                message: error.message,
                value: error.value
            });
        });
    }

    return errors;
};

// Handle different types of errors
const handleError = (error, req, res, next) => {
    let statusCode = error.statusCode || 500;
    let message = error.message || 'Internal server error';
    let code = error.code || 'INTERNAL_ERROR';
    let details = error.details || null;

    // Mongoose validation error
    if (error.name === 'ValidationError') {
        statusCode = 422;
        message = 'Validation failed';
        code = 'VALIDATION_ERROR';
        details = formatMongooseErrors(error);
    }

    // Mongoose duplicate key error
    else if (error.code === 11000) {
        statusCode = 409;
        message = 'Resource already exists';
        code = 'DUPLICATE_ERROR';
        details = formatMongooseErrors(error);
    }

    // Mongoose cast error (invalid ObjectId)
    else if (error.name === 'CastError') {
        statusCode = 400;
        message = 'Invalid resource ID';
        code = 'INVALID_ID';
    }

    // JWT errors
    else if (error.name === 'JsonWebTokenError') {
        statusCode = 401;
        message = 'Invalid token';
        code = 'INVALID_TOKEN';
    }

    // JWT expired error
    else if (error.name === 'TokenExpiredError') {
        statusCode = 401;
        message = 'Token expired';
        code = 'TOKEN_EXPIRED';
    }

    // Stripe errors
    else if (error.type && error.type.startsWith('Stripe')) {
        statusCode = 400;
        message = 'Payment processing error';
        code = 'PAYMENT_ERROR';
        details = { stripeError: error.message };
    }

    return sendError(res, message, statusCode, code, details);
};

// Rate limiting helper
const createRateLimitResponse = (windowMs, max) => {
    return {
        windowMs,
        max,
        message: 'Too many requests from this IP, please try again later',
        standardHeaders: true,
        legacyHeaders: false,
        handler: (req, res) => {
            return sendTooManyRequests(res, 'Rate limit exceeded. Too many requests from this IP.');
        }
    };
};

// API health check response
const sendHealthCheck = (res, checks = {}) => {
    const status = Object.values(checks).every(check => check.status === 'ok') ? 'healthy' : 'degraded';

    const data = {
        status,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development',
        version: process.env.npm_package_version || '1.0.0',
        checks
    };

    const statusCode = status === 'healthy' ? 200 : 503;
    return res.status(statusCode).json(data);
};

// Cache response helper
const sendCached = (res, data, cacheInfo, message = 'Data retrieved from cache') => {
    const meta = {
        cached: true,
        cacheKey: cacheInfo.key,
        cachedAt: cacheInfo.cachedAt,
        expiresAt: cacheInfo.expiresAt,
        ttl: cacheInfo.ttl
    };

    return sendSuccess(res, message, data, 200, meta);
};

// Bulk operation response helper
const sendBulkResult = (res, results, message = 'Bulk operation completed') => {
    const data = {
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length,
        total: results.length,
        results: results
    };

    return sendSuccess(res, message, data);
};

// Analytics response helper
const sendAnalytics = (res, data, dateRange, message = 'Analytics data retrieved') => {
    const meta = {
        dateRange,
        generatedAt: new Date().toISOString(),
        dataPoints: Array.isArray(data) ? data.length : Object.keys(data).length
    };

    return sendSuccess(res, message, data, 200, meta);
};

module.exports = {
    ApiResponse,
    ApiError,
    sendSuccess,
    sendCreated,
    sendUpdated,
    sendDeleted,
    sendError,
    sendBadRequest,
    sendUnauthorized,
    sendForbidden,
    sendNotFound,
    sendConflict,
    sendValidationError,
    sendTooManyRequests,
    sendInternalError,
    sendServiceUnavailable,
    sendPaginated,
    sendList,
    sendFileUploaded,
    sendHealthCheck,
    sendCached,
    sendBulkResult,
    sendAnalytics,
    asyncHandler,
    formatJoiErrors,
    formatMongooseErrors,
    handleError,
    createRateLimitResponse
};
