.pp-event-service-img--stacked{
  width: 100%;
    
}
.pp-event-service-img-inner{
 
  
  object-fit: cover;
}
.pp-event-service-img-inner:first-child{
  width: 70%;
}
.pp-event-service-img-inner:last-child{
  width: 30%;
}
.pp-event-row {
    display: flex;
    align-items: flex-start;
   
    gap: var(--pp-sp-16);
    min-height: 88px;
    width: 100%;
  }
  .pp-event-service-divider {
    width: 100%;
    height: 1px;
    background: var(--pp-clr-border);
    margin: var(--pp-sp-8) 0;
  }
  
  .pp-event-service-vendor-main{
    display: grid;
    gap: var(--pp-sp-4);
    justify-content: start;
    align-items: center;
    line-height: var(--pp-sp-22);
  }
  .pp-event-row-right-main{
    display: flex;
width: 100%;
    align-items: flex-start;
    min-width: 140px;
  }
  .pp-event-row-left-main{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding-top: var(--pp-sp-16);
    width: 30%;
  }
  .pp-event-row-left {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    min-width: 140px;
  }
  .pp-event-row-title {
    font-size: 26px;
    font-weight: 600;
    margin-bottom: var(--pp-sp-4);
  }
  .pp-event-row-edit {
    font-size: var(--pp-font-extra-small);
    font-weight: 600;
    text-decoration: underline;
    cursor: pointer;
    margin-bottom: var(--pp-sp-8);
  }
  .pp-event-service-details{
    
    align-items: center;
    gap: var(--pp-sp-60);
    display: flex
  }
  .pp-event-row-dateblock {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    min-width: 56px;

  }
  .pp-event-row-month {
    font-size: var(--pp-font-heading5);
    font-weight: 600;
   
  }
  .pp-event-row-day {
    font-size: 60px;
    font-weight: 600;
    color: var(--pp-clr-secondary);
    line-height: 1;
  }
  .pp-event-row-content {
    display: flex;
    flex-direction: column;
    
    width: 100%;
  
  }
  .pp-event-service-row-inner{
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: var(--pp-sp-16);
  }
  .pp-event-service-row {
  padding: var(--pp-sp-12);
    display: flex;
    align-items: center;
    gap: var(--pp-sp-16);
    border-radius: var(--pp-bor-rad-8);
    background: var(--pp-clr-primary);
    transition: background-color 0.2s ease;
    cursor: pointer;
  }
  .pp-event-service-row:hover {
    background-color: #E9E9E9;
  }
 
  .pp-event-service-img {
    width: 146px;
    height:96px;
    display: flex;
    gap: 3px;
    overflow: hidden;
    object-fit: cover;
    border-radius: var(--pp-bor-rad-8);
    border: 1px solid var(--pp-clr-border);
   
  }
  .pp-event-service-img-one{
    width: 146px;
    height:96px;
    
    overflow: hidden;
    object-fit: cover;
    border-radius: var(--pp-bor-rad-8);
    border: 1px solid var(--pp-clr-border);
   
  }
  .pp-event-service-img-three{
    width: 146px;
    height:96px;
    display: grid;
  grid-template-columns: 1fr 1fr;
    gap: 3px;
    overflow: hidden;
    object-fit: cover;
    border-radius: var(--pp-bor-rad-8);
    border: 1px solid var(--pp-clr-border);
   

  }
 
  .pp-event-service-details-main {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .pp-event-service-title {
    font-size: var(--pp-font-heading5);
    font-weight: 600;
    margin-bottom: var(--pp-sp-10);
  }
 
  .pp-event-service-meta {
    display: grid;
    gap: var(--pp-sp-4);
    font-size: var(--pp-font-extra-small);
    color: var(--pp-clr-text-main);
    line-height: var(--pp-sp-22);
  }
  .pp-event-service-vendor {
    font-size: var(--pp-font-extra-small);
    color: var(--pp-clr-text-main);
    font-weight: 600;
  }
  .pp-event-service-price {
    font-size: var(--pp-font-extra-small);
    
    color: var(--pp-clr-secondary);
    
    white-space: nowrap;
  }
  .pp-event-row-nobook {
    display: flex;
    align-items: center;
    font-size: var(--pp-font-extra-small);
    color: var(--pp-clr-text-gray);
    gap: var(--pp-sp-12);
  }
  .pp-event-row-noservice {
    font-size: var(--pp-font-heading5  );
    font-style: italic;
  }
  .pp-event-row-booknow {
    
    font-weight: 600;
    cursor: pointer;
    font-size: var(--pp-font-heading5);
    margin-left: var(--pp-sp-8);
    text-decoration: underline;
  }
  


  /* Keep your existing media queries, and add below */

@media (max-width: 800px) {
  .pp-event-row {
    flex-direction: column;   /* stack event details + services vertically */
    align-items: stretch;
  }
  .pp-event-row-title{
    font-size: var(--pp-font-heading5);
  }
  .pp-event-row-left-main {
    min-width: unset;
    width: 100%;
    padding-top: var(--pp-sp-8);
  }

  .pp-event-row-right-main {
    width: 100%;
    min-width: unset;
  }

  .pp-event-service-row {
    flex-direction: row;
    align-items: flex-start;
    padding: var(--pp-sp-12);
  }

 

  .pp-event-service-details-main {
    flex: 1;
  }
}
@media (max-width: 600px) {
  .pp-event-row-day{
    font-size: var(--pp-font-heading2);
  }
  .pp-event-service-details{
    gap: var(--pp-sp-20);
    
  }
  
}

@media (max-width: 480px) {
  .pp-event-service-details {
    gap: var(--pp-sp-16);
    display: grid;
  }
  .pp-event-service-img ,.pp-event-service-img-three,.pp-event-service-img-one{
    width: 115px;
    height: 85px;
  }
 
    
  .pp-event-row-dateblock{
    min-width: unset;
  }
  .pp-event-service-row{
    padding: 0;
  }

}

@media (max-width: 350px) {
  .pp-event-service-details {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--pp-sp-4);
  }

  .pp-event-service-price {
    font-size: 12px;
  }
  .pp-event-service-img ,.pp-event-service-img-three,.pp-event-service-img-one{
    width: 95px;
    height: 65px;
  }
  .pp-event-service-title,.pp-event-row-booknow,.pp-event-row-noservice {
    font-size: var(--pp-font-small-font);
  }
  .pp-my-events-title{
    width: 100%;
  }


  .pp-event-row-dateblock {
    min-width: 40px;
    padding: var(--pp-sp-4);
  }
}

@media (max-width: 280px) {
  .pp-my-events-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--pp-sp-12);
  }

  .pp-event-row {
    padding: var(--pp-sp-8);
  }
  
  .pp-event-row-title {
    font-size: 14px;
  }

  .pp-event-service-img {
    width: 40px;
    height: 40px;
  }

  .pp-event-service-meta span {
    font-size: 12px;
  }
}

  