import React, { useState } from "react";
import CustomSelect from "../../components/CustomeSelect";
import "../../styles/CheckoutEventForm.css";

// Dummy options for demo
const US_STATES = [
  { value: "UT", label: "UT" },
  { value: "CA", label: "CA" },
  { value: "TX", label: "TX" },
  // ...add rest needed
];

export default function CheckoutEventForm() {
  // Minimal local state for inputs
  const [form, setForm] = useState({
    // main event selectors
    eventName: "",
    numPeople: "",
    street: "",
    apt: "",
    city: "",
    state: "",
    zip: "",
    contactFirst: "",
    contactLast: "",
    contactPhone: "",
    contactEmail: "",
    company: "",
    isOutdoor: false,
    tipJars: false,
    details: "",
  });

  const onChange = (e) => {
    const { name, value, type, checked } = e.target;
    setForm((f) => ({
      ...f,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  return (
    <section className="pp-event-checkout-form">
      {/* --- Heading section --- */}
      <h2 className="pp-event-checkout-form__heading">
        What event is this for?
      </h2>

      {/* --- "My Events" row --- */}
      <div className="pp-event-checkout-form__group-row">
        <label className="pp-form-label">My Events</label>
        <div className="pp-event-checkout-form__select-wrap">
          <CustomSelect
           
            value={"new"}
            onChange={() => {}}
            options={[{ label: "Create a new event", value: "new" }]}
            disabled
          />
        </div>
      </div>

      {/* Divider */}
      <div className="pp-event-checkout-form__divider" aria-hidden />

      {/* --- Create new event section --- */}
      <h3 className="pp-event-checkout-form__section-title">
        Create a new event
      </h3>

      <form className="pp-event-checkout-form__form" autoComplete="off">
        {/* ========== First Row ========== */}
        <div className="pp-event-checkout-form__2col">
          <div className="pp-event-checkout-form__group">
            <label className="pp-form-label">
              Event Name <span className="pp-form-required">*</span>
            </label>
            <input
              type="text"
              className="pp-form-input"
              name="eventName"
              value={form.eventName}
              onChange={onChange}
              placeholder="Name your event"
              required
            />
          </div>
          <div className="pp-event-checkout-form__group">
            <label className="pp-form-label ">
              Number of People Expected{" "}
              <span className="pp-event-checkout-form__required">*</span>
            </label>
            <input
              type="number"
              min="1"
              className="pp-form-input !w-[40%]"
              name="numPeople"
              value={form.numPeople}
              onChange={onChange}
              placeholder="e.g. 100"
              required
            />
          </div>
        </div>
        {/* ========== Address Row ========== */}
        <div className="pp-event-checkout-form__2col !flex">
          <div className="pp-event-checkout-form__group sm:w-[30%]">
            <label className="pp-form-label">
              Event Street Address{" "}
              <span className="pp-event-checkout-form__required">*</span>
            </label>
            <input
              type="text"
              className="pp-form-input"
              name="street"
              value={form.street}
              onChange={onChange}
              placeholder="123 Party Lane"
              required
            />
          </div>
          <div className="pp-event-checkout-form__group sm:w-[30%]">
            <label className="pp-form-label">
              Apt, suite, unit etc. (optional)
            </label>
            <input
              type="text"
              className="pp-form-input"
              name="apt"
              value={form.apt}
              onChange={onChange}
              placeholder="#2A"
            />
          </div>
        </div>

        {/* ========== City / State / Zip ========== */}
        <div className="pp-event-checkout-form__3col !flex">
          <div className="pp-event-checkout-form__group !w-[30%]">
            <label className="pp-form-label">
              Town / City{" "}
              <span className="pp-event-checkout-form__required">*</span>
            </label>
            <input
              type="text"
              className="pp-form-input"
              name="city"
              value={form.city}
              onChange={onChange}
              placeholder=""
              required
            />
          </div>
          <div className="pp-event-checkout-form__group">
            <label className="pp-form-label">
              State <span className="pp-event-checkout-form__required">*</span>
            </label>
            <CustomSelect
             
              value={form.state}
              onChange={(val) => setForm((f) => ({ ...f, state: val }))}
              options={[{ label: "Select", value: "" }, ...US_STATES]}
            />
          </div>
          <div className="pp-event-checkout-form__group">
            <label className="pp-form-label">
              Postcode / ZIP{" "}
              <span className="pp-event-checkout-form__required">*</span>
            </label>
            <input
              type="text"
              className="pp-form-input"
              name="zip"
              value={form.zip}
              onChange={onChange}
              placeholder=""
              required
            />
          </div>
        </div>

        {/* Country Row */}
        <div className="pp-event-checkout-form__group-row">
          <label className="pp-form-label">Country / Region*</label>
          <div className="pp-event-checkout-form__country-static">
            United States (US)
          </div>
        </div>

        {/* ========== Contact Info ========== */}
        <div className="pp-event-checkout-form__2col">
          <div className="pp-event-checkout-form__group">
            <label className="pp-form-label">
              Point of Contact First Name{" "}
              <span className="pp-event-checkout-form__required">*</span>
            </label>
            <input
              type="text"
              className="pp-form-input"
              name="contactFirst"
              value={form.contactFirst}
              onChange={onChange}
              placeholder=""
              required
            />
          </div>
          <div className="pp-event-checkout-form__group">
            <label className="pp-form-label">
              Point of Contact Last Name (optional)
            </label>
            <input
              type="text"
              className="pp-form-input"
              name="contactLast"
              value={form.contactLast}
              onChange={onChange}
              placeholder=""
            />
          </div>
        </div>
        <div className="pp-event-checkout-form__2col !grid-cols-1">
          <div className="pp-event-checkout-form__group sm:!w-[49%]">
            <label className="pp-form-label">
              Point of Contact Cell Phone{" "}
              <span className="pp-event-checkout-form__required">*</span>
            </label>
            <input
              type="tel"
              className="pp-form-input"
              name="contactPhone"
              value={form.contactPhone}
              onChange={onChange}
              placeholder=""
              required
            />
          </div>
          <div className="pp-event-checkout-form__group sm:w-[49%]">
            <label className="pp-form-label">
              Point of Contact Cell Email{" "}
              <span className="pp-event-checkout-form__required">*</span>
            </label>
            <input
              type="email"
              className="pp-form-input"
              name="contactEmail"
              value={form.contactEmail}
              onChange={onChange}
              placeholder=""
              required
            />
          </div>
        </div>

        {/* Company Name */}
        <div className="pp-event-checkout-form__group-row sm:w-[49%]">
          <label className="pp-form-label">Company Name (optional)</label>
          <input
            type="text"
            className="pp-form-input"
            name="company"
            value={form.company}
            onChange={onChange}
            placeholder=""
          />
        </div>

        {/* Is event outdoors */}
        <div className="pp-event-checkout-form__2col-no-space">
          <div className="pp-event-checkout-form__group pp-event-checkout-form__small-group">
            <label className="pp-form-label">
              Is the event outdoors?{" "}
              <span className="pp-event-checkout-form__required">*</span>
            </label>
            <div className="pp-event-checkout-form__radio-wrap">
              <button
                className={`pp-event-checkout-form__radio-btn${
                  form.isOutdoor ? " selected" : ""
                }`}
                type="button"
                onClick={() => setForm((f) => ({ ...f, isOutdoor: true }))}
                aria-pressed={form.isOutdoor}
              >
                Yes
              </button>
              <button
                className={`pp-event-checkout-form__radio-btn${
                  !form.isOutdoor ? " selected" : ""
                }`}
                type="button"
                onClick={() => setForm((f) => ({ ...f, isOutdoor: false }))}
                aria-pressed={!form.isOutdoor}
              >
                No
              </button>
            </div>
          </div>
          <div className="pp-event-checkout-form__group pp-event-checkout-form__small-group">
            <label className="pp-form-label">
              Are Tip Jars Allowed? (optional)
            </label>
            <div className="pp-event-checkout-form__radio-wrap">
              <button
                type="button"
                className={`pp-event-checkout-form__radio-btn${
                  form.tipJars ? " selected" : ""
                }`}
                onClick={() => setForm((f) => ({ ...f, tipJars: true }))}
                aria-pressed={form.tipJars}
              >
                Yes
              </button>
              <button
                type="button"
                className={`pp-event-checkout-form__radio-btn${
                  !form.tipJars ? " selected" : ""
                }`}
                onClick={() => setForm((f) => ({ ...f, tipJars: false }))}
                aria-pressed={!form.tipJars}
              >
                No
              </button>
            </div>
          </div>
        </div>

        {/* Additional Details */}
        <div className="pp-event-checkout-form__group-row !w-[100%]">
          <label className="pp-form-label">Additional Details</label>
          <textarea
            className="pp-form-input !h-[150px] "
            name="details"
            value={form.details}
            onChange={onChange}
            rows={3}
            placeholder="Any details we should know?"
          />
        </div>

        {/* --- Navigation buttons --- */}
        <div className="pp-event-checkout-form__footer">
          <button className="pp-btn pp-btn-secondary" type="button">
            Previous
          </button>
          <button className="pp-btn pp-btn-primary" type="submit">
            Next
          </button>
        </div>
      </form>
    </section>
  );
}
