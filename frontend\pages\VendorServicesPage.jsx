import React, { useState } from "react";
import CustomSelect from "../components/CustomeSelect";
import { useDispatch } from "react-redux";
import {
  openCreateModal,
  setSelectedCategory,
  setActiveTab,
} from "../redux/createServiceSlice";
import { FiSearch, FiChevronDown } from "react-icons/fi";
import "../styles/VendorServicesPage.css";
import AddServices from "../components/AddServicesModal";

// Local placeholder images for services
import img88 from "../src/assets/images/servicesimages/Rectangle 88.svg";
import img101 from "../src/assets/images/servicesimages/Rectangle 101.svg";
import img102 from "../src/assets/images/servicesimages/Rectangle 102.svg";
import img103 from "../src/assets/images/servicesimages/Rectangle 103.svg";
import img104 from "../src/assets/images/servicesimages/Rectangle 104.svg";
import img105 from "../src/assets/images/servicesimages/Rectangle 105.svg";
import img106 from "../src/assets/images/servicesimages/Rectangle 106.svg";
import img107 from "../src/assets/images/servicesimages/Rectangle 107.svg";

const localImages = [
  img88,
  img101,
  img102,
  img103,
  img104,
  img105,
  img106,
  img107,
];

const services = [
  {
    id: 1,
    avatar: localImages[0 % localImages.length],
    name: "Batman",
    status: "Live",
    fee: "$240",
  },
  {
    id: 2,
    avatar: localImages[1 % localImages.length],
    name: "Robin",
    status: "Live",
    fee: "$240",
  },
  {
    id: 3,
    avatar: localImages[2 % localImages.length],
    name: "Superman",
    status: "Live",
    fee: "$240",
  },
  {
    id: 4,
    avatar: localImages[3 % localImages.length],
    name: "Wonder Woman",
    status: "Live",
    fee: "$240",
  },
  {
    id: 5,
    avatar: localImages[4 % localImages.length],
    name: "Thor",
    status: "Draft",
    fee: "$240",
  },
  {
    id: 6,
    avatar: localImages[5 % localImages.length],
    name: "Ironman",
    status: "Live",
    fee: "$240",
  },
  {
    id: 7,
    avatar: localImages[6 % localImages.length],
    name: "Captain Marvel",
    status: "In Review",
    fee: "$240",
  },
  {
    id: 8,
    avatar: localImages[7 % localImages.length],
    name: "Luke Skywalker",
    status: "Live",
    fee: "$240",
  },
  {
    id: 9,
    avatar: localImages[8 % localImages.length] || localImages[0],
    name: "Storm Trooper",
    status: "Live",
    fee: "$240",
  },
];

export default function ServiceListPage() {
  const dispatch = useDispatch();
  const [filter, setFilter] = useState("All");
  const [search, setSearch] = useState("");
  const [isAddOpen, setIsAddOpen] = useState(false);

  const filteredServices = services.filter(
    (s) =>
      (filter === "All" || s.status === filter) &&
      s.name.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <>
      <div className="srv-root">
        <div className="srv-toolbar">
          <div className="srv-toolbar-left">
            <CustomSelect
              value={filter}
              onChange={(val) => setFilter(val)}
              options={["All", "Live", "Draft", "In Review"]}
            />
            <div className="srv-search">
              <FiSearch className="srv-search-icon" />
              <input
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                placeholder="Search"
                aria-label="Search services"
              />
            </div>
          </div>
          <button
            type="button"
            className="srv-add-btn"
            onClick={() => setIsAddOpen(true)}
          >
            Add Service
          </button>
        </div>
        <div className="srv-table">
          <div className="srv-table-head srv-table-row">
            <div className="srv-col srv-col-avatar">
              Bookable <FiChevronDown className="srv-table-caret" />
            </div>
            <div className="srv-col srv-col-status">Status</div>
            <div className="srv-col srv-col-fee">
              Fee <FiChevronDown className="srv-table-caret" />
            </div>
          </div>
          {filteredServices.map((s) => (
            <div key={s.id} className="srv-table-row" tabIndex={0}>
              <div className="srv-col srv-col-avatar">
                <img
                  src={s.avatar}
                  alt={s.name}
                  className="srv-avatar"
                  loading="lazy"
                  decoding="async"
                />
                <span className="srv-name">{s.name}</span>
              </div>
              <div className="srv-col srv-col-status">{s.status}</div>
              <div className="srv-col srv-col-fee">{s.fee}</div>
            </div>
          ))}
        </div>
      </div>
      <AddServices
        isOpen={isAddOpen}
        onClose={() => setIsAddOpen(false)}
        onSelect={(value) => {
          // Map AddServices values directly to categoryConfig categories
          const category = value; // entertainment, music, food, rental
          setIsAddOpen(false);
          dispatch(setSelectedCategory(category));
          dispatch(setActiveTab("General"));
          dispatch(openCreateModal());
        }}
      />
    </>
  );
}
