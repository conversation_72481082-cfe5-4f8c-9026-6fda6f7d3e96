.vendor-layout {
  padding-top: var(--pp-sp-40);
  padding-bottom: var(--pp-sp-40);
  background: var(--pp-clr-bg);
}

/* When chat is full-screen on mobile, remove outer paddings to allow the page
   to be taken over by the chat page layout */
.vendor-layout--chat-full {
  padding-top: 0;
  padding-bottom: 0;
}

.vendor-layout__nav-row {
  display: flex;
  border-bottom: 1px solid var(--pp-clr-border-light);
}

.vendor-layout__title {
  font-family: var(--pp-font-Metro-Sans);
  font-size: var(--pp-font-heading4);
  color: var(--pp-clr-text-main);
  margin-bottom: var(--pp-sp-20);
  font-weight: 400;
}

.vendor-layout__tabs {
  display: flex;
  flex-wrap: wrap;
  gap: var(--pp-sp-20);
  font-size: var(--pp-font-base2-font);
  color: var(--pp-clr-text-gray);
  border-bottom: 1px solid var(--pp-clr-border-light);
  align-items: center;
}

.vendor-layout__tab {
  background: none;
  border: none;
  font-family: var(--pp-font-Metro-Sans);
  color: var(--pp-clr-secondary);
  font-size: var(--pp-font-extra-small);
  padding: var(--pp-sp-8) var(--pp-sp-8) var(--pp-sp-12);
  cursor: pointer;
  border-bottom: 1px solid transparent;
  transition: color 300ms ease, border-bottom 300ms ease;
  text-decoration: none;
}

.vendor-layout__tab--active {
  color: var(--pp-clr-text-main);
  border-bottom: 3px solid var(--pp-clr-secondary);
  font-weight: 500;
}

.vendor-layout__link {
  margin-left: auto;
  color: var(--pp-clr-secondary);
  font-size: var(--pp-font-extra-small);
  text-decoration: none;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* === RESPONSIVE === */
@media (max-width: 1024px) {
  .vendor-layout__nav-row {
    border-bottom: none;
  }

  .vendor-layout__tabs {
    display: none;
  }

  .vendor-layout__dropdown,
  .custom-select-wrapper.vendor-layout__dropdown {
    display: block;
  }
}

/* Hide vendor shell on small screens for chat full view */
@media (max-width: 768px) {
  body.pp-chat-active .vendor-layout {
    padding: 0;
  }
  body.pp-chat-active .vendor-layout__title,
  body.pp-chat-active .vendor-layout__nav-row {
    display: none;
  }
}
@media (max-width: 412px) {
  .vendor-layout__title {
    margin-bottom: var(--pp-sp-10);
  }
}
