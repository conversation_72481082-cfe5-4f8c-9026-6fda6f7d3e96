import React, { useState } from "react";
import CustomSelect from "../components/CustomeSelect";
import "../styles/VendorDashboard.css";
import arrow from "../src/assets/images/Browse-category.svg";
import { IoNotificationsSharp } from "react-icons/io5";
import { IoMdClose } from "react-icons/io";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";

// Chart data
const chartData = {
  Week: [
    { date: "Mon", sales: 120 },
    { date: "Tue", sales: 200 },
    { date: "Wed", sales: 180 },
    { date: "Thu", sales: 250 },
    { date: "Fri", sales: 300 },
    { date: "Sat", sales: 400 },
    { date: "Sun", sales: 350 },
  ],
  Month: [
    { date: "May 04", sales: 250 },
    { date: "May 06", sales: 350 },
    { date: "May 08", sales: 450 },
    { date: "May 13", sales: 450 },
    { date: "May 14", sales: 600 },
    { date: "May 16", sales: 600 },
  ],
  Year: [
    { date: "Jan", sales: 1000 },
    { date: "Feb", sales: 1200 },
    { date: "Mar", sales: 1500 },
    { date: "Apr", sales: 1700 },
    { date: "May", sales: 2000 },
    { date: "Jun", sales: 2300 },
    { date: "Jul", sales: 2500 },
    { date: "Aug", sales: 2600 },
    { date: "Sep", sales: 2800 },
    { date: "Oct", sales: 3000 },
    { date: "Nov", sales: 3200 },
    { date: "Dec", sales: 3500 },
  ],
};

// Reusable row component
const StatRow = ({ rows }) => (
  <div className="dashboard-main__card-section">
    {rows.map(({ label, value }) => (
      <div className="dashboard-main__booking-row" key={label}>
        <span>{label}</span>
        <span>{value}</span>
      </div>
    ))}
  </div>
);

// Card config for metrics and widgets
const metrics = [
  { label: "Total Sales", value: "$12,372" },
  { label: "My Balance", value: "$230" },
  { label: "Upcoming Bookings", value: 4 },
  { label: "Live Services", value: 7 },
];

const widgetCards = [
  {
    className: "dashboard-main__card--bookings",
    title: "Bookings",
    rows: [
      { label: "Lifetime", value: 23 },
      { label: "Completed", value: 19 },
      { label: "Pending", value: 0 },
      { label: "Processing", value: 1 },
      { label: "Cancelled", value: 0 },
      { label: "Refunded", value: 0 },
      { label: "On hold", value: 0 },
    ],
  },
  {
    className: "dashboard-main__card--sales",
    title: "Sales",
    isSales: true,
  },
  {
    className: "dashboard-main__card--next dashboard-main__card--green",
    title: "Next Booking",
    isNext: true,
  },
  {
    className:
      "dashboard-main__card--reviews dashboard-main__card--border-blue",
    title: "Reviews",
    rows: [
      { label: "Total", value: 5 },
      { label: "Positive", value: 4 },
      { label: "Negative", value: 1 },
      { label: "Replied", value: 1 },
    ],
  },
  {
    className: "dashboard-main__card--services",
    title: "Services",
    rows: [
      { label: "Total", value: 8 },
      { label: "Live", value: 7 },
      { label: "Offline", value: 0 },
      { label: "Pending Review", value: 1 },
    ],
  },
];

const DashboardMain = () => {
  const [period, setPeriod] = useState("Month");

  return (
    <div className="dashboard-main ">
      <div className="dashboard-main__info-banner">
        <span className="dashboard-banner-span">
          <IoNotificationsSharp /> Level up your store with a banner
        </span>
        <IoMdClose className="font-semibold text-base cursor-pointer" />
      </div>

      <div className="dashboard-main__metrics">
        {metrics.map(({ label, value }) => (
          <div key={label} className="w-full">
            <p className="dashboard-main__metric-label">{label}</p>
            <p className="dashboard-main__metric-value">{value}</p>
          </div>
        ))}
      </div>

      <div className="dashboard-main__widgets">
        {widgetCards.map((card, idx) => {
          if (card.isSales)
            return (
              <div
                className={`dashboard-main__card ${card.className}`}
                key={card.title}
              >
                <div className="dashboard-main__card-row">
                  <div className="dashboard-main__card-title">{card.title}</div>
                  <CustomSelect
                    className="dashboard-main__period-select"
                    value={period}
                    onChange={setPeriod}
                    options={[
                      { value: "Week", label: "This Week" },
                      { value: "Month", label: "This Month" },
                      { value: "Year", label: "This Year" },
                    ]}
                  />
                </div>
                <div className="SalesReport__chart-placeholder">
                  <ResponsiveContainer width="100%" height={200}>
                    <LineChart data={chartData[period]}>
                      <CartesianGrid
                        strokeDasharray="3 3"
                        vertical={false}
                        stroke="#e5e5e5"
                      />
                      <XAxis
                        dataKey="date"
                        tick={{ fontSize: 12 }}
                        axisLine={false}
                      />
                      <YAxis tick={{ fontSize: 12 }} axisLine={false} />
                      <Tooltip />
                      <Line
                        type="stepAfter"
                        dataKey="sales"
                        stroke="#00B75F"
                        strokeWidth={2}
                        dot={{ r: 4 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>
            );
          if (card.isNext)
            return (
              <div
                className={`dashboard-main__card ${card.className}`}
                key={card.title}
              >
                <div className="dashboard-main__card-title">{card.title}</div>
                <div className="dashboard-main__next-booking">
                  <span className="dashboard-main__next-booking__title">
                    Batman
                  </span>
                  <span className="dashboard-main__next-booking__details">
                    Jun 12 2023 | 2pm – 3:30pm
                  </span>
                  <span className="dashboard-main__next-booking__location">
                    Lehi, Utah
                  </span>
                  <img
                    src={arrow}
                    alt="arrow"
                    className="dashboard-main__next-booking__arrow"
                  />
                </div>
              </div>
            );
          return (
            <div
              className={`dashboard-main__card ${card.className}`}
              key={card.title}
            >
              <div className="dashboard-main__card-title">{card.title}</div>
              <StatRow rows={card.rows} />
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default DashboardMain;
