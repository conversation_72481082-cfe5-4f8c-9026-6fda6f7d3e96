/* =========================
   📊 Availability Inputs
   ========================= */

.ai-availability-inputs {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-24);
}

.ai-form-group {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-12);
}

.ai-form-label {
  font-size: var(--pp-font-extra-small);
  font-weight: 500;
  color: var(--pp-clr-text-main);
  margin: 0;
}

.ai-description {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  margin: 0;
  line-height: 1.5;
}

.ai-form-input {
  width: 100%;
  padding: var(--pp-sp-12) var(--pp-sp-16);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-8);
  font-size: var(--pp-font-extra-small);
  font-family: var(--pp-font-Metro-Sans);
  background: var(--pp-clr-primary);
  transition: all 0.3s ease;
  color: var(--pp-clr-text-main);
  max-width: 74px;
}

.ai-form-input:focus {
  outline: none;
  border-color: var(--pp-clr-secondary);
}

.ai-form-input::placeholder {
  color: var(--pp-clr-text-gray);
}

/* Input with Unit */
.ai-input-with-unit {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8);
}

.ai-input-with-unit .ai-form-input {
  flex: 1;
}

/* Duration Inputs Row */
.ai-duration-inputs-row {
  display: flex;
  gap: var(--pp-sp-16);
  align-items: center;
}

.ai-input-group {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-8);
  min-width: 120px;
}

.ai-input-group .ai-unit {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  text-align: center;
  font-weight: 500;
}

/* Time Inputs */
.ai-time-inputs {
  display: flex;
  gap: var(--pp-sp-16);
  align-items: center;
}
.ai-time-inputs .ai-form-input {
  width: fit-content;
  max-width: 100%;
}
.ai-time-input {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-8);
  min-width: 120px;
}

.ai-time-label {
  font-size: var(--pp-font-extra-small);
  font-weight: 500;
  color: var(--pp-clr-text-main);
  margin: 0;
}

/* Days Grid */
.ai-days-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: var(--pp-sp-8);
}

.ai-day-checkbox {
  display: flex;

  align-items: center;
  gap: var(--pp-sp-8);
  cursor: pointer;
  user-select: none;
}

.ai-checkbox {
  position: relative;
  width: var(--pp-sp-20);
  height: var(--pp-sp-20);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-4);
  background: var(--pp-clr-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.ai-checkbox:hover {
  border-color: var(--pp-clr-text-gray);
  transform: scale(1.05);
}

.ai-checkbox:focus {
  outline: none;
  border-color: var(--pp-clr-secondary);
}

.ai-checkbox:checked {
  background-color: var(--pp-clr-secondary) !important;
  border-color: var(--pp-clr-secondary) !important;
}

.ai-checkbox:checked::after {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: var(--pp-sp-12);
  height: var(--pp-sp-12);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.ai-day-label {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-main);
  font-weight: 500;
  text-align: center;
  cursor: pointer;
}

/* Checkboxes */
.ai-checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-12);
  cursor: pointer;
  user-select: none;
}

.ai-checkbox-wrapper .ai-checkbox {
  margin: 0;
}

.ai-checkbox-label {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-main);
  font-weight: 500;
  cursor: pointer;
}

/* Responsive */
@media (max-width: 768px) {
  .ai-availability-inputs {
    gap: var(--pp-sp-20);
  }

  .ai-form-group {
    gap: var(--pp-sp-10);
  }

  .ai-duration-inputs-row {
    gap: var(--pp-sp-12);
  }

  .ai-time-inputs {
    gap: var(--pp-sp-12);
  }

  .ai-input-group {
    min-width: 100px;
  }

  .ai-time-input {
    min-width: 100px;
  }
}

@media (max-width: 480px) {
  .ai-availability-inputs {
    gap: var(--pp-sp-16);
  }

  .ai-form-group {
    gap: var(--pp-sp-8);
  }

  .ai-duration-inputs-row {
    flex-direction: column;
    gap: var(--pp-sp-8);
    align-items: stretch;
  }

  .ai-time-inputs {
    flex-direction: column;
    gap: var(--pp-sp-8);
    align-items: stretch;
  }

  .ai-input-group {
    min-width: auto;
  }

  .ai-time-input {
    min-width: auto;
  }

  .ai-form-input {
    padding: var(--pp-sp-10) var(--pp-sp-12);
  }
}
input[type="time"]::-webkit-datetime-edit-fields-wrapper {
  color: #333;
}
