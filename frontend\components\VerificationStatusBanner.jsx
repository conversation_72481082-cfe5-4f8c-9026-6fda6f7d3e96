import React from 'react';
import { FiAlertTriangle, FiCheckCircle, FiClock, FiXCircle, FiRefreshCw } from 'react-icons/fi';

/**
 * VerificationStatusBanner Component
 * Displays the current verification status with appropriate styling and actions
 */
const VerificationStatusBanner = ({ onboardingStatus, onResolveClick, isPolling, pollingCount, onStartPolling }) => {
  if (!onboardingStatus || !onboardingStatus.onboardingCompleted) {
    return null;
  }

  const getStatusInfo = () => {
    const { requirements } = onboardingStatus;

    // Account suspended or disabled
    if (requirements?.disabled_reason) {
      return {
        type: 'error',
        icon: <FiXCircle className="text-red-500" size={20} />,
        title: 'Account Verification Failed',
        message: `Issue: ${requirements.disabled_reason}`,
        action: 'Resolve Issue',
        color: 'bg-red-50 border-red-200 text-red-800',
        buttonColor: 'bg-red-600 hover:bg-red-700'
      };
    }

    // Past due requirements
    if (requirements?.past_due?.length > 0) {
      return {
        type: 'error',
        icon: <FiAlertTriangle className="text-red-500" size={20} />,
        title: 'Past Due Requirements',
        message: `Required: ${requirements.past_due.join(', ')}`,
        action: 'Submit Now',
        color: 'bg-red-50 border-red-200 text-red-800',
        buttonColor: 'bg-red-600 hover:bg-red-700'
      };
    }

    // Currently due requirements
    if (requirements?.currently_due?.length > 0) {
      return {
        type: 'warning',
        icon: <FiAlertTriangle className="text-orange-500" size={20} />,
        title: 'Additional Verification Required',
        message: `Required: ${requirements.currently_due.join(', ')}`,
        action: 'Complete Verification',
        color: 'bg-orange-50 border-orange-200 text-orange-800',
        buttonColor: 'bg-orange-600 hover:bg-orange-700'
      };
    }

    // Pending verification (normal state)
    if (requirements?.pending_verification?.length > 0) {
      return {
        type: 'info',
        icon: isPolling ? <FiRefreshCw className="text-blue-500 animate-spin" size={20} /> : <FiClock className="text-blue-500" size={20} />,
        title: isPolling ? 'Verification in Progress (Auto-checking)' : 'Verification in Progress',
        message: isPolling 
          ? `Processing: ${requirements.pending_verification.join(', ')} (${pollingCount}/37)`
          : `Processing: ${requirements.pending_verification.join(', ')}`,
        action: isPolling ? null : 'Start Auto-check',
        color: 'bg-blue-50 border-blue-200 text-blue-800',
        buttonColor: 'bg-blue-600 hover:bg-blue-700'
      };
    }

    // Fully verified
    return {
      type: 'success',
      icon: <FiCheckCircle className="text-green-500" size={20} />,
      title: 'Account Fully Verified',
      message: 'Your account is verified and ready to accept payments',
      action: null,
      color: 'bg-green-50 border-green-200 text-green-800',
      buttonColor: null
    };
  };

  const statusInfo = getStatusInfo();

  return (
    <div className={`rounded-lg border p-4 ${statusInfo.color}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0 mr-3">
          {statusInfo.icon}
        </div>
        <div className="flex-1">
          <h3 className="text-sm font-medium mb-1">
            {statusInfo.title}
          </h3>
          <p className="text-sm mb-3">
            {statusInfo.message}
          </p>
          
          {/* Additional details based on status */}
          {statusInfo.type === 'error' && (
            <div className="text-xs mb-3 p-2 bg-red-100 rounded">
              <strong>⚠️ Action Required:</strong> Please resolve these issues immediately to avoid account restrictions.
            </div>
          )}
          
          {statusInfo.type === 'warning' && (
            <div className="text-xs mb-3 p-2 bg-orange-100 rounded">
              <strong>⚠️ Action Required:</strong> Complete verification to enable full account functionality.
            </div>
          )}
          
          {statusInfo.type === 'info' && (
            <div className="text-xs mb-3 p-2 bg-blue-100 rounded">
              <strong>ℹ️ Processing:</strong> Stripe is reviewing your documents. This usually takes 24-48 hours.
            </div>
          )}

          {/* Action button */}
          {statusInfo.action && (
            <button
              onClick={statusInfo.action === 'Start Auto-check' ? onStartPolling : onResolveClick}
              className={`px-4 py-2 text-white text-sm font-medium rounded-md transition-colors ${statusInfo.buttonColor}`}
            >
              {statusInfo.action}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default VerificationStatusBanner;