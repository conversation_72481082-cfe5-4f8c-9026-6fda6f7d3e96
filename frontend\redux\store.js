import { configureStore } from "@reduxjs/toolkit";
import servicesReducer from "./servicesSlice";
import uiReducer from "./uiSlice";
import authReducer from "./authSlice";
import vendorReducer from "./vendorSlice";
import { authMiddleware } from "./authMiddleware";
import createServiceReducer from "./createServiceSlice";

const store = configureStore({
	reducer: {
		services: servicesReducer,
		ui: uiReducer,
		auth: authReducer,
		vendor: vendorReducer,
		createService: createServiceReducer,
	},
	middleware: (getDefaultMiddleware) =>
		getDefaultMiddleware({
			serializableCheck: {
				ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
				createService: createServiceReducer,
			},
		}).concat(authMiddleware),
});

export default store;

