.pp-my-events-root {
    width: 100%;
    background: var(--pp-clr-primary);
    color: var(--pp-clr-text-main);
    font-family: var(--pp-font-Metro-Sans);
    margin-top: var(--pp-sp-24);
    margin-bottom: var(--pp-sp-60);
    padding-bottom: var(--pp-sp-24);
  }
  .pp-my-events-header {
    display: flex;
    border-bottom: 1px solid var(--pp-clr-border);
    padding-bottom: var(--pp-sp-24);
    align-items: center;
    gap: var(--pp-sp-24);
    margin-bottom: var(--pp-sp-24);
  }
  .pp-my-events-title {
    font-size: var(--pp-font-heading4);
    font-weight: 600;
    width: 30.5%;
    
  }

  .pp-my-events-create {
    font-size: var(--pp-font-extra-small);
    padding: var(--pp-sp-8) var(--pp-sp-20);
  }
  .pp-my-events-list {
    width: 100%;
    margin-bottom: var(--pp-sp-60);
  }
  .pp-my-events-divider {
    width: 100%;
    height: 1px;
    background: var(--pp-clr-border);
    margin: var(--pp-sp-12) 0;
  }
  .pp-my-events-actions{
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: var(--pp-sp-12);
  }
  @media (max-width: 600px) {
    .pp-my-events-header {
      flex-direction: column;
      align-items: stretch;
      gap: var(--pp-sp-12);
    }
  }
  