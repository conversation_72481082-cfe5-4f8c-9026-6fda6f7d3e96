import React, { useEffect, useMemo, useRef, useState } from "react";

import "../styles/CustomeSelect.css";

// Accessible custom select with keyboard support
// Props: label, value, onChange, options=[{label, value}], placeholder, className, leftIcon, rightIcon
const CustomSelect = ({
  label,
  value,
  onChange,
  options = [],
  placeholder = "Select...",
  className = "",
  disabled = false,
  id,
  leftIcon = null,
  rightIcon = null,
}) => {
  const [open, setOpen] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const wrapperRef = useRef(null);
  const buttonRef = useRef(null);
  const listRef = useRef(null);

  const normalizedOptions = useMemo(
    () =>
      options.map((opt) =>
        typeof opt === "string"
          ? { label: opt, value: opt }
          : { label: opt.label, value: opt.value }
      ),
    [options]
  );

  const selectedOption = useMemo(
    () => normalizedOptions.find((o) => o.value === value) || null,
    [normalizedOptions, value]
  );

  // Close dropdown if clicked outside
  useEffect(() => {
    const handleClickOutside = (e) => {
      if (wrapperRef.current && !wrapperRef.current.contains(e.target)) {
        setOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Manage keyboard interactions
  const onKeyDownButton = (e) => {
    if (disabled) return;
    switch (e.key) {
      case " ":
      case "Enter":
      case "ArrowDown":
      case "ArrowUp":
        e.preventDefault();
        setOpen(true);
        setFocusedIndex(
          Math.max(
            0,
            selectedOption
              ? normalizedOptions.findIndex(
                  (o) => o.value === selectedOption.value
                )
              : 0
          )
        );
        break;
      default:
        break;
    }
  };

  const onKeyDownList = (e) => {
    if (!open) return;
    switch (e.key) {
      case "Escape":
        setOpen(false);
        buttonRef.current?.focus();
        break;
      case "ArrowDown":
        e.preventDefault();
        setFocusedIndex((i) => Math.min(i + 1, normalizedOptions.length - 1));
        break;
      case "ArrowUp":
        e.preventDefault();
        setFocusedIndex((i) => Math.max(i - 1, 0));
        break;
      case "Home":
        e.preventDefault();
        setFocusedIndex(0);
        break;
      case "End":
        e.preventDefault();
        setFocusedIndex(normalizedOptions.length - 1);
        break;
      case "Enter":
      case " ":
        e.preventDefault();
        if (focusedIndex >= 0 && normalizedOptions[focusedIndex]) {
          onChange(normalizedOptions[focusedIndex].value);
          setOpen(false);
          buttonRef.current?.focus();
        }
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (!open) return;
    const active = listRef.current?.querySelector(
      '[role="option"][aria-selected="true"]'
    );
    active?.scrollIntoView({ block: "nearest" });
  }, [open]);

  return (
    <div className={`custom-select-wrapper ${className}`} ref={wrapperRef}>
      {label && (
        <label className="custom-select-label" htmlFor={id}>
          {label}
        </label>
      )}

      <button
        id={id}
        type="button"
        className="custom-select-box"
        aria-haspopup="listbox"
        aria-expanded={open}
        onClick={() => !disabled && setOpen((p) => !p)}
        onKeyDown={onKeyDownButton}
        ref={buttonRef}
        disabled={disabled}
      >
        {/* Left Icon */}
        {leftIcon && (
          <span className="custom-select-left-icon">
            {leftIcon}
          </span>
        )}
        
        {/* Selected Value or Placeholder */}
        <span className="custom-select-text">
          {selectedOption ? selectedOption.label : placeholder}
        </span>
        
        {/* Right Icon (default dropdown arrow or custom icon) */}
        <span className="custom-select-right-icon">
          {rightIcon || (
            <svg 
              width="12" 
              height="8" 
              viewBox="0 0 12 8" 
              fill="none" 
              xmlns="http://www.w3.org/2000/svg"
              className={`custom-select-arrow ${open ? 'open' : ''}`}
            >
              <path 
                d="M1 1.5L6 6.5L11 1.5" 
                stroke="currentColor" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
            </svg>
          )}
        </span>
      </button>

      {open && (
        <ul
          className="custom-select-dropdown"
          role="listbox"
          aria-activedescendant={
            focusedIndex >= 0
              ? `custom-option-${normalizedOptions[focusedIndex].value}`
              : undefined
          }
          tabIndex={-1}
          onKeyDown={onKeyDownList}
          ref={listRef}
        >
          {normalizedOptions.map((option, index) => {
            const isSelected = option.value === value;
            const isFocused = index === focusedIndex;
            return (
              <li
                id={`custom-option-${option.value}`}
                key={option.value}
                role="option"
                aria-selected={isSelected}
                className={`custom-select-option ${
                  isSelected ? "selected" : ""
                } ${isFocused ? "focused" : ""}`}
                onMouseEnter={() => setFocusedIndex(index)}
                onClick={() => {
                  onChange(option.value);
                  setOpen(false);
                  buttonRef.current?.focus();
                }}
              >
                {option.label}
              </li>
            );
          })}
        </ul>
      )}
    </div>
  );
};

export default CustomSelect;
