import React from "react";
import CustomSelect from "../CustomeSelect";

export default function DurationInputs({ value = {}, onChange }) {
  const handleChange = (field, val) => {
    onChange({ ...value, [field]: val });
  };

  return (
    <div className="di-duration-inputs">
      <div className="di-form-row">
        <div className="di-form-group">
          <label className="di-form-label">Minimum Duration</label>
          <p className="di-description">
            Minimum amount of time a customer can book.
          </p>
          <div className="di-duration-inputs-row">
            <div className="di-input-group">
              <CustomSelect
                value={value.minHours || "1"}
                onChange={(val) => handleChange("minHours", val)}
                options={[
                  ...Array.from({ length: 24 }, (_, i) => ({
                    value: (i + 1).toString(),
                    label: (i + 1).toString()
                  }))
                ]}
                className="di-form-select"
              />
              <span className="di-unit">Hour(s) &</span>
            </div>
            <div className="di-input-group">
              <CustomSelect
                value={value.minMinutes || "0"}
                onChange={(val) => handleChange("minMinutes", val)}
                options={[
                  { value: "0", label: "0" },
                  ...Array.from({ length: 60 }, (_, i) => ({
                    value: (i + 1).toString(),
                    label: (i + 1).toString()
                  }))
                ]}
                className="di-form-select"
              />
              <span className="di-unit">Minutes</span>
            </div>
          </div>
        </div>
      </div>

      <div className="di-form-row">
        <div className="di-form-group">
          <label className="di-form-label">Maximum Duration</label>
          <p className="di-description">
            Maximum amount of time a customer can book.
          </p>
          <div className="di-duration-inputs-row">
            <div className="di-input-group">
              <CustomSelect
                value={value.maxHours || "4"}
                onChange={(val) => handleChange("maxHours", val)}
                options={[
                  ...Array.from({ length: 24 }, (_, i) => ({
                    value: (i + 1).toString(),
                    label: (i + 1).toString()
                  }))
                ]}
                className="di-form-select"
              />
              <span className="di-unit">Hour(s) &</span>
            </div>
            <div className="di-input-group">
              <CustomSelect
                value={value.maxMinutes || "0"}
                onChange={(val) => handleChange("maxMinutes", val)}
                options={[
                  { value: "0", label: "0" },
                  ...Array.from({ length: 60 }, (_, i) => ({
                    value: (i + 1).toString(),
                    label: (i + 1).toString()
                  }))
                ]}
                className="di-form-select"
              />
              <span className="di-unit">Minutes</span>
            </div>
          </div>
        </div>
      </div>

      <div className="di-form-row">
        <div className="di-form-group">
          <label className="di-form-label">Default Starting Duration</label>
          <p className="di-description">
            Default amount of time selected on your service page.
          </p>
          <div className="di-duration-inputs-row">
            <div className="di-input-group">
              <CustomSelect
                value={value.defaultHours || "1"}
                onChange={(val) => handleChange("defaultHours", val)}
                options={[
                  ...Array.from({ length: 24 }, (_, i) => ({
                    value: (i + 1).toString(),
                    label: (i + 1).toString()
                  }))
                ]}
                className="di-form-select"
              />
              <span className="di-unit">Hour(s) &</span>
            </div>
            <div className="di-input-group">
              <CustomSelect
                value={value.defaultMinutes || "30"}
                onChange={(val) => handleChange("defaultMinutes", val)}
                options={[
                  { value: "0", label: "0" },
                  ...Array.from({ length: 60 }, (_, i) => ({
                    value: (i + 1).toString(),
                    label: (i + 1).toString()
                  }))
                ]}
                className="di-form-select"
              />
              <span className="di-unit">Minutes</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
