import React, { useState } from "react";
import "../../styles/CustomerCreateEventModal.css";
import PopupModal from "../common/PopupModal";
import CustomSelect from "../CustomeSelect";
const initialState = {
  eventName: "",
  numberOfPeopleExpected: "",
  eventStreetAddress: "",
  aptSuiteUnitEtc: "",
  townCity: "",
  state: "UT",
  postcodeZip: "",
  countryRegion: "United States (US)",
  pointOfContactFirstName: "",
  pointOfContactLastName: "",
  pointOfContactCellPhone: "",
  pointOfContactCellEmail: "",
  companyName: "",
  isEventOutdoors: true,
  areTipJarsAllowed: false,
  additionalDetails: "",
};

export default function CustomerCreateEventModal({
  isOpen,
  onClose,
  eventTitle = "Create Event",
  onSubmit,
}) {
  const [form, setForm] = useState(initialState);

  const handleChange = (e) => {
    const { id, value, type, checked } = e.target;
    setForm((prev) => ({
      ...prev,
      [id]: type === "checkbox" ? checked : value
    }));
  };

  const handleRadioChange = (fieldName, value) => {
    setForm((prev) => ({ ...prev, [fieldName]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (onSubmit) onSubmit(form);
  };

  return (
    <PopupModal isOpen={isOpen} onClose={onClose}>
      <form className="CreateEventModal-form" onSubmit={handleSubmit} autoComplete="off">
        <div style={{ marginBottom: 'var(--pp-sp-16)' }}>
          <div className="CreateEventModal-title">{eventTitle}</div>
          <div className="CreateEventModal-status">Upcoming</div>
        </div>

        <div className="CreateEventModal-grid">



          <div className="CreateEventModal-colspan2">
            <div className="pp-form-row">
              <div className="pp-form-group">
                <label className="pp-form-label" htmlFor="eventName">
                  Event Name *
                </label>
                <input
                  id="eventName"
                  className="pp-form-input"
                  placeholder=""
                  required
                  value={form.eventName}
                  onChange={handleChange}
                />
              </div>

              <div className="pp-form-group">
                <label className="pp-form-label" htmlFor="numberOfPeopleExpected">
                  Number of People Expected *
                </label>
                <input
                  id="numberOfPeopleExpected"
                  className="pp-form-input !w-1/2"

                  placeholder=""
                  required
                  type="number"
                  value={form.numberOfPeopleExpected}
                  onChange={handleChange}
                />
              </div>
            </div>
            <div className="pp-form-row" style={{ display: 'flex' }}>
              <div className="pp-form-group sm:w-[35%]">
                <label className="pp-form-label" htmlFor="eventStreetAddress">
                  Event Street Address *
                </label>
                <input
                  id="eventStreetAddress"
                  placeholder="123 Party Lane"
                  className="pp-form-input"
                  required
                  value={form.eventStreetAddress}
                  onChange={handleChange}
                />
              </div>
              <div className="pp-form-group sm:w-[35%]">
                <label className="pp-form-label" htmlFor="aptSuiteUnitEtc">
                  Apt, suite, unit, etc. (optional)
                </label>
                <input
                  id="aptSuiteUnitEtc"
                  placeholder="#2A"
                  className="pp-form-input "
                  value={form.aptSuiteUnitEtc}
                  onChange={handleChange}
                />
              </div>
            </div>

            <div className="pp-form-row ">
              <div className="pp-form-group">
                <label className="pp-form-label" htmlFor="townCity">
                  Town / City *
                </label>
                <input
                  id="townCity"
                  placeholder=""
                  className="pp-form-input"
                  required
                  value={form.townCity}
                  onChange={handleChange}
                />
              </div>
              <div className="pp-form-group  w-fit">
                <label className="pp-form-label" htmlFor="state">
                  State *
                </label>
                

                <CustomSelect
                  id="state"
                 
                  required
                  value={form.state}
                  onChange={handleChange}
                  options={[
                    { value: 'UT', label: 'UT' },
                    // Add other states as needed
                  ]}
                  placeholder="Select state"
                />
              </div>
              <div className="pp-form-group">
                <label className="pp-form-label" htmlFor="postcodeZip">
                  Postcode / ZIP *
                </label>
                <input
                  id="postcodeZip"
                  placeholder=""
                  className="pp-form-input "
                  required
                  value={form.postcodeZip}
                  onChange={handleChange}
                />
              </div>
            </div>

            <div className="pp-form-group">
              <label className="pp-form-label" htmlFor="countryRegion">
                Country / Region *
              </label>
              <p>{form.countryRegion}</p>
            </div>
          </div>

          <div className="CreateEventModal-colspan2">
            <div className="pp-form-row">
              <div className="pp-form-group" >
                <label className="pp-form-label" htmlFor="pointOfContactFirstName">
                  Point of Contact First Name *
                </label>
                <input
                  id="pointOfContactFirstName"
                  placeholder=""
                  className="pp-form-input"
                  required
                  value={form.pointOfContactFirstName}
                  onChange={handleChange}
                />
              </div>
              <div className="pp-form-group">
                <label className="pp-form-label" htmlFor="pointOfContactLastName">
                  Point of Contact Last Name (optional)
                </label>
                <input
                  id="pointOfContactLastName"
                  placeholder=""
                  className="pp-form-input"
                  value={form.pointOfContactLastName}
                  onChange={handleChange}
                />
              </div>
            </div>

            <div className="pp-form-row !grid-cols-1 sm:w-[50%] ">
              <div className="pp-form-group ">
                <label className="pp-form-label" htmlFor="pointOfContactCellPhone">
                  Point of Contact Cell Phone *
                </label>
                <input
                  id="pointOfContactCellPhone"
                  placeholder=""
                  className="pp-form-input "
                  required
                  type="tel"
                  value={form.pointOfContactCellPhone}
                  onChange={handleChange}
                />
              </div>
              <div className="pp-form-group">
                <label className="pp-form-label" htmlFor="pointOfContactCellEmail">
                  Point of Contact Cell Email *
                </label>
                <input
                  id="pointOfContactCellEmail"
                  placeholder=""
                  className="pp-form-input"
                  required
                  type="email"
                  value={form.pointOfContactCellEmail}
                  onChange={handleChange}
                />
              </div>
            </div>

            <div className="pp-form-group sm:w-[50%]">
              <label className="pp-form-label" htmlFor="companyName">
                Company Name (optional)
              </label>
              <input
                id="companyName"
                placeholder=""
                className="pp-form-input"
                value={form.companyName}
                onChange={handleChange}
              />
            </div>
          </div>

          <div className="CreateEventModal-colspan2">
            <div className="pp-form-group">
              <label className="pp-form-label">Is the event outdoors? *</label>
              <div className="CreateEventModal-radio-group">
                <label className="CreateEventModal-radio-option">
                  <div className="pp-event-checkout-form__radio-wrap">
                    <button
                      type="button"
                      className={`pp-event-checkout-form__radio-btn${form.isEventOutdoors ? " selected" : ""}`}
                      onClick={() => setForm(f => ({ ...f, isEventOutdoors: true }))}
                      aria-pressed={form.isEventOutdoors}
                    >
                      Yes
                    </button>
                    <button
                      type="button"
                      className={`pp-event-checkout-form__radio-btn${!form.isEventOutdoors ? " selected" : ""}`}
                      onClick={() => setForm(f => ({ ...f, isEventOutdoors: false }))}
                      aria-pressed={!form.isEventOutdoors}
                    >
                      No
                    </button>
                  </div>
                </label>
              </div>
            </div>

            <div className="pp-form-group">
              <label className="pp-form-label">Are Tip Jars Allowed? (optional)</label>
              <div className="CreateEventModal-radio-group">
                
                <label className="CreateEventModal-radio-option">
                  <div className="pp-event-checkout-form__radio-wrap">
                    <button
                      type="button"
                      className={`pp-event-checkout-form__radio-btn${form.tipJars ? " selected" : ""
                        }`}
                      onClick={() => setForm((f) => ({ ...f, tipJars: true }))}
                      aria-pressed={form.tipJars}
                    >
                      Yes
                    </button>
                    <button
                      type="button"
                      className={`pp-event-checkout-form__radio-btn${!form.tipJars ? " selected" : ""
                        }`}
                      onClick={() => setForm((f) => ({ ...f, tipJars: false }))}
                      aria-pressed={!form.tipJars}
                    >
                      No
                    </button>
                  </div>
                </label>
              </div>
            </div>

            <div className="pp-form-group">
              <label className="pp-form-label" htmlFor="additionalDetails">
                Additional Details
              </label>
              <div className="CreateEventModal-textarea-subtitle">
                Any details we should know?
              </div>
              <textarea
                id="additionalDetails"
                placeholder=""
                className="pp-form-input pp-form-input--textarea !h-[150px]"
                value={form.additionalDetails}
                onChange={handleChange}
              />
            </div>
          </div>
        </div>

        <div className="CreateEventModal-buttons">
          <button type="button" className="pp-btn-secondary" onClick={onClose}>
            Cancel
          </button>
          <button type="submit" className="pp-btn-primary">
            Save
          </button>
        </div>
      </form>
    </PopupModal>
  );
}
