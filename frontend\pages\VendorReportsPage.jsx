import React, { useState } from "react";
import CustomSelect from "../components/CustomeSelect";
import CustomDatePicker from "../components/customeCalender";
import SalesReportComponent from "../pages/VendorSalesReport";
import TopEarningsComponent from "../components/TopEarningComponent";
import "../styles/VendorReportsPage.css";

export default function ReportFilterPage() {
  const [type, setType] = useState("Sales");
  const [startDate, setStartDate] = useState("2023-06-24");
  const [endDate, setEndDate] = useState("2023-06-25");
  const [showReport, setShowReport] = useState(false);

  const formatYmd = (d) => {
    if (!d) return "";
    const dt = d instanceof Date ? d : new Date(d);
    if (isNaN(dt.getTime())) return "";
    const y = dt.getFullYear();
    const m = String(dt.getMonth() + 1).padStart(2, "0");
    const day = String(dt.getDate()).padStart(2, "0");
    return `${y}-${m}-${day}`;
  };

  function handleSubmit(e) {
    e.preventDefault();
    setShowReport(true);
  }

  function handleTypeChange(e) {
    setType(e.target.value);
    setShowReport(false);
  }

  return (
    <div className="report-root">
      <form className="report-form" onSubmit={handleSubmit}>
        <div className="report-fields">
          <div className="report-field">
            <label className="report-label">Report Type</label>
            <CustomSelect
              className="report-select"
              value={type}
              onChange={(val) => {
                setType(val);
                setShowReport(false);
              }}
              options={[
                { value: "Sales", label: "Sales" },
                { value: "Top Earnings", label: "Top Earnings" },
              ]}
            />
          </div>
          <div className="report-field">
            <label className="report-label">Date Range</label>
            <div className="report-date-range">
              <CustomDatePicker
                className="report-date"
                value={startDate}
                onChange={(d) => setStartDate(formatYmd(d))}
              />
              <span className="report-date-sep">to</span>
              <CustomDatePicker
                className="report-date"
                value={endDate}
                onChange={(d) => setEndDate(formatYmd(d))}
              />
            </div>
          </div>
        </div>
        <button type="submit" className="pp-btn-primary">
          Generate Report
        </button>
      </form>

      {/* Show selected report after pressing button */}
      {showReport &&
        (type === "Sales" ? (
          <SalesReportComponent startDate={startDate} endDate={endDate} />
        ) : (
          <TopEarningsComponent startDate={startDate} endDate={endDate} />
        ))}
    </div>
  );
}
