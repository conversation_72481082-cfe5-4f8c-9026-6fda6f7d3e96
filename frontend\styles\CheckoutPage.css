/* ===============================
   Scoped Checkout Page Layout
   =============================== */

/* Header: Title + Notice + Steps */
.pp-checkout-header {
  display: flex;
  margin-bottom: var(--pp-sp-48);
  flex-direction: column;
  gap: var(--pp-sp-12);
}
.pp-checkout-page {
  display: flex;
  gap: var(--pp-sp-40);
  margin-bottom: 1rem;
}
.pp-checkoutpage-container {
  max-width: var(--pp-sp-1200);
  margin: 0 auto;
}

.pp-checkout-title {
  font-family: var(--pp-font-Playfair-Display);
  font-size: var(--pp-font-heading2);
  font-weight: 700;
  color: var(--pp-clr-text-main);
  margin-bottom: var(--pp-sp-40);
  margin-top: var(--pp-sp-24);
}
.checkout-first {
  width: 100%;
}

/* Green notice banner for returning customer */
.pp-checkout-notice {
  background: var(--pp-clr-guarantee-section-bg);
  border-radius: var(--pp-bor-rad-8);

  padding: var(--pp-sp-8) var(--pp-sp-16);
  font-size: var(--pp-font-extra-small);
  display: flex;
  align-items: center;
  gap: var(--pp-sp-4);
  width: 100%;
  margin-bottom: var(--pp-sp-8);
}
.pp-checkout-notice-link {
  background: none;
  border: none;

  font-weight: 600;
  cursor: pointer;
  font-size: var(--pp-font-extra-small);
  text-decoration: underline;
  padding: 0;
  margin: 0;
  transition: color var(--pp-trans-duration-fast) var(--pp-trans-ease);
}
.pp-checkout-notice-link:hover {
  color: var(--pp-clr-secondary);
  text-decoration: underline;
}

.pp-checkout-steps-bar {
  margin-top: var(--pp-sp-8);
}

.pp-checkout-content {
  display: grid;
  grid-template-columns: 1fr var(--pp-sp-400);
  gap: var(--pp-grid-gap-md);
  align-items: flex-start;
}

.pp-checkout-main-form {
  background: var(--pp-clr-primary);
  border-radius: var(--pp-bor-rad-16);

  padding: var(--pp-sp-24) 0 var(--pp-sp-60) 0;
  min-width: 0; /* allows shrinking on narrow screens */
}

.pp-checkout-sidebar {
  min-width: 320px;

  /* summary card handles its own BG/padding/radius */
}

@media (max-width: 1024px) {
  .pp-checkout-page {
    padding: var(--pp-sp-24) 0;
    gap: var(--pp-sp-24);
  }
  .pp-checkout-header {
    gap: var(--pp-sp-8);
  }
  .pp-checkout-content {
    grid-template-columns: 1fr;
    gap: var(--pp-grid-gap-md);
  }
}
@media (max-width: 800px) {
  .pp-checkout-sidebar {
    min-width: 280px;

    padding-bottom: var(--pp-sp-24);
  }
  .pp-checkout-page {
    flex-direction: column;
    padding: 0;
  }
  .pp-checkout-main-form {
    padding: 0;
  }
}

@media (max-width: 600px) {
  .pp-summary-card {
    padding: var(
      --pp-padding-card-inner
    ); /* your variables already scale down */
  }
  .pp-checkout-header {
    margin-bottom: 20px;
  }
}
@media (max-width: 300px) {
  .pp-checkout-sidebar {
    min-width: 260px;
  }
}
