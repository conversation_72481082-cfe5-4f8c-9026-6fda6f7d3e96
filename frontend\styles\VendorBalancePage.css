.acc-root {
  background: var(--pp-clr-bg);
  font-family: var(--pp-font-Metro-Sans), system-ui, -apple-system, "Segoe UI",
    Roboto, Arial, sans-serif;
}

.acc-balance-bloc {
  margin-bottom: var(--pp-sp-40);
}
.acc-balance-label {
  color: black;
  font-size: var(--pp-font-heading5);
  font-weight: 600;
  margin-bottom: var(--pp-sp-8);
}

.acc-balance-value {
  font-size: var(--pp-font-heading2);
  font-weight: 600;
  font-family: Inter, sans-serif;
  color: var(--pp-clr-secondary);
  margin-bottom: var(--pp-sp-16);
}
.acc-main-btn {
  width: fit-content;
}
.acc-linked-bloc {
  margin-top: var(--pp-sp-16);
}
.acc-accounts-title {
  font-size: var(--pp-font-base-font);
  font-weight: 600;

  color: var(--pp-clr-text-main);
  
  padding-bottom: 10px;
}

.acc-accounts-tablewrap {
  background: var(--pp-clr-primary);
  border-top: 2px solid var(--pp-clr-border-light);

  width: 100%;

  margin-bottom: var(--pp-sp-24);
  overflow-x: auto;
}
.acc-accounts-table {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.acc-table-row {
  display: grid;
  grid-template-columns:0.5fr 1fr 0.5fr  2.1fr;
  align-items: center;

  min-height: 75px;
 
  gap: 10px;
  border-bottom: 2px solid var(--pp-clr-border-light);
}

.acc-col {
  display: flex;
  align-items: center;
  min-width: 0;
}
.acc-icon {
  justify-content: center;
  background-color: #f4f4f4;
  height: 50px;
  border-radius: 6px;
  aspect-ratio: 1/1;
}
.acc-account {
  flex-direction: column;
  gap: 2px;
  align-items: flex-start;
  color: var(--pp-clr-text-main);
}
.acc-account-name {
  font-size: var(--pp-font-base2-font);
  font-weight: 600;
  color: var(--pp-clr-text-main);
}
.acc-account-details {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  margin-top: 1px;
  white-space: pre;
}
.acc-type {
  color: var(--pp-clr-text-gray);
  font-size: var(--pp-font-base2-font);
  font-weight: 400;
}
.acc-status {
  font-size: var(--pp-font-base2-font);
  color: var(--pp-clr-secondary);
  font-weight: 500;
  gap: var(--pp-sp-4);
}
.acc-verified-icon {
  font-size: 1em;
  margin-right: 2px;
}
.acc-actions {
  display: flex;
  gap: var(--pp-sp-8);
}



@media (max-width: 900px) {
  .acc-table-row {
    grid-template-columns: 0.5fr 3fr 1fr  4fr;
min-width: 900px;
    padding: 0 var(--pp-sp-8);
  }
}

@media (max-width: 600px) {
  .acc-accounts-tablewrap {
    padding: 0;
  }
  .acc-table-row {
    grid-template-columns: 1fr 3fr 2fr  4fr;
    font-size: var(--pp-font-small-font);
    gap: var(--pp-sp-4);
   min-width: 800px;
  }
}

.acc-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.22);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.acc-modal {
  background: var(--pp-clr-bg);
  border-radius: var(--pp-bor-rad-16);
  box-shadow: 0 4px 32px rgba(0, 0, 0, 0.1);
  min-width: 390px;
  max-width: 94vw;
  padding: 28px 28px 28px 28px;
  display: flex;
  flex-direction: column;
  gap: 18px;
  position: relative;
}

/* Modal header with title and close icon */
.acc-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--pp-clr-border);
}

.acc-modal-title {
  font-size: var(--pp-font-heading3);
  font-weight: 400;
  color: var(--pp-clr-text-main);
  margin: 0;
}

.acc-modal-close-btn {
  background: transparent;
  border: none;
  cursor: pointer;
  color: var(--pp-clr-text-gray);
  transition: color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.acc-modal-close-btn:hover,
.acc-modal-close-btn:focus-visible {
  color: var(--pp-clr-secondary);
  outline: none;
}

.acc-modal-subtitle {
  font-size: var(--pp-font-heading5);
  font-weight: 600;
  color: var(--pp-clr-secondary);
  margin-top: 8px;
}

.acc-modal-options {
  display: grid;
  gap: 20px;
  justify-content: space-between;
  grid-template-columns: 1fr 1fr;
}

.acc-modal-option {
  background: var(--pp-clr-primary);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-12);
  display: grid;
  align-items: center;
  justify-items: center;
  gap: 16px;
  padding: 22px 20px;
  cursor: pointer;
  min-width: 160px;
  min-height: 200px;
  transition: border-color 0.2s, box-shadow 0.2s;
  font-family: var(--pp-font-Metro-Sans), system-ui, -apple-system, "Segoe UI",
    Roboto, Arial, sans-serif;
  user-select: none;
}

.acc-modal-option:hover,
.acc-modal-option:focus-visible {
  border:none;
background-color: #F4F4F4;
  outline: none;
}

.acc-modal-option-icon {
  font-size: 32px;
  margin-right: 10px;
  margin-top: 2px;
  flex-shrink: 0;
}

.acc-modal-option-title {
  font-weight: 600;
  font-size: var(--pp-font-base2-font);
  margin-bottom: 2px;
  color: var(--pp-clr-text-main);
}

.acc-modal-option-desc {
  font-size: var(--pp-font-small-font);
  color: var(--pp-clr-text-gray);
}

@media (max-width: 500px) {
  .acc-modal {
    padding: 18px 10px 15px 10px;
    min-width: 95vw;
  }
  .acc-modal-option {
    min-width: 120px;
    padding: 14px 8px;
  }
  .acc-modal-option-icon {
    font-size: 21px;
  }
}
