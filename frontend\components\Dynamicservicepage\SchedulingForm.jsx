import React from "react";

import DurationInputs from "../common/DurationInputs";
import AvailabilityInputs from "../common/AvailabilityInputs";
import BlackoutDates from "../common/BlackoutDates";

import "../../styles/SchedulingForm.css";

export default function SchedulingForm({
  formData,
  onChange,
  config,
  pricingMethod,
}) {
  const handleDurationChange = (newDuration) => {
    onChange({ ...formData, duration: newDuration });
  };

  const handleAvailabilityChange = (newAvailability) => {
    onChange({ ...formData, availability: newAvailability });
  };

  const handleBlackoutDatesChange = (dates) => {
    onChange({ ...formData, blackoutDates: dates });
  };

  return (
    <div className="sf-scheduling-form">
      {config.duration && pricingMethod !== "packages" && (
        <div className="sf-section">
          <h3 className="sf-section-title">Duration</h3>
          <DurationInputs
            value={formData.duration}
            onChange={handleDurationChange}
          />
        </div>
      )}

      {config.availability && (
        <div className="sf-section">
          <h3 className="sf-section-title">Availability</h3>
          <AvailabilityInputs
            value={formData.availability}
            onChange={handleAvailabilityChange}
          />
        </div>
      )}

      {config.blackoutDates && (
        <div className="sf-section">
          <h3 className="sf-section-title">Black-out Dates</h3>
          <BlackoutDates
            dates={formData.blackoutDates}
            onChange={handleBlackoutDatesChange}
          />
        </div>
      )}
    </div>
  );
}
