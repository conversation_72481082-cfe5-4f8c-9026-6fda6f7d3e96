import React, { useState } from "react";
import CustomSelect from "../components/CustomeSelect";
import "../styles/VendorBookingsPage.css";
import VendorBookingTable from "../components/VendorBookingTable";
import BookingsCalendar from "./VendorBookingsCalendar";
import { BsCalendar4 } from "react-icons/bs";
import BookingDetailsModal from "../components/VendorBookingDetailsModal";

const BookingsPage = () => {
  const [view, setView] = useState("list"); // default view
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState(null);

  const handleRowClick = (booking) => {
    setSelectedBooking(booking);
    setIsModalOpen(true);
  };

  return (
    <div className="bookings-main ">
      <div className="bookings-main__controls">
        <CustomSelect
          className="bookings-main__filter"
          value={"all"}
          onChange={() => {}}
          options={[{ label: "All", value: "all" }]}
        />

        <div
          className="bookings-main__viewtoggle"
          role="group"
          aria-label="Toggle view"
        >
          <button
            className={`bookings-main__toggle-btn${
              view === "list" ? " active" : ""
            }`}
            aria-label="List View"
            onClick={() => setView("list")}
          >
            <span aria-hidden="true">≡</span>
          </button>
          <button
            className={`bookings-main__toggle-btn${
              view === "calendar" ? " active" : ""
            }`}
            aria-label="Calendar View"
            onClick={() => setView("calendar")}
          >
            <span aria-hidden="true">
              <BsCalendar4 />
            </span>
          </button>
        </div>
      </div>

      {/* Render the view based on state */}
      {view === "list" ? (
        <VendorBookingTable onRowClick={handleRowClick} />
      ) : (
        <BookingsCalendar />
      )}

      <BookingDetailsModal
        open={isModalOpen}
        booking={selectedBooking}
        onClose={() => setIsModalOpen(false)}
      />
    </div>
  );
};

export default BookingsPage;
