/**
 * Pagination utility functions
 * Handles consistent pagination across all list endpoints
 */

/**
 * Default pagination settings
 */
const DEFAULT_PAGE = 1;
const DEFAULT_LIMIT = 20;
const MAX_LIMIT = 100;

/**
 * Parse pagination parameters from request query
 * @param {Object} query - Request query object
 * @returns {Object} Parsed pagination parameters
 */
const parsePaginationParams = (query) => {
    const page = Math.max(1, parseInt(query.page) || DEFAULT_PAGE);
    const limit = Math.min(Math.max(1, parseInt(query.limit) || DEFAULT_LIMIT), MAX_LIMIT);
    const skip = (page - 1) * limit;

    return {
        page,
        limit,
        skip
    };
};

/**
 * Create pagination metadata
 * @param {number} totalItems - Total number of items
 * @param {number} page - Current page number
 * @param {number} limit - Items per page
 * @returns {Object} Pagination metadata
 */
const createPaginationMeta = (totalItems, page, limit) => {
    const totalPages = Math.ceil(totalItems / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    return {
        page,
        limit,
        totalItems,
        totalPages,
        hasNextPage,
        hasPreviousPage,
        nextPage: hasNextPage ? page + 1 : null,
        previousPage: hasPreviousPage ? page - 1 : null,
        skip: (page - 1) * limit
    };
};

/**
 * Apply pagination to Mongoose query
 * @param {Object} query - Mongoose query object
 * @param {Object} params - Pagination parameters
 * @returns {Object} Query with pagination applied
 */
const applyPagination = (query, params) => {
    const { skip, limit } = parsePaginationParams(params);
    return query.skip(skip).limit(limit);
};

/**
 * Paginate results using Mongoose aggregation
 * @param {Object} model - Mongoose model
 * @param {Array} pipeline - Aggregation pipeline
 * @param {Object} options - Pagination options
 * @returns {Object} Paginated results with metadata
 */
const paginateAggregate = async (model, pipeline, options = {}) => {
    const { page, limit } = parsePaginationParams(options);
    const skip = (page - 1) * limit;

    // Create count pipeline
    const countPipeline = [
        ...pipeline,
        { $count: 'total' }
    ];

    // Create data pipeline with pagination
    const dataPipeline = [
        ...pipeline,
        { $skip: skip },
        { $limit: limit }
    ];

    // Execute both pipelines
    const [countResult, data] = await Promise.all([
        model.aggregate(countPipeline),
        model.aggregate(dataPipeline)
    ]);

    const totalItems = countResult.length > 0 ? countResult[0].total : 0;
    const pagination = createPaginationMeta(totalItems, page, limit);

    return {
        data,
        pagination
    };
};

/**
 * Paginate standard Mongoose find query
 * @param {Object} model - Mongoose model
 * @param {Object} filter - Query filter
 * @param {Object} options - Query options including pagination
 * @returns {Object} Paginated results with metadata
 */
const paginate = async (model, filter = {}, options = {}) => {
    const { page, limit, skip } = parsePaginationParams(options);

    const {
        sort = { createdAt: -1 },
        populate = null,
        select = null,
        lean = false
    } = options;

    // Count total documents
    const totalItems = await model.countDocuments(filter);

    // Build query
    let query = model.find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit);

    if (select) {
        query = query.select(select);
    }

    if (populate) {
        if (Array.isArray(populate)) {
            populate.forEach(pop => {
                query = query.populate(pop);
            });
        } else {
            query = query.populate(populate);
        }
    }

    if (lean) {
        query = query.lean();
    }

    const data = await query.exec();
    const pagination = createPaginationMeta(totalItems, page, limit);

    return {
        data,
        pagination
    };
};

/**
 * Create pagination links for API responses
 * @param {Object} req - Express request object
 * @param {Object} pagination - Pagination metadata
 * @returns {Object} Pagination links
 */
const createPaginationLinks = (req, pagination) => {
    const baseUrl = `${req.protocol}://${req.get('host')}${req.baseUrl}${req.path}`;
    const query = { ...req.query };

    const links = {};

    // First page link
    if (pagination.page > 1) {
        query.page = 1;
        links.first = `${baseUrl}?${new URLSearchParams(query)}`;
    }

    // Previous page link
    if (pagination.hasPreviousPage) {
        query.page = pagination.previousPage;
        links.prev = `${baseUrl}?${new URLSearchParams(query)}`;
    }

    // Current page link
    query.page = pagination.page;
    links.self = `${baseUrl}?${new URLSearchParams(query)}`;

    // Next page link
    if (pagination.hasNextPage) {
        query.page = pagination.nextPage;
        links.next = `${baseUrl}?${new URLSearchParams(query)}`;
    }

    // Last page link
    if (pagination.page < pagination.totalPages) {
        query.page = pagination.totalPages;
        links.last = `${baseUrl}?${new URLSearchParams(query)}`;
    }

    return links;
};

/**
 * Validate pagination parameters
 * @param {Object} params - Pagination parameters
 * @returns {Object} Validation result
 */
const validatePaginationParams = (params) => {
    const errors = [];

    if (params.page && (isNaN(params.page) || params.page < 1)) {
        errors.push('Page must be a positive integer');
    }

    if (params.limit && (isNaN(params.limit) || params.limit < 1 || params.limit > MAX_LIMIT)) {
        errors.push(`Limit must be between 1 and ${MAX_LIMIT}`);
    }

    return {
        isValid: errors.length === 0,
        errors
    };
};

/**
 * Parse sorting parameters from request query
 * @param {Object} query - Request query object
 * @param {Array} allowedFields - Array of allowed sort fields
 * @param {Object} defaultSort - Default sort object
 * @returns {Object} Parsed sort parameters
 */
const parseSortParams = (query, allowedFields = [], defaultSort = { createdAt: -1 }) => {
    if (!query.sort) {
        return defaultSort;
    }

    const sortParams = {};
    const sortFields = query.sort.split(',');

    for (const field of sortFields) {
        let sortField = field.trim();
        let sortOrder = 1; // ascending by default

        // Check for descending order (-)
        if (sortField.startsWith('-')) {
            sortOrder = -1;
            sortField = sortField.substring(1);
        }

        // Check for explicit order (+)
        if (sortField.startsWith('+')) {
            sortOrder = 1;
            sortField = sortField.substring(1);
        }

        // Only allow whitelisted fields
        if (allowedFields.length === 0 || allowedFields.includes(sortField)) {
            sortParams[sortField] = sortOrder;
        }
    }

    // Return default sort if no valid fields found
    return Object.keys(sortParams).length > 0 ? sortParams : defaultSort;
};

/**
 * Create cursor-based pagination for real-time data
 * @param {Object} query - Mongoose query
 * @param {Object} options - Cursor options
 * @returns {Object} Cursor pagination result
 */
const createCursorPagination = async (query, options = {}) => {
    const {
        cursorField = '_id',
        cursor = null,
        limit = DEFAULT_LIMIT,
        sortOrder = -1 // -1 for desc, 1 for asc
    } = options;

    // Add cursor condition if provided
    if (cursor) {
        const operator = sortOrder === -1 ? '$lt' : '$gt';
        query = query.where(cursorField)[operator](cursor);
    }

    // Apply limit + 1 to check if there are more results
    const results = await query
        .sort({ [cursorField]: sortOrder })
        .limit(limit + 1)
        .exec();

    const hasMore = results.length > limit;
    const data = hasMore ? results.slice(0, limit) : results;

    const nextCursor = hasMore && data.length > 0
        ? data[data.length - 1][cursorField]
        : null;

    return {
        data,
        pagination: {
            hasMore,
            nextCursor,
            limit
        }
    };
};

/**
 * Calculate pagination offset for large datasets
 * @param {number} page - Current page
 * @param {number} limit - Items per page
 * @returns {number} Offset value
 */
const calculateOffset = (page, limit) => {
    return Math.max(0, (page - 1) * limit);
};

/**
 * Get pagination summary text
 * @param {Object} pagination - Pagination metadata
 * @returns {string} Summary text
 */
const getPaginationSummary = (pagination) => {
    const { page, limit, totalItems } = pagination;
    const start = Math.min((page - 1) * limit + 1, totalItems);
    const end = Math.min(page * limit, totalItems);

    if (totalItems === 0) {
        return 'No items found';
    }

    if (totalItems === 1) {
        return '1 item';
    }

    return `Showing ${start}-${end} of ${totalItems} items`;
};

module.exports = {
    DEFAULT_PAGE,
    DEFAULT_LIMIT,
    MAX_LIMIT,
    parsePaginationParams,
    createPaginationMeta,
    applyPagination,
    paginate,
    paginateAggregate,
    createPaginationLinks,
    validatePaginationParams,
    parseSortParams,
    createCursorPagination,
    calculateOffset,
    getPaginationSummary
};
