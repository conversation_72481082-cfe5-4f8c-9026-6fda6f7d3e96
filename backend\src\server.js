require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const cookieParser = require('cookie-parser');
const mongoSanitize = require('express-mongo-sanitize');
const http = require('http');
const socketIo = require('socket.io');

// Import utilities and middleware
const connectDB = require('./config/database');
const insertFixedCategories = require('./utils/fixCategories');
const { httpLogger, logger } = require('./utils/logger');
const StripeStatusChecker = require('./services/stripeStatusChecker');
const {
    globalErrorHandler,
    notFoundHandler,
    unhandledRejectionHandler,
    uncaughtExceptionHandler
} = require('./middlewares/errorHandler');
const { generalRateLimit } = require('./middlewares/rateLimiting');

// Import routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const vendorRoutes = require('./routes/vendors');
const serviceRoutes = require('./routes/services');
const bookingRoutes = require('./routes/bookings');
const cartRoutes = require('./routes/cart');
const paymentRoutes = require('./routes/payments');
const messageRoutes = require('./routes/messages');
const reviewRoutes = require('./routes/reviews');
const adminRoutes = require('./routes/admin');
const cmsRoutes = require('./routes/cms');

// Import socket handlers
const SocketManager = require('./sockets/index');

const app = express();
const server = http.createServer(app);

// Initialize Socket.IO with proper management
const socketManager = new SocketManager(server);
const io = socketManager.initialize();

// Set up global error handlers
unhandledRejectionHandler();
uncaughtExceptionHandler();

// Connect to MongoDB
connectDB().then(async () => {
    await insertFixedCategories();
    logger.info('Fixed categories inserted into DB');
});

// Security middleware
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
        },
    },
    crossOriginEmbedderPolicy: false,
    crossOriginResourcePolicy: { policy: 'cross-origin' }
}));

// CORS configuration
app.use(cors({
    origin: function (origin, callback) {
        const allowedOrigins = [
            process.env.FRONTEND_URL,
            'http://localhost:3000',
            'http://localhost:3001',
            'http://localhost:5173'
        ].filter(Boolean);

        // Allow requests with no origin (mobile apps, etc.)
        if (!origin) return callback(null, true);

        if (allowedOrigins.indexOf(origin) !== -1) {
            callback(null, true);
        } else {
            callback(new Error('Not allowed by CORS'));
        }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Body parsing middleware
app.use(express.json({
    limit: '10mb',
    verify: (req, res, buf) => {
        // Store raw body for webhook verification
        if (req.originalUrl === '/api/payments/webhook') {
            req.rawBody = buf;
        }
    }
}));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());

// Data sanitization
app.use(mongoSanitize());

// Compression
app.use(compression());

// HTTP request logging
app.use(httpLogger);

// Serve local uploads if using local storage
app.use('/uploads', express.static(require('path').join(__dirname, '../uploads')));

// Rate limiting
app.use('/api/', generalRateLimit);

// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development',
        version: process.env.npm_package_version || '1.0.0'
    });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/vendors', vendorRoutes);
app.use('/api/services', serviceRoutes);
app.use('/api/categories', require('./routes/categories')); // New dedicated categories route
app.use('/api/bookings', bookingRoutes);
app.use('/api/cart', cartRoutes);
app.use('/api/payments', paymentRoutes);
app.use('/api/messages', messageRoutes);
app.use('/api/reviews', reviewRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/cms', cmsRoutes);
app.use('/api/files', require('./routes/files')); // File proxy routes

// Root endpoint
app.get('/', (req, res) => {
    res.json({
        message: 'Welcome to Party Pipeline API',
        version: '1.0.0',
        documentation: '/api/docs',
        health: '/health'
    });
});

// Make Socket.IO accessible to controllers and middleware
app.set('io', io);
app.set('socketManager', socketManager);

// Middleware to make socket manager available in requests
app.use((req, res, next) => {
    req.io = io;
    req.socketManager = socketManager;
    next();
});

// 404 handler
app.use(notFoundHandler);

// Global error handler
app.use(globalErrorHandler);

// Start server
const PORT = process.env.PORT || 5000;

// Initialize Stripe Status Checker
const stripeStatusChecker = new StripeStatusChecker();

server.listen(PORT, () => {
    logger.info(`🚀 Party Pipeline API server running on port ${PORT}`, {
        environment: process.env.NODE_ENV || 'development',
        port: PORT,
        baseUrl: process.env.BASE_URL || `http://localhost:${PORT}`
    });

    // Start the Stripe status checker
    stripeStatusChecker.start();
});

// Graceful shutdown
const gracefulShutdown = (signal) => {
    logger.info(`${signal} received. Starting graceful shutdown...`);

    // Stop Stripe status checker
    if (stripeStatusChecker) {
        stripeStatusChecker.stop();
    }

    // Shutdown Socket.IO first
    if (socketManager) {
        socketManager.shutdown();
    }

    server.close((err) => {
        if (err) {
            logger.error('Error during server shutdown', { error: err.message });
            process.exit(1);
        }

        logger.info('Server closed successfully');
        process.exit(0);
    });

    // Force shutdown after 30 seconds
    setTimeout(() => {
        logger.error('Forced shutdown after timeout');
        process.exit(1);
    }, 30000);
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

module.exports = app;
