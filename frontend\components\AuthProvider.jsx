import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useAuth } from '../redux/useAuth';
import { refreshAccessToken, initializeAuth } from '../redux/authSlice';

/**
 * Authentication Provider component
 * Handles token refresh, persistence, and authentication initialization
 */
const AuthProvider = ({ children }) => {
  const dispatch = useDispatch();
  const { isAuthenticated, user } = useAuth();

  useEffect(() => {
    // Initialize auth state from localStorage
    dispatch(initializeAuth());
    
    // Try to refresh token if user data exists but no current session
    const userData = localStorage.getItem('user');
    const accessToken = localStorage.getItem('accessToken');
    
    if (userData && !accessToken) {
      // User data exists but no access token, try to refresh
      dispatch(refreshAccessToken());
    }
  }, [dispatch]);

  useEffect(() => {
    // Set up automatic token refresh
    let refreshInterval;
    
    if (isAuthenticated) {
      // Refresh token every 14 minutes (tokens expire in 15 minutes)
      refreshInterval = setInterval(() => {
        dispatch(refreshAccessToken());
      }, 14 * 60 * 1000);
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [isAuthenticated, dispatch]);

  // Handle page visibility change to refresh token when user returns
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && isAuthenticated) {
        // Check if token needs refresh when user returns to tab
        const lastRefresh = localStorage.getItem('lastTokenRefresh');
        const now = Date.now();
        
        // Refresh if more than 10 minutes have passed
        if (!lastRefresh || now - parseInt(lastRefresh) > 10 * 60 * 1000) {
          dispatch(refreshAccessToken());
          localStorage.setItem('lastTokenRefresh', now.toString());
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isAuthenticated, dispatch]);

  return children;
};

export default AuthProvider;
