import React from "react";
import { FiDownload } from "react-icons/fi";
import "../styles/TopEarningComponent.css";
// Dummy top earnings data matching screenshot
const topEarnings = [
  { bookable: "Batman", bookings: 7, total: "$710.00" },
  { bookable: "Storm Trooper", bookings: 6, total: "$620.70" },
  { bookable: "Ironman", bookings: 6, total: "$618.00" },
  { bookable: "Thor", bookings: 4, total: "$450.57" },
  { bookable: "Spider-Man", bookings: 4, total: "$425.00" },
  { bookable: "Captain Marvel", bookings: 2, total: "$388.00" },
  { bookable: "Luke Skywalker", bookings: 3, total: "$290.00" },
  { bookable: "Wonder Woman", bookings: 2, total: "$220.25" },
  { bookable: "Captain America", bookings: 1, total: "$180.00" },
  { bookable: "Hulk", bookings: 1, total: "$165.00" },
  { bookable: "Robin", bookings: 1, total: "$165.50" },
  { bookable: "Superman", bookings: 1, total: "$78.00" },
];

export default function TopEarningsComponent({ startDate, endDate }) {
  return (
    <div className="TopEarningComponent sr-report-content ">
      <div className="sr-header-row">
        <div className="sr-title">
          <div className="sr-main-title">Top Earning</div>
          <div className="sr-sub-title">
            {startDate} to {endDate}
          </div>
        </div>
        <button className="pp-btn-secondary flex items-center gap-2">
          <FiDownload className="sr-btn-icon" /> Download Report
        </button>
      </div>
      <div className="sr-section">
        <div className="sr-table-wrap">
          <table className="sr-table">
            <thead>
              <tr>
                <th>Bookable</th>
                <th># of Bookings</th>
                <th>Total</th>
              </tr>
            </thead>
            <tbody>
              {topEarnings.map((row, idx) => (
                <tr key={idx}>
                  <td>{row.bookable}</td>
                  <td>{row.bookings}</td>
                  <td>{row.total}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
