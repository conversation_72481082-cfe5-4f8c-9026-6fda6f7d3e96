import React from "react";
import { Link } from "react-router-dom";
import "../styles/ProductCard.css";
import { MdOutlineStarBorderPurple500 } from "react-icons/md";

// Scope: .pp-product-card
const ProductCard = ({
  id,
  image,
  title,
  subtitle,
  price,
  category,
  rating,
}) => {
  return (
    <article
      className="pp-product-card"
      aria-labelledby={`pp-product-${id}-title`}
    >
      <Link
        to={`/services/${id}`}
        className="pp-product-card__link"
        aria-label={`View details for ${title}`}
      >
        <div className="pp-product-card__image-wrap">
          <img
            src={image}
            alt={title}
            className="pp-product-card__image"
            loading="lazy"
            decoding="async"
          />
        </div>
        <div className="pp-product-card__content">
          <div className="grid gap-1">
            <h3
              id={`pp-product-${id}-title`}
              className="pp-product-card__title"
            >
              {title}
            </h3>
            {subtitle ? (
              <p className="pp-product-card__subtitle">{subtitle}</p>
            ) : null}
          </div>
          <div className="grid gap-2">

            <div className="pp-product-card__footer">
              
              <p className="pp-product-card__price">
                From <span>${price}</span>
              </p>
            </div>
          </div>
        </div>
      </Link>
    </article>
  );
};

export default ProductCard;
