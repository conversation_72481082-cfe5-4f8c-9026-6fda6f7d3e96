const express = require('express');
const { GetObjectCommand } = require('@aws-sdk/client-s3');
const { S3Client } = require('@aws-sdk/client-s3');
const { sendError, sendNotFound } = require('../utils/responseHelper');
const { hasS3Config } = require('../config/storage');

const router = express.Router();

// Initialize S3 client (reuse from storage config)
let s3Client = null;
if (hasS3Config) {
    s3Client = new S3Client({
        region: process.env.AWS_REGION || 'us-east-1',
        credentials: {
            accessKeyId: process.env.AWS_ACCESS_KEY_ID,
            secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
        }
    });
}

/**
 * Proxy endpoint to serve S3 images
 * GET /api/files/image/:key
 * 
 * This endpoint serves images from S3 through the backend,
 * eliminating the need for signed URLs or public bucket access.
 */
router.get('/image/:key(*)', async (req, res) => {
    try {
        // Check if S3 is configured
        if (!hasS3Config || !s3Client) {
            return sendError(res, 'File service not available', 503);
        }

        const { key } = req.params;

        if (!key) {
            return sendError(res, 'File key is required', 400);
        }

        // Decode the key in case it's URL encoded
        const decodedKey = decodeURIComponent(key);

        // Get the object from S3
        const command = new GetObjectCommand({
            Bucket: process.env.AWS_S3_BUCKET_NAME,
            Key: decodedKey
        });

        const response = await s3Client.send(command);

        // Set appropriate headers
        res.set({
            'Content-Type': response.ContentType || 'application/octet-stream',
            'Content-Length': response.ContentLength,
            'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
            'ETag': response.ETag,
            'Last-Modified': response.LastModified?.toUTCString()
        });

        // Handle conditional requests
        if (req.headers['if-none-match'] === response.ETag) {
            return res.status(304).end();
        }

        if (req.headers['if-modified-since'] && response.LastModified) {
            const ifModifiedSince = new Date(req.headers['if-modified-since']);
            if (response.LastModified <= ifModifiedSince) {
                return res.status(304).end();
            }
        }

        // Stream the file content
        response.Body.pipe(res);

    } catch (error) {
        console.error('Error serving file:', error);

        if (error.name === 'NoSuchKey') {
            return sendNotFound(res, 'File not found');
        }

        if (error.name === 'AccessDenied') {
            return sendError(res, 'Access denied', 403);
        }

        return sendError(res, 'Error serving file', 500);
    }
});

/**
 * Health check for file service
 */
router.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        s3Configured: hasS3Config,
        timestamp: new Date().toISOString()
    });
});

module.exports = router;
