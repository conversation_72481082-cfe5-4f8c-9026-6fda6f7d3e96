import React, { useMemo, useState } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { selectServiceById } from "../redux/servicesSlice";
import GuaranteeSection from "../components/GuaranteeSection";
import "../styles/ServiceDetails.css";
import { HiOutlineBuildingStorefront } from "react-icons/hi2";
import { MdOutlineStarBorderPurple500 } from "react-icons/md";
import ServiceReviewDetails from "../components/ServiceReviewmodel";
import CustomDatePicker from "../components/customeCalender";
import CustomSelect from "../components/CustomeSelect";

const formatCurrency = (n) => `$${Number(n || 0).toLocaleString("en-IN")}`;

const generateTimeOptions = () => {
  const options = [];
  const start = 16 * 60; // 4:00 PM
  const end = 22 * 60 + 30; // 10:30 PM
  for (let m = start; m <= end; m += 30) {
    const hours24 = Math.floor(m / 60);
    const minutes = m % 60;
    const ampm = hours24 >= 12 ? "PM" : "AM";
    const hours12 = ((hours24 + 11) % 12) + 1;
    options.push(`${hours12}:${minutes.toString().padStart(2, "0")} ${ampm}`);
  }
  return options;
};

const ServiceDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const service = useSelector((state) => selectServiceById(state, id));

  const gallery = useMemo(() => {
    const imgs =
      Array.isArray(service?.gallery) && service.gallery.length > 0
        ? service.gallery
        : [service?.image].filter(Boolean);
    return imgs;
  }, [service]);

  const [activeImage, setActiveImage] = useState(gallery[0]);
  const [selectedPackageId, setSelectedPackageId] = useState(null);
  const [selectedMenuItemId, setSelectedMenuItemId] = useState(null);
  const [addOnSelections, setAddOnSelections] = useState({
    costumeProps: "no",
    attendant: "no",
  });
  const [booking, setBooking] = useState({
    date: null,
    timeFrom: "",
    timeUntil: "",
    guests: 1,
  });
  const [showReviewModal, setShowReviewModal] = useState(false);

  const times = useMemo(() => generateTimeOptions(), []);
  const selectedPackage = useMemo(() => {
    return (
      (service?.packages || []).find((p) => p.id === selectedPackageId) || null
    );
  }, [service, selectedPackageId]);

  const total = useMemo(() => {
    const base = Number(service?.price || service?.basePrice || 0);
    const pkg = Number(selectedPackage?.price || 0);

    // Calculate add-ons cost
    const addons = Object.entries(addOnSelections).reduce(
      (sum, [name, selection]) => {
        if (selection === "yes") {
          // Handle default add-ons
          if (name === "costumeProps") return sum + 50;
          if (name === "attendant") return sum + 80;

          // Handle dynamic add-ons from service data
          const addon = service.addOns?.find(
            (a) => a.id === name || a.title === name
          );
          if (addon && addon.price) {
            return sum + Number(addon.price);
          }
        }
        return sum;
      },
      0
    );

    // Calculate multiple workers cost (for entertainment services)
    let workersCost = 0;
    if (
      service.category === "entertainment" &&
      service.multipleWorkers?.enabled
    ) {
      const workersCount = addOnSelections.workers || 1;
      if (workersCount > 1 && service.multipleWorkers.multiplyCosts) {
        workersCost = (base + pkg) * (workersCount - 1);
      }
    }

    return base + pkg + addons + workersCost;
  }, [service, selectedPackage, addOnSelections]);

  if (!service)
    return (
      <section className="pp-service-details">
        <p> Service not found. </p>
      </section>
    );

  return (
    <div className="pp-max-container">
      <section className="pp-service-details">
        <div className="pp-service-details__grid">
          {/* Media Gallery */}
          <div className="pp-service-details__media">
            <img
              src={activeImage}
              alt={service.title}
              className="pp-service-details__main-image"
              loading="lazy"
              decoding="async"
            />
            <div className="pp-service-details__thumbs">
              {gallery.map((src, i) => (
                <img
                  key={`${src}-${i}`}
                  src={src}
                  alt={`${service.title} ${i + 1}`}
                  className={`pp-service-details__thumb ${
                    activeImage === src ? "active" : ""
                  }`}
                  loading="lazy"
                  decoding="async"
                  onClick={() => setActiveImage(src)}
                />
              ))}
            </div>
          </div>

          {/* Meta & Booking */}
          <div className="pp-service-details__meta">
            <h1 className="pp-service-details__title">{service.title}</h1>
            {service.subtitle && (
              <p
                className="pp-service-details__subtitle"
                onClick={() => navigate("/vendorstore")}
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === "Enter" || e.key === " ") {
                    navigate("/vendorstore");
                  }
                }}
                aria-label="Go to vendor store"
                role="button"
              >
                <HiOutlineBuildingStorefront />
                {service.subtitle}
              </p>
            )}
            <div className="pp-service-rating-row flex gap-2">
              <p className="font-semibold">0.0 </p>
              <div className="pp-service-rating-stars flex items-center">
                <MdOutlineStarBorderPurple500 />
                <MdOutlineStarBorderPurple500 />
                <MdOutlineStarBorderPurple500 />
                <MdOutlineStarBorderPurple500 />
                <MdOutlineStarBorderPurple500 />
              </div>
              <button
                type="button"
                className="pp-service-rating-link cursor-pointer"
                onClick={() => setShowReviewModal(true)}
                aria-label="View ratings and reviews"
              >
                <span className="underline font-semibold">
                  {service.reviews?.length || 23} ratings
                </span>
              </button>
            </div>
            <div className="pp-service-details__price">
              From{" "}
              <strong>
                {formatCurrency(service.price || service.basePrice)}
              </strong>
            </div>
            {service.description && (
              <div className="pp-service-details__description">
                <p>{service.description}</p>
              </div>
            )}
            {/* Menu Items */}
            {service.menuItems?.length > 0 && (
              <section className="pp-service-details__section">
                <h2 className="pp-service-details__section-title">
                  Choose a Menu Item
                </h2>
                <div className="pp-option-list">
                  {service.menuItems.map((item) => (
                    <div
                      key={item.id}
                      className={`pp-option-card ${
                        selectedMenuItemId === item.id
                          ? "pp-option-card--selected"
                          : ""
                      }`}
                      onClick={() => setSelectedMenuItemId(item.id)}
                      role="button"
                      tabIndex={0}
                      onKeyDown={(e) => {
                        if (e.key === "Enter" || e.key === " ")
                          setSelectedMenuItemId(item.id);
                      }}
                    >
                      <div className="pp-option-card__content">
                        <div className="pp-option-card__header">
                          <div className="pp-option-card__title">
                            {item.name}
                          </div>
                          <div className="pp-option-card__price flex items-center">
                            <h6>{formatCurrency(item.price)}</h6>
                            <span className="text-black-50">/person</span>
                          </div>
                        </div>
                        {item.description && (
                          <p className="pp-option-card__desc">
                            {item.description}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </section>
            )}

            {/* Packages */}
            {service.packages?.length > 0 && (
              <section className="pp-service-details__section">
                <h2 className="pp-service-details__section-title">
                  Book {service.title}
                </h2>
                <div className="pp-option-list">
                  {service.packages.map((pkg) => (
                    <div
                      key={pkg.id}
                      className={`pp-option-card ${
                        selectedPackageId === pkg.id
                          ? "pp-option-card--selected"
                          : ""
                      }`}
                      onClick={() => setSelectedPackageId(pkg.id)}
                      role="button"
                      tabIndex={0}
                      onKeyDown={(e) => {
                        if (e.key === "Enter" || e.key === " ")
                          setSelectedPackageId(pkg.id);
                      }}
                    >
                      <div className="pp-option-card__content">
                        <div className="pp-option-card__header">
                          <div className="pp-option-card__title">
                            {pkg.title}
                            {service.category === "rental" && pkg.duration && (
                              <div className="pp-package-duration">
                                {pkg.duration}
                              </div>
                            )}
                          </div>
                          <div className="pp-option-card__price">
                            {formatCurrency(pkg.price)}
                          </div>
                        </div>
                        {pkg.description && (
                          <p className="pp-option-card__desc">
                            {pkg.description}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </section>
            )}

            {/* Booking Form */}
            <section className="pp-service-details__section">
              <h2 className="pp-service-details__section-title">
                Book {service.title}{" "}
              </h2>
              <div className="pp-booking-form">
                <div className="pp-field">
                  <label className="pp-label">Date</label>
                  <CustomDatePicker
                    value={booking.date}
                    onChange={(date) =>
                      setBooking((prev) => ({ ...prev, date: date }))
                    }
                    placeholder="Select a date"
                  />
                </div>
                <div className="flex gap-10 items-center ">
                  <CustomSelect
                    label="From"
                    value={booking.timeFrom}
                    onChange={(val) =>
                      setBooking((b) => ({ ...b, timeFrom: val }))
                    }
                    options={times}
                  />

                  <CustomSelect
                    label="Until"
                    value={booking.timeUntil}
                    onChange={(val) =>
                      setBooking((b) => ({ ...b, timeUntil: val }))
                    }
                    options={times}
                  />
                </div>
                {service?.bookingFields?.guests && (
                  <div className="pp-field mt-4">
                    <label className="pp-label">Number of People</label>
                    <p className="text-gray-500 text-sm">50 Min / 400 Max</p>
                    <input
                      type="text"
                      min="1"
                      className="pp-input w-[80px]"
                      value={booking.guests}
                      onChange={(e) =>
                        setBooking((b) => ({
                          ...b,
                          guests: Number(e.target.value),
                        }))
                      }
                    />
                  </div>
                )}
              </div>
            </section>

            {/* Multiple Workers Section - Only for Entertainment Services */}
            {service.category === "entertainment" &&
              service.multipleWorkers?.enabled && (
                <section className="pp-service-details__section">
                  <h2 className="pp-service-details__section-title">
                    {service.multipleWorkers.workerTitle || "Multiple Workers"}
                  </h2>

                  <div className="pp-workers-selector">
                    <CustomSelect
                      className="pp-workers-select"
                      label="Number of Workers"
                      value={addOnSelections.workers || 1}
                      onChange={(val) =>
                        setAddOnSelections((prev) => ({
                          ...prev,
                          workers: Number(val),
                        }))
                      }
                      options={Array.from(
                        { length: service.multipleWorkers.maxWorkers || 5 },
                        (_, i) => ({
                          label: String(i + 1),
                          value: i + 1,
                        })
                      )}
                    />
                  </div>
                </section>
              )}

            {/* Add-ons Section - For All Service Categories */}
            <section className="pp-service-details__section">
              <h2 className="pp-service-details__section-title">Add-ons</h2>
              <div className="pp-addon-list">
                {/* Dynamic Add-ons from Service Data */}
                {service.addOns?.length > 0 ? (
                  service.addOns.map((addon, index) => {
                    // Default to "no" if not set
                    const addonKey = addon.id || `addon-${index}`;
                    const selectedValue =
                      addOnSelections[addonKey] !== undefined
                        ? addOnSelections[addonKey]
                        : "no";
                    return (
                      <div key={addonKey} className="pp-addon-item">
                        <h3 className="pp-addon-item__title">{addon.title}</h3>
                        <p className="pp-addon-item__description">
                          {addon.description ||
                            "Additional service option to enhance your experience."}
                        </p>
                        <div className="Addon-container">
                          <button
                            type="button"
                            className={`pp-addon-button pp-addon-button--no ${
                              selectedValue === "no"
                                ? "pp-addon-button--selected"
                                : ""
                            }`}
                            onClick={() =>
                              setAddOnSelections((prev) => ({
                                ...prev,
                                [addonKey]: "no",
                              }))
                            }
                          >
                            No, Thanks
                          </button>
                          <button
                            type="button"
                            className={`pp-addon-button pp-addon-button--yes ${
                              selectedValue === "yes"
                                ? "pp-addon-button--selected"
                                : ""
                            }`}
                            onClick={() =>
                              setAddOnSelections((prev) => ({
                                ...prev,
                                [addonKey]: "yes",
                              }))
                            }
                          >
                            <span>Costume Props</span>
                            <span>+ $50</span>
                          </button>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  // Default Add-ons for services without custom add-ons
                  <>
                    {/* Costume Props Add-on */}
                    <div className="pp-addon-item">
                      <h3 className="pp-addon-item__title">Costume Props</h3>
                      <p className="pp-addon-item__description">
                        Vestibulum ante ipsum primis in faucibus orci luctus et
                        ultrices posuere cubilia curae.
                      </p>
                      <div className="Addon-container">
                        <button
                          type="button"
                          className={`pp-addon-button pp-addon-button--no ${
                            addOnSelections.costumeProps === "no"
                              ? "pp-addon-button--selected"
                              : ""
                          }`}
                          onClick={() =>
                            setAddOnSelections((prev) => ({
                              ...prev,
                              costumeProps: "no",
                            }))
                          }
                        >
                          No, Thanks
                        </button>
                        <button
                          type="button"
                          className={`pp-addon-button pp-addon-button--yes ${
                            addOnSelections.costumeProps === "yes"
                              ? "pp-addon-button--selected"
                              : ""
                          }`}
                          onClick={() =>
                            setAddOnSelections((prev) => ({
                              ...prev,
                              costumeProps: "yes",
                            }))
                          }
                        >
                          <span>Costume Props</span>
                          <span>+ $50</span>
                        </button>
                      </div>
                    </div>

                    {/* Attendant Add-on */}
                    <div className="pp-addon-item">
                      <h3 className="pp-addon-item__title">Attendant</h3>
                      <p className="pp-addon-item__description">
                        Sed semper ultrices pellentesque metus felis, sit amet
                        mauris dapibus quam fermentum lorem iaculis quis.
                      </p>
                      <div className="Addon-container">
                        <button
                          type="button"
                          className={`pp-addon-button pp-addon-button--no ${
                            addOnSelections.attendant === "no"
                              ? "pp-addon-button--selected"
                              : ""
                          }`}
                          onClick={() =>
                            setAddOnSelections((prev) => ({
                              ...prev,
                              attendant: "no",
                            }))
                          }
                        >
                          No, Thanks
                        </button>
                        <button
                          type="button"
                          className={`pp-addon-button pp-addon-button--yes ${
                            addOnSelections.attendant === "yes"
                              ? "pp-addon-button--selected"
                              : ""
                          }`}
                          onClick={() =>
                            setAddOnSelections((prev) => ({
                              ...prev,
                              attendant: "yes",
                            }))
                          }
                        >
                          <span>Attendant</span>
                          <span>+ $80</span>
                        </button>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </section>

            {/* Total & Actions */}
            <div className="pp-total-row">
              <button className="pp-btn-primary">Add to Cart</button>
              <div className="pp-total-row__value">{formatCurrency(total)}</div>
            </div>

            <div className="pp-service-details__actions">
              <button className="querymessage">
                <span>?</span> Questions? Message Wild Cats Catering
              </button>
            </div>

            <div className="small-size">
              <GuaranteeSection />
            </div>
          </div>
        </div>

        {showReviewModal && (
          <ServiceReviewDetails
            service={service}
            onClose={() => setShowReviewModal(false)}
          />
        )}
      </section>
    </div>
  );
};

export default ServiceDetails;
