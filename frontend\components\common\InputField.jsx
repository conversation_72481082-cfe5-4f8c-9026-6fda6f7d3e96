import React from "react";

export default function InputField({
  label,
  name,
  value,
  onChange,
  type = "text",
  placeholder = "",
  currency = false,
}) {
  return (
    <div className="pp-form-group">
      <label htmlFor={name} className="pp-form-label">
        {label}
      </label>
      {currency ? (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            gap: "var(--pp-sp-8)",
          }}
        >
          <span style={{ fontWeight: 600, color: "var(--pp-clr-text-gray)" }}>
            $
          </span>
          <input
            id={name}
            name={name}
            type="number"
            className="pp-form-input"
            value={value}
            onChange={onChange}
            placeholder={placeholder}
            min="0"
            step="0.01"
          />
        </div>
      ) : (
        <input
          id={name}
          name={name}
          type={type}
          className="pp-form-input"
          value={value}
          onChange={onChange}
          placeholder={placeholder}
        />
      )}
    </div>
  );
}
