/* =========================
   📸 Photo Form
   ========================= */

.pf-photo-form {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-24);
}

.pf-section-title {
  font-size: var(--pp-font-heading5);
  font-weight: 600;
  color: var(--pp-clr-text-main);
  margin: 0;
  line-height: 1.3;
}

/* Photos Grid */
.pf-photos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--pp-sp-20);
}

.pf-photo-item {
  position: relative;
}

.pf-photo-container {
  position: relative;
  width: 100%;
  aspect-ratio: 1;
  border-radius: var(--pp-bor-rad-12);
  overflow: hidden;
  background-color: var(--pp-clr-primary);
  border: 1px solid var(--pp-clr-border-light);
}

.pf-photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.pf-photo-container:hover .pf-photo {
  transform: scale(1.05);
}

.pf-cover-badge {
  position: absolute;
  bottom: var(--pp-sp-8);
  left: var(--pp-sp-8);
  background-color: var(--pp-clr-secondary);
  color: var(--pp-clr-primary);
  padding: var(--pp-sp-4) var(--pp-sp-8);
  border-radius: var(--pp-bor-rad-8);
  font-size: var(--pp-font-extra-small);
  font-weight: 600;
  z-index: 2;
}

.pf-photo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: var(--pp-sp-8);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 3;
}

.pf-photo-container:hover .pf-photo-overlay {
  opacity: 1;
}

.pf-btn-remove {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  color: var(--pp-clr-secondary);
  cursor: pointer;
  padding: var(--pp-sp-8);
  border-radius: var(--pp-bor-rad-8);
  transition: all 0.3s ease;
  font-size: var(--pp-font-extra-small);
  font-weight: 600;
}

.pf-btn-remove:hover {
  background: var(--pp-clr-primary);
  transform: scale(1.1);
}

.pf-btn-set-cover {
  background: var(--pp-clr-secondary);
  border: none;
  color: var(--pp-clr-primary);
  cursor: pointer;
  padding: var(--pp-sp-8) var(--pp-sp-12);
  border-radius: var(--pp-bor-rad-8);
  transition: all 0.3s ease;
  font-size: var(--pp-font-extra-small);
  font-weight: 600;
}

.pf-btn-set-cover:hover {
  background: var(--pp-clr-text-gray);
  transform: scale(1.05);
}

/* Upload Area */
.pf-upload-area {
  position: relative;
  width: 100%;
  aspect-ratio: 1;
  border-radius: var(--pp-bor-rad-12);
  overflow: hidden;
}

.pf-file-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
  z-index: 2;
}

.pf-upload-label {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px dashed var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-12);
  background-color: rgba(0, 0, 0, 0.02);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: var(--pp-sp-12);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1;
}

.pf-upload-label:hover,
.pf-upload-label.pf-drag-active {
  border-color: var(--pp-clr-secondary);
  background-color: rgba(0, 0, 0, 0.05);
}

.pf-upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--pp-sp-8);
  text-align: center;
}

.pf-upload-icon {
  font-size: var(--pp-font-heading3);

  font-weight: 400;
}

.pf-upload-text {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  margin: 0;
  line-height: 1.4;
  max-width: 120px;
}

/* Upload Info */
.pf-upload-info {
  margin-top: var(--pp-sp-16);
  padding: var(--pp-sp-16);
  background-color: var(--pp-clr-border-light);
  border-radius: var(--pp-bor-rad-8);
  border-left: 3px solid var(--pp-clr-text-gray);
}

.pf-info-text {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  margin: 0 0 var(--pp-sp-8) 0;
  line-height: 1.5;
}

.pf-info-text:last-child {
  margin-bottom: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .pf-photo-form {
    gap: var(--pp-sp-20);
  }

  .pf-photos-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: var(--pp-sp-16);
  }

  .pf-upload-info {
    margin-top: var(--pp-sp-12);
    padding: var(--pp-sp-12);
  }
}

@media (max-width: 480px) {
  .pf-photo-form {
    gap: var(--pp-sp-16);
  }

  .pf-photos-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: var(--pp-sp-12);
  }

  .pf-upload-info {
    margin-top: var(--pp-sp-8);
    padding: var(--pp-sp-8);
  }

  .pf-upload-text {
    font-size: var(--pp-font-small-font);
    max-width: 100px;
  }
}
