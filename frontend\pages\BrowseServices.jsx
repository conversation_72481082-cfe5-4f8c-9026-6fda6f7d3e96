import React, { useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setFilter, selectFilteredServices } from "../redux/servicesSlice";
import ProductCard from "../components/ProductCard";
import "../styles/BrowseServices.css";
import { IoSearch, IoClose } from "react-icons/io5";

import CustomSelect from "../components/CustomeSelect";

const BrowseServices = () => {
  const dispatch = useDispatch();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const { items, filters } = useSelector((state) => state.services);
  const filteredServices = useSelector(selectFilteredServices);

  const uniqueVendors = useMemo(() => {
    const set = new Set(
      (items || []).map((s) => s.vendor || s.subtitle || "").filter(Boolean)
    );
    return Array.from(set);
  }, [items]);

  const uniqueLocations = useMemo(() => {
    const set = new Set((items || []).map((s) => s.location).filter(Boolean));
    return Array.from(set);
  }, [items]);

  const uniqueSubCategories = useMemo(() => {
    const set = new Set(
      (items || []).map((s) => s.subCategory).filter(Boolean)
    );
    return Array.from(set);
  }, [items]);

  return (
    <section className="pp-browse-services">
      <header className="pp-browse-services__header">
        <div className="pp-browse-services__filters">
          <div className="pp-browse-services__filters-row">
            <button
              type="button"
              className=" pp-browse-services__filter-btn"
              aria-label="Open filters"
              onClick={() => setIsDrawerOpen(true)}
            >
              Filter
            </button>

            <div className="pp-browse-services__search">
              <input
                className="pp-browse-services__input"
                placeholder="Anywhere   |   Anytime"
                value={filters.query || ""}
                onChange={(e) =>
                  dispatch(setFilter({ name: "query", value: e.target.value }))
                }
              />
              <IoSearch className="pp-browse-services__search-icon" />
            </div>
          </div>

          <div className="pp-browse-services__categories">
            <button
              className={`pp-browse-services__chip${
                filters.category === "all"
                  ? " pp-browse-services__chip--active"
                  : ""
              }`}
              onClick={() =>
                dispatch(setFilter({ name: "category", value: "" }))
              }
            >
              All
            </button>
            <button
              className={`pp-browse-services__chip${
                filters.category === "entertainment"
                  ? " pp-browse-services__chip--active"
                  : ""
              }`}
              onClick={() =>
                dispatch(
                  setFilter({ name: "category", value: "entertainment" })
                )
              }
            >
              Entertainment
            </button>
            <button
              className={`pp-browse-services__chip${
                filters.category === "characters"
                  ? " pp-browse-services__chip--active"
                  : ""
              }`}
              onClick={() =>
                dispatch(setFilter({ name: "category", value: "characters" }))
              }
            >
              Characters
            </button>
            <button
              className={`pp-browse-services__chip${
                filters.category === "food"
                  ? " pp-browse-services__chip--active"
                  : ""
              }`}
              onClick={() =>
                dispatch(setFilter({ name: "category", value: "food" }))
              }
            >
              Food
            </button>
            <button
              className={`pp-browse-services__chip${
                filters.category === "music"
                  ? " pp-browse-services__chip--active"
                  : ""
              }`}
              onClick={() =>
                dispatch(setFilter({ name: "category", value: "music" }))
              }
            >
              Music
            </button>
            <button
              className={`pp-browse-services__chip${
                filters.category === "rental"
                  ? " pp-browse-services__chip--active"
                  : ""
              }`}
              onClick={() =>
                dispatch(setFilter({ name: "category", value: "rental" }))
              }
            >
              Rentals
            </button>
          </div>
          <div className="pp-browse-services__categories-dropdown-wrape">
            <CustomSelect
              className="pp-browse-services__categories-dropdown"
              value={filters.category || ""}
              onChange={(v) =>
                dispatch(setFilter({ name: "category", value: v }))
              }
              options={[
                { label: "All Categories", value: "" },
                { label: "Entertainment", value: "entertainment" },
                { label: "Characters", value: "characters" },
                { label: "Food", value: "food" },
                { label: "Music", value: "music" },
                { label: "Rentals", value: "rental" },
              ]}
            />
          </div>
        </div>

        {/* Mobile drawer for filters (<= 992px) */}
        <aside
          className={`pp-filter-drawer${
            isDrawerOpen ? " pp-filter-drawer--open" : ""
          }`}
          aria-hidden={!isDrawerOpen}
        >
          <div className="pp-filter-drawer__header">
            <span className="pp-filter-drawer__title">Filters & Sort</span>
            <button
              type="button"
              className="pp-filter-drawer__close"
              aria-label="Close filters"
              onClick={() => setIsDrawerOpen(false)}
            >
              <IoClose />
            </button>
          </div>
          <div className="pp-filter-drawer__body">
            <div className="pp-browse-services__control">
              <label className="pp-browse-services__control-label">
                Sort by
              </label>
              <CustomSelect
                className="pp-browse-services__select"
                value={filters.sort || ""}
                onChange={(v) =>
                  dispatch(setFilter({ name: "sort", value: v }))
                }
                options={[
                  { label: "Relevance", value: "" },
                  { label: "Lowest price", value: "price-asc" },
                  { label: "Highest price", value: "price-desc" },
                  { label: "Nearest location", value: "distance-asc" },
                  { label: "Farthest location", value: "distance-desc" },
                ]}
              />
            </div>
          </div>
        </aside>
      </header>
      <div className="pp-browse-services__grid">
        {filteredServices.map((service) => (
          <ProductCard key={service.id} {...service} />
        ))}
      </div>
    </section>
  );
};

export default BrowseServices;
