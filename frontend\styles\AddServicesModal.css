.srv-modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--pp-z-index-modal);
}

.srv-modal {
  width: 100%;
  max-width: 640px;
  overflow: auto;
  background: var(--pp-clr-primary);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-12);
  box-shadow: var(--pp-shdw-lg);
  padding: var(--pp-sp-16);
}

.srv-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: var(--pp-sp-12);
  border-bottom: 1px solid var(--pp-clr-border);
}

.srv-modal-title {
  font-size: 24px;
  color: var(--pp-clr-text-main);
  font-weight: 600;
}

.srv-modal-close {
  background: transparent;
  border: none;
  color: var(--pp-clr-text-main);
  cursor: pointer;
}

.srv-modal-subtitle {
  margin-top: var(--pp-sp-16);
  margin-bottom: var(--pp-sp-12);
  color: var(--pp-clr-text-gray);
  font-size: var(--pp-font-extra-small);
}

.srv-modal-grid {
  display: grid;
  gap: var(--pp-sp-12);

  grid-template-columns: repeat(2, minmax(0, 1fr));
}

/* ---------------------------
   Base Card Styling
---------------------------- */
.srv-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--pp-sp-8);
  padding: var(--pp-sp-16);
  background: var(--pp-clr-primary);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-8);
  cursor: pointer;
  transition: background 0.2s ease, box-shadow 0.2s ease, border-color 0.2s ease;
}

.srv-card:hover {
  box-shadow: var(--pp-shdw-md);
}

/* ---------------------------
   Hover + Active Accent Colors
---------------------------- */
.srv-card.entertainment:hover,
.srv-card.entertainment.active {
  background: #cbe5ff; /* light blue */
  border: none;
}

.srv-card.music:hover,
.srv-card.music.active {
  background: #ffd1ce; /* light red */
  border-color: none;
}

.srv-card.food:hover,
.srv-card.food.active {
  background: #bbf6c4; /* light green */
  border-color: none;
}

.srv-card.rental:hover,
.srv-card.rental.active {
  background: #cfd4ff; /* light purple */
  border-color: none;
}

/* ---------------------------
   Icon & Text
---------------------------- */
.srv-card-icon {
  width: 74px;
  height: auto;
  padding: 10px;
}

.srv-card-title {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-main);
  font-weight: 600;
}

.srv-card-desc {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
}

/* ---------------------------
   Responsive
---------------------------- */
@media (max-width: 480px) {
  .srv-modal {
    max-width: 90vw;
    height: 95vh;
  }
  .srv-modal-grid {
    grid-template-columns: 1fr;
  }
  .srv-modal-title {
    font-size: 20px;
  }
}
