/* Container */
.CustomerLayout-root {
  margin: 0 auto;
  padding: var(--pp-sp-24) var(--pp-sp-24) ;
}

.CustomerLayout-content {
  display: block;
}

.CustomerLayout-title {
  font-size: var(--pp-font-heading4);
  font-weight: 700;
  margin: 0 0 var(--pp-sp-16);
}

/* Tabs */
.CustomerLayout-tabs {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--pp-sp-24);
}


.CustomerLayout-tablist {
  display: flex;
  gap: var(--pp-sp-20);
  flex-wrap: wrap;
  width: 100%;
  border-bottom: var(--pp-bor-w-1) solid var(--pp-clr-border-light);
}

.CustomerLayout-tab {
  display: inline-block;
  padding: 0 0 var(--pp-sp-8);
  color: var(--pp-clr-text-main);
  text-decoration: none;
  transition: color 160ms ease;
  border-bottom: var(--pp-bor-w-1) solid transparent;
}

.CustomerLayout-tab:hover {
  color: var(--pp-clr-secondary);
}

.CustomerLayout-tab--active {
  color: var(--pp-clr-text-main);
  border-bottom-color: var(--pp-clr-secondary);
}

/* Responsive */
@media (max-width: 600px) {
  .CustomerLayout-tablist { display: none; }
}

/* Container */
.CustomerLayout-root {
  margin: 0 auto;
  padding: var(--pp-sp-24) var(--pp-sp-24);
}

.CustomerLayout-content {
  display: block;
}

.CustomerLayout-title {
  font-size: var(--pp-font-heading4);
  font-weight: 700;
  margin: 0 0 var(--pp-sp-16);
}

/* Tabs */
.CustomerLayout-tabs {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--pp-sp-24);
}



.CustomerLayout-tablist {
  display: flex;
  gap: var(--pp-sp-20);
  flex-wrap: wrap;
  width: 100%;
  border-bottom: var(--pp-bor-w-1) solid var(--pp-clr-border-light);
}

.CustomerLayout-tab {
  display: inline-block;
  padding: 0 0 var(--pp-sp-8);
  color: var(--pp-clr-text-main);
  text-decoration: none;
  transition: color 160ms ease;
  border-bottom: var(--pp-bor-w-1) solid transparent;
}

.CustomerLayout-tab:hover {
  color: var(--pp-clr-secondary);
}

.CustomerLayout-tab--active {
  color: var(--pp-clr-text-main);
  border-bottom-color: var(--pp-clr-secondary);
}

/* Reuse note: CustomerAccountSettings-* classes are applied in markup.
   Styling is inherited from CustomerLayout-* rules to avoid duplication. */

/* Responsive */
@media (max-width: 600px) {
  .CustomerLayout-tablist { display: none; }
}


