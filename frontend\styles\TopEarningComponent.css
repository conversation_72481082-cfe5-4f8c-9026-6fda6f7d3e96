@import "./ReportsShared.css";

.report-label {
  font-size: var(--pp-font-small-font);
}
.report-field .custom-select-box {
  width: 255px;
}


/* ==== Report Container ==== */
.sr-report-content,
.report-content {
  max-width: 1400px;
  margin: var(--pp-sp-24) auto 0 auto;
  background: var(--pp-clr-primary);
  border-radius: var(--pp-bor-rad-16);
}

/* ==== Header Row ==== */
.sr-header-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
 
}

.sr-title {
  display: flex;
  gap: 10px;
  align-items: center;
}

.sr-main-title {
  font-size: var(--pp-font-heading6);
  font-weight: 700;
  color: var(--pp-clr-text-main);
  letter-spacing: 0.01em;
}

.sr-sub-title {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
}

/* ==== Download Button ==== */

/* ==== Overview and Chart Grid ==== */
.sr-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--pp-sp-32);
  margin-bottom: var(--pp-sp-40);
  margin-top: 32px;
}

.sr-overview-card {
  border-radius: var(--pp-bor-rad-16);
  padding: var(--pp-sp-24) var(--pp-sp-20);
  min-width: 220px;

  border: 1px solid var(--pp-clr-border);
}

.sr-overview-title {
  font-size: var(--pp-font-heading6);
  font-weight: 600;
  color: var(--pp-clr-secondary);
  margin-bottom: var(--pp-sp-16);
}

.sr-overview-list {
  margin: 0;
  padding: 0;
}

.sr-overview-item {
  display: flex;
  justify-content: space-between;
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-main);
  margin-bottom: var(--pp-sp-10);
  font-weight: 400;
}

.sr-overview-item dt {
  color: var(--pp-clr-text-gray);
}

.sr-overview-item dd {
  font-weight: 500;
  color: var(--pp-clr-text-main);
}

/* ==== Chart Card ==== */
.sr-chart-card {
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-16);
  padding: var(--pp-sp-24);
}

.sr-chart-wrap {
  padding: var(--pp-sp-18);
  background: var(--pp-clr-primary);
  border-radius: var(--pp-bor-rad-10);
  box-shadow: var(--pp-shdw-sm);
}

.sr-chart-svg {
  width: 100%;
  height: 96px;
  display: block;
}

/* ==== Table Section ==== */
.sr-section {
  margin-top: var(--pp-sp-32);
}

.sr-section-title {
  font-size: var(--pp-font-base2-font);
  font-weight: 600;
  color: var(--pp-clr-secondary);
  margin-bottom: var(--pp-sp-14);
}

.sr-table-wrap {
  overflow-x: auto;
  background: var(--pp-clr-primary);
  border-radius: var(--pp-bor-rad-8);
}

.sr-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-main);
  background: var(--pp-clr-primary);
  min-width: 650px;
  overflow-x: auto;
}
.TopEarningComponent .sr-table thead tr,
.TopEarningComponent .sr-table tbody tr {
  display: grid;
  grid-template-columns: 8.5fr 1.5fr 2fr;
  
}
.sr-table th {
  font-weight: 600;
  color: var(--pp-clr-secondary);
  padding: var(--pp-sp-8) var(--pp-sp-4);
  border-bottom: 2px solid var(--pp-clr-border-light);
  text-align: start;
  white-space: nowrap;
}

.sr-table td {
  padding: var(--pp-sp-8) var(--pp-sp-4);
}

.sr-table tfoot td {
  font-weight: 700;
  color: var(--pp-clr-secondary);
  border-top: 2px solid var(--pp-clr-border-light);
}

.sr-total-value {
  font-size: var(--pp-font-base2-font);
  font-weight: 700;
  color: var(--pp-clr-secondary);
}

/* ==== Responsive ==== */
@media (max-width: 900px) {
  .sr-report-content,
  .report-content {
    padding: var(--pp-sp-20) var(--pp-sp-10);
  }
  .sr-grid {
    grid-template-columns: 1fr;
    gap: var(--pp-sp-16);
  }
  .sr-table {

    min-width: 320px;
    
  }
  .TopEarningComponent .sr-table thead tr,
.TopEarningComponent .sr-table tbody tr {
  display: grid;
  grid-template-columns: 6.5fr 3.5fr 4fr;
  
}
}

@media (max-width: 600px) {
  .sr-table th,
  .sr-table td {
    font-size: 13px;
    padding: var(--pp-sp-10) var(--pp-sp-10);
  }
  .sr-section-title {
    font-size: 16px;
    margin-bottom: 8px;
  }

  .sr-main-title,
  .sr-overview-title {
    font-size: 18px;
  }
  .sr-header-row{
    flex-direction: column;
    align-items: start;
    gap: var(--pp-sp-8);
  }
}
