.dashboard-main {
  background: var(--pp-clr-bg);
}

.dashboard-main__info-banner {
  margin-bottom: var(--pp-sp-20);
  background: var(--pp-clr-blue-bg);
  border-radius: var(--pp-bor-rad-8);
  padding: var(--pp-sp-16) var(--pp-sp-20);
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.dashboard-banner-span {
  color: var(--pp-clr-secondary);
  font-size: var(--pp-font-small-font);
  text-decoration: underline;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
}

/* Metrics grid */
.dashboard-main__metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: var(--pp-sp-32);
  margin-bottom: var(--pp-sp-32);
  width: 100%;
  justify-items: center;
}
.dashboard-main__metric-label {
  color: var(--pp-clr-secondary);
  font-size: var(--pp-font-extra-small);
  margin-bottom: var(--pp-sp-8);
  text-decoration: underline;
}
.dashboard-main__metric-value {
  font-family: "Inter", sans-serif;
  font-size: var(--pp-font-heading2);
  color: var(--pp-clr-text-main);
  font-weight: 600;
}

/* ----- Responsive Widget GRID ----- */

/* Desktop ≥1100px: 2 col first row, 3 col second row */
@media (min-width: 767px) {
  .dashboard-main__widgets {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-areas:
      "bookings sales sales"
      "next reviews services";
    gap: var(--pp-sp-24);
    width: 100%;
    margin: 0 auto;
  }
  .dashboard-main__card--bookings {
    grid-area: bookings;
  }
  .dashboard-main__card--sales {
    grid-area: sales;
  }
  .dashboard-main__card--next {
    grid-area: next;
  }
  .dashboard-main__card--reviews {
    grid-area: reviews;
  }
  .dashboard-main__card--services {
    grid-area: services;
  }
}

/* Tablet 700px–1099px: 2 col flexible layout */
@media (min-width: 700px) and (max-width: 766px) {
  .dashboard-main__widgets {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-areas:
      "bookings sales"
      "next reviews"
      "services services";
    gap: var(--pp-sp-20);
    width: 100%;
    margin: 0 auto;
  }
  .dashboard-main__card--bookings {
    grid-area: bookings;
  }
  .dashboard-main__card--sales {
    grid-area: sales;
  }
  .dashboard-main__card--next {
    grid-area: next;
  }
  .dashboard-main__card--reviews {
    grid-area: reviews;
  }
  .dashboard-main__card--services {
    grid-area: services;
  }
}

/* Mobile <700px: single column */

/* Card appearance */
.dashboard-main__card {
  background: var(--pp-clr-primary);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-12);

  padding: var(--pp-sp-20);
  display: flex;
  flex-direction: column;
  min-width: 0;
  min-height: 210px;
}
.dashboard-main__card--green {
  color: var(--pp-clr-text-main);
  border-radius: var(--pp-bor-rad-8);
}
.dashboard-main__card--border-blue {
  border: 1px solid var(--pp-clr-border);
}
.dashboard-main__card-title {
  font-size: var(--pp-font-heading5);
  color: var(--pp-clr-text-main);
  font-weight: 600;
  font-family: var(--pp-font-Metro-Sans);
  margin-bottom: var(--pp-sp-20);
}
.dashboard-main__card-section {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-8);
}
.dashboard-main__booking-row {
  display: flex;
  justify-content: space-between;
  font-size: var(--pp-font-small-font);
  color: var(--pp-clr-text-main);
  font-weight: 400;
  padding-bottom: var(--pp-sp-4);
}
.dashboard-main__booking-row span:last-child {
  font-weight: 700;
}
.dashboard-main__card-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--pp-sp-8);
}
.dashboard-main__chart-placeholder {
  width: 100%;
  height: 200px;
  min-height: 160px;
  margin-top: var(--pp-sp-16);
  margin-bottom: var(--pp-sp-8);
}

/* Next Booking */
.dashboard-main__next-booking {
  margin-left: 0;
  margin-bottom: var(--pp-sp-8);
  border-radius: var(--pp-bor-rad-12);
  position: relative;
  padding: 15px;
  background: #bbf6c4;
}
.dashboard-main__next-booking__title {
  font-weight: 600;
  font-size: var(--pp-font-small-font);
  color: var(--pp-clr-secondary);
  display: block;
}
.dashboard-main__next-booking__details,
.dashboard-main__next-booking__location {
  
  font-size: var(--pp-font-extra-small);
  display: block;
  margin-top: var(--pp-sp-4);
}
.dashboard-main__next-booking__arrow {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 17px;
  aspect-ratio: 1/1;
  color: var(--pp-clr-secondary);
  font-weight: 700;
}

/* General Responsive Padding */
@media (max-width: 699px) {
  .dashboard-main__card {
    min-height: unset;
  }
  .dashboard-main__metrics {
    grid-template-columns: 1fr 1fr;
  }
  .dashboard-main__widgets {
    display: grid;
    grid-template-columns: 1fr;
    grid-template-areas:
      "bookings"
      "sales"
      "next"
      "reviews"
      "services";
    gap: var(--pp-sp-16);
    width: 100%;
    margin: 0 auto;
  }
  .dashboard-main__card--bookings {
    grid-area: bookings;
  }
  .dashboard-main__card--sales {
    grid-area: sales;
  }
  .dashboard-main__card--next {
    grid-area: next;
  }
  .dashboard-main__card--reviews {
    grid-area: reviews;
  }
  .dashboard-main__card--services {
    grid-area: services;
  }
}
