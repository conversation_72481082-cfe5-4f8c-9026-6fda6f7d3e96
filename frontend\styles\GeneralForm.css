/* =========================
   📝 General Form
   ========================= */

.gf-general-form {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-24);
}

.gf-section-title {
  font-size: var(--pp-font-heading5);
  font-weight: 600;
  color: var(--pp-clr-text-main);
  margin: 0;
  line-height: 1.3;
}

.gf-section-description {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  margin: var(--pp-sp-8) 0 0 0;
  line-height: 1.5;
}

/* Form Groups */
.gf-form-group {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-8);
}

.gf-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--pp-sp-20);
}

.gf-form-label {
  font-size: var(--pp-font-extra-small);
  font-weight: 600;
  color: var(--pp-clr-text-main);
  line-height: 1.3;
}

.gf-form-input {
  padding: var(--pp-sp-12) var(--pp-sp-16);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-8);
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-main);
  background-color: var(--pp-clr-primary);
  transition: all 0.3s ease;
  line-height: 1.4;
}

.gf-form-input:focus {
  outline: none;
  border-color: var(--pp-clr-secondary);
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.gf-form-input::placeholder {
  color: var(--pp-clr-text-gray);
}

.gf-form-textarea {
  padding: var(--pp-sp-12) var(--pp-sp-16);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-8);
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-main);
  background-color: var(--pp-clr-primary);
  transition: all 0.3s ease;
  line-height: 1.4;
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.gf-form-textarea:focus {
  outline: none;
  border-color: var(--pp-clr-secondary);
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.gf-form-textarea::placeholder {
  color: var(--pp-clr-text-gray);
}

/* Character Count */
.gf-char-count {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  text-align: right;
  margin-top: var(--pp-sp-4);
  line-height: 1.3;
}

.gf-char-count.gf-char-count-warning {
  color: var(--pp-clr-warning);
}

.gf-char-count.gf-char-count-error {
  color: var(--pp-clr-error);
}

/* Toggle Section */
.gf-toggle-section {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-16);
  padding: var(--pp-sp-20);
  background-color: var(--pp-clr-border-light);
  border-radius: var(--pp-bor-rad-12);
  border: 1px solid var(--pp-clr-border);
}

.gf-toggle-group {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-12);
}

.gf-toggle-description {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  margin: var(--pp-sp-4) 0 0 0;
  line-height: 1.5;
}

/* Email Section */
.gf-email-section {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-16);
  padding: var(--pp-sp-20);
  background-color: var(--pp-clr-border-light);
  border-radius: var(--pp-bor-rad-12);
  border: 1px solid var(--pp-clr-border);
}

.gf-email-description {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  margin: var(--pp-sp-8) 0 0 0;
  line-height: 1.5;
}
.gf-general-form .pp-count {
  color: #727272;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
/* Responsive */
@media (max-width: 768px) {
  .gf-general-form {
    gap: var(--pp-sp-20);
  }

  .gf-form-row {
    grid-template-columns: 1fr;
    gap: var(--pp-sp-16);
  }

  .gf-toggle-section,
  .gf-email-section {
    padding: var(--pp-sp-16);
  }
}

@media (max-width: 480px) {
  .gf-general-form {
    gap: var(--pp-sp-16);
  }

  .gf-form-row {
    gap: var(--pp-sp-12);
  }

  .gf-form-input,
  .gf-form-textarea {
    padding: var(--pp-sp-10) var(--pp-sp-12);
    font-size: var(--pp-font-small-font);
  }

  .gf-toggle-section,
  .gf-email-section {
    padding: var(--pp-sp-12);
  }

  .gf-section-description,
  .gf-toggle-description,
  .gf-email-description {
    font-size: var(--pp-font-small-font);
  }
}
