import React from "react";
import PopupModal from "../common/PopupModal";
import "../../styles/CustomerBookingDetailsModal.css";

const CustomerBookingDetailsModal = ({ open, booking, onClose }) => {
  if (!open || !booking) return null;

  // Gather vendor info from the first service (customize as needed)
  const firstService = booking.services?.[0] || {};
  const vendor = {
    name: firstService.vendor || "N/A",
    email: "<EMAIL>", // Replace with data if you have it
    phone: "(*************",
  };

  return (
    <PopupModal isOpen={open} onClose={onClose} >
      {/* Header inside content so close stays top-right */}
      <div className="BookingDetailsModal-header">
        <div>
          <div className="BookingDetailsModal-title">{vendor.name}</div>
          <div className="BookingDetailsModal-tag">Upcoming</div>
        </div>
        <button className="pp-btn-secondary">Cancel Booking</button>
      </div>

      {/* Divider */}
      <div className="BookingDetailsModal-divider" />

      {/* Main content grid */}
      <div className="BookingDetailsModal-main">
        <div className="BookingDetailsModal-details">
          <div className="BookingDetailsModal-sectionTitle">Booking Details</div>

          <div className="BookingDetailsModal-keyVals">
            <div className="BookingDetailsModal-keyVal-row">
              <span className="BookingDetailsModal-label">Date</span>
              <div>{booking.date ? (new Date(booking.date)).toLocaleDateString("en-US", { month: "long", day: "2-digit", year: "numeric" }) : "N/A"}</div>
            </div>
            <div className="BookingDetailsModal-keyVal-row">
              <span className="BookingDetailsModal-label">Services</span>
              <div>
                {booking.services?.map((s, idx) => (
                  <div key={idx}>
                    {s.title}
                    <div className="BookingDetailsModal-time">{s.time}</div>
                  </div>
                ))}
              </div>
            </div>
            <div className="BookingDetailsModal-keyVal-row">
              <span className="BookingDetailsModal-label">Company</span>
              <div>N/A</div>
            </div>
            <div className="BookingDetailsModal-keyVal-row">
              <span className="BookingDetailsModal-label">Event Address</span>
              <div>Home<br />1720 N 272 E<br />Apt 5E<br />Salt Lake City, UT 85515</div>
            </div>
          </div>
        </div>

        <div className="BookingDetailsModal-images">
          <a href="#" className="BookingDetailsModal-viewOrder">View Order Details</a>
          <div className="BookingDetailsModal-imageList">
            {booking.services?.flatMap((s, idx) => 
              s.images?.map((img, imgIdx) => (
                <img
                  src={img}
                  alt={s.title}
                  key={`${idx}-${imgIdx}`}
                  className="BookingDetailsModal-img"
                />
              ))
            )}
          </div>
        </div>
      </div>

      {/* Divider */}
      <div className="BookingDetailsModal-divider" />

      {/* Vendor Info */}
      <div className="BookingDetailsModal-main BookingDetailsModal-footer">
        <div >
          <div className="BookingDetailsModal-sectionTitle">Vendor Info</div>
          <div className="BookingDetailsModal-kvrow"><span>Name</span><span>{vendor.name}</span></div>
          <div className="BookingDetailsModal-kvrow"><span>Email</span><span>{vendor.email}</span></div>
          <div className="BookingDetailsModal-kvrow"><span>Phone</span><span>{vendor.phone}</span></div>
        </div>
        <button className="pp-btn-secondary">Message Vendor</button>
      </div>
    </PopupModal>
  );
};

export default CustomerBookingDetailsModal;