import { createAsyncThunk } from '@reduxjs/toolkit';
import { refreshAccessToken, forceLogout } from './authSlice';
import { ApiError } from './apiUtils';

/**
 * Redux middleware to handle automatic token refresh
 * Intercepts API errors and attempts to refresh tokens when needed
 */
export const authMiddleware = (store) => (next) => (action) => {
  // Handle API errors that might indicate expired tokens
  if (action.type?.endsWith('/rejected') && action.payload?.status === 401) {
    const state = store.getState();
    
    // Only attempt refresh if user is currently authenticated
    if (state.auth.isAuthenticated) {
      // Attempt to refresh token
      store.dispatch(refreshAccessToken())
        .unwrap()
        .then(() => {
          // Token refreshed successfully, could retry the original action
          console.log('Token refreshed successfully');
        })
        .catch(() => {
          // Refresh failed, force logout
          store.dispatch(forceLogout());
        });
    }
  }

  return next(action);
};

/**
 * Higher-order function to wrap async thunks with automatic retry on token refresh
 */
export const withTokenRefresh = (asyncThunk) => {
  return createAsyncThunk(
    asyncThunk.typePrefix + '/withRefresh',
    async (arg, { dispatch, rejectWithValue, getState }) => {
      try {
        // First attempt
        return await asyncThunk.payloadCreator(arg, { dispatch, rejectWithValue, getState });
      } catch (error) {
        // If 401 error and user is authenticated, try to refresh token
        if (error instanceof ApiError && error.status === 401 && getState().auth.isAuthenticated) {
          try {
            await dispatch(refreshAccessToken()).unwrap();
            // Retry the original request
            return await asyncThunk.payloadCreator(arg, { dispatch, rejectWithValue, getState });
          } catch (refreshError) {
            // Refresh failed, force logout
            dispatch(forceLogout());
            return rejectWithValue({
              message: 'Session expired. Please login again.',
              status: 401
            });
          }
        }
        
        // Re-throw original error if not a 401 or refresh not applicable
        throw error;
      }
    }
  );
};
