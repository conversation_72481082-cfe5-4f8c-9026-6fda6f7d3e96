/**
 * Image URL utility functions for handling proxy URLs
 */

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
const BASE_URL = API_BASE_URL.replace('/api', ''); // Remove /api to get base URL

/**
 * Convert S3 URLs to proxy URLs
 * This is useful for backward compatibility with existing data
 */
export const convertToProxyUrl = (url) => {
    if (!url) return url;

    // If it's already a proxy URL, return as is
    if (url.includes('/api/files/image/')) {
        return url;
    }

    // If it's an S3 URL, convert to proxy URL
    if (url.includes('amazonaws.com')) {
        const keyMatch = url.match(/\.com\/(.+)$/);
        if (keyMatch) {
            const encodedKey = encodeURIComponent(keyMatch[1]);
            return `${BASE_URL}/api/files/image/${encodedKey}`;
        }
    }

    // If it's a relative path (starts with /), make it absolute
    if (url.startsWith('/')) {
        return `${BASE_URL}${url}`;
    }

    // Return as is for other URLs (http/https)
    return url;
};

/**
 * Get the proper image URL for display
 * Handles various URL formats and ensures they work with the proxy system
 */
export const getImageUrl = (url) => {
    if (!url) return null;

    // Handle data URLs (for previews)
    if (url.startsWith('data:')) {
        return url;
    }

    // Convert to proxy URL if needed
    return convertToProxyUrl(url);
};

/**
 * Check if a URL is a proxy URL
 */
export const isProxyUrl = (url) => {
    return url && url.includes('/api/files/image/');
};

/**
 * Extract the S3 key from a proxy URL
 */
export const extractS3Key = (proxyUrl) => {
    if (!isProxyUrl(proxyUrl)) return null;

    const match = proxyUrl.match(/\/api\/files\/image\/(.+)$/);
    return match ? decodeURIComponent(match[1]) : null;
};

export default {
    convertToProxyUrl,
    getImageUrl,
    isProxyUrl,
    extractS3Key
};
