.OrderDetailsModal-overlay {
  background: rgba(0, 0, 0, 0.22);
  position: fixed;
  inset: 0;
  z-index: var(--pp-z-index-modal);
  display: flex;
  align-items: center;
  justify-content: center;
}
.OrderDetailsModal-modal {
  background: var(--pp-clr-primary);
  border-radius: var(--pp-bor-rad-16);
  width: 100%;
  max-width: 800px;
  box-shadow: var(--pp-shdw-lg);
  position: relative;
  padding: var(--pp-sp-24) var(--pp-sp-24) var(--pp-sp-20) var(--pp-sp-24);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-height: 92vh;
}

.OrderDetailsModal-close-div {
  display: flex;

  justify-content: end;
}
.OrderDetailsModal-close {
  background: none;
  border: none;
  font-size: 2rem;
  color: var(--pp-clr-text-main);
  cursor: pointer;
  border-radius: var(--pp-bor-rad-round);

  display: flex;
  align-items: center;
  justify-content: center;
}
.OrderDetailsModal-ordertitle {
  font-size: var(--pp-font-heading4);
  font-weight: 400;
}
.OrderDetailsModal-ordernum {
  font-weight: 600;
}
.OrderDetailsModal-total {
  font-size: var(--pp-font-heading4);
  font-weight: 600;
  color: var(--pp-clr-secondary);
}
.OrderDetailsModal-header {
  display: flex;
  padding-bottom: var(--pp-sp-16);
  border-bottom: 1px solid var(--pp-clr-border-light);
  margin-bottom: var(--pp-sp-16);

  justify-content: space-between;
  align-items: flex-start;
  gap: var(--pp-sp-16);
}
.OrderDetailsModal-headerContent {
  flex-grow: 1;
}
.OrderDetailsModal-orderdate-col{
  display: grid;
  width: 100%;
}
.OrderDetailsModal-orderdate-row {
  background: #e6faed;
  border-radius: var(--pp-bor-rad-8);
  display: flex;
  gap: var(--pp-sp-12);
  
  padding: var(--pp-sp-10) var(--pp-sp-16);
  font-size: var(--pp-font-extra-small);
  margin-top: var(--pp-sp-16);
  font-weight: 500;
  color: var(--pp-clr-secondary);
}
.OrderDetailsModal-orderdate-label {
  color: var(--pp-clr-text-gray);
}

.OrderDetailsModal-infoRow {
  margin-bottom: var(--pp-sp-24);
}
.OrderDetailsModal-groupTitle {
  text-decoration: underline;
  font-weight: bold;
  margin: var(--pp-sp-24) 0 var(--pp-sp-8) 0;
  font-size: var(--pp-font-small-font);
}
.OrderDetailsModal-tableWrap {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}
.OrderDetailsModal-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: var(--pp-sp-12);
}
.OrderDetailsModal-table th,
.OrderDetailsModal-table td {
  border-bottom: 1px solid var(--pp-clr-border-light);
  font-size: var(--pp-font-extra-small);
  padding: var(--pp-sp-16) var(--pp-sp-8);
  text-align: left;
  vertical-align: top;
}
.OrderDetailsModal-table th {
  border-bottom: 2px solid var(--pp-clr-text-gray);
  color: var(--pp-clr-secondary);
  font-weight: 600;
}

/* Column alignment helpers for table */
.OrderDetailsModal-colQty {
  text-align: center;
  width: 64px;
}
.OrderDetailsModal-colMoney {
  text-align: right;
  white-space: nowrap;
}
.OrderDetailsModal-colCenter {
  text-align: center;
}

/* Apply alignment to both header and cell when class is present */
.OrderDetailsModal-table th.OrderDetailsModal-colQty,
.OrderDetailsModal-table td.OrderDetailsModal-colQty { text-align: center; }
.OrderDetailsModal-table th.OrderDetailsModal-colMoney,
.OrderDetailsModal-table td.OrderDetailsModal-colMoney { text-align: right; }
.OrderDetailsModal-table th.OrderDetailsModal-colCenter,
.OrderDetailsModal-table td.OrderDetailsModal-colCenter { text-align: center; }

/* Link styles to match design */
.OrderDetailsModal-serviceLink {
  text-decoration: underline;
  color: inherit;
  font-weight: 600;
}
.OrderDetailsModal-eventLink {
  text-decoration: underline;
  color: var(--pp-clr-secondary);
}
.OrderDetailsModal-summary {
 
  margin-top: var(--pp-sp-24);
  padding-top: var(--pp-sp-12);
  font-size: var(--pp-font-extra-small);
}
.OrderDetailsModal-summaryRow-total{
  font-weight: 600;
  
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0 0 var(--pp-sp-12) 0;
}
.OrderDetailsModal-summaryRow-total-last{
  font-weight: 600;
  border-bottom: 1px solid var(--pp-clr-border-light);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--pp-sp-12) 0 var(--pp-sp-12) 0;
  border-top: 1px solid var(--pp-clr-border-light);
}
.OrderDetailsModal-summaryRow {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0 0 var(--pp-sp-12) 0;
}

.OrderDetailsModal-summaryRow--paid {
  font-size: var(--pp-font-base2-font);
  color: var(--pp-clr-secondary);
  font-weight: 700;
  border-bottom: 2px solid var(--pp-clr-text-gray);

  margin-top: var(--pp-sp-8);
  padding-top: var(--pp-sp-8);
}
.OrderDetailsModal-footer {
  margin-top: var(--pp-sp-24);
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  gap: var(--pp-sp-16);
}
.OrderDetailsModal-receiptNote {
  
  font-size: var(--pp-font-extra-small);

  align-items: center;
  gap: var(--pp-sp-8);
}
.OrderDetailsModal-contact{
 display: flex;
 flex-wrap: wrap;
 gap: var(--pp-sp-8);
 align-items: center;
 font-weight: 600; 
}
.OrderDetailsModal-contact a{
  text-decoration: underline;
}
.OrderDetailsModal-helpIcon {
  font-size: 1.2em;
 

  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.OrderDetailsModal-downloadBtn {
  min-width: 180px;
  font-size: var(--pp-font-base2-font);
}

@media (max-width: 750px) {
  .OrderDetailsModal-modal {
    padding: var(--pp-sp-12) var(--pp-sp-12);
    max-width: 95%; /* Adjust max-width for better fit on smaller screens */
    min-width: 0;
  }
  .OrderDetailsModal-total {
    /* Removed absolute positioning, rely on flexbox */
  }
  .OrderDetailsModal-header {
    flex-direction: column; /* Stack header items vertically */
    align-items: flex-start;
    min-height: auto; /* Remove fixed min-height */
    margin-bottom: 0;
    gap: 0;
  }
  .OrderDetailsModal-infoRow {
    margin-bottom: var(--pp-sp-16);
  }
  .OrderDetailsModal-footer {
    flex-direction: column; /* Stack footer items vertically */
    align-items: center; /* Center footer items */
    text-align: center;
    gap: var(--pp-sp-12);
  }
  .OrderDetailsModal-downloadBtn {
    width: 100%; /* Make download button full width */
    min-width: unset;
  }
  .OrderDetailsModal-tableWrap {
    overflow-x: auto;
  }
  .OrderDetailsModal-table {
    min-width: 640px; /* keep columns readable; scroll container handles overflow */
  }
}

@media (max-width: 480px) {
  .OrderDetailsModal-contact{
    justify-content: center;
    margin-top: 3px;
  }
}
