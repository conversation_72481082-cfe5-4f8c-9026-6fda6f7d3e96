/*-- Service Details Main styles --*/
.pp-service-details {
  padding: 80px 0;
  color: var(--pp-clr-text-main);
  background: var(--pp-clr-bg, #fff);
}

/*-- Container & Grid --*/
.pp-service-details .pp-service-details__grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--pp-sp-40);
  align-items: flex-start;
}

/*-- Media and Gallery --*/
.pp-service-details .pp-service-details__media {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-16, 16px);
}
.pp-service-details .pp-service-details__main-image {
  width: 100%;

  border-radius: var(--pp-bor-rad-12, 16px);
  box-shadow: var(--pp-shdw-md, 0px 4px 6px rgba(0, 0, 0, 0.08));
  object-fit: cover;
  aspect-ratio: 1 / 1;
  min-height: 320px;
  background: #ececec;
}
.pp-service-details .pp-service-details__thumbs {
  display: flex;
  gap: var(--pp-sp-8, 8px);
  margin-top: var(--pp-sp-8, 8px);
  flex-wrap: wrap;
}
.pp-service-details .pp-service-details__thumb {
  width: 80px;
  aspect-ratio: 1/1;
  object-fit: cover;
  border-radius: var(--pp-bor-rad-8, 8px);
  box-shadow: var(--pp-shdw-sm, 0px 1px 2px rgba(0, 0, 0, 0.08));
  cursor: pointer;
  border: 2px solid transparent;
  transition: border-color 0.2s;
}
.pp-service-details .pp-service-details__thumb:hover,
.pp-service-details .pp-service-details__thumb.active {
  border-color: var(--pp-clr-secondary, #000);
}

/*-- Meta, Card, Title, Price --*/
.pp-service-details .pp-service-details__meta {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-16, 16px);

  border-radius: var(--pp-bor-rad-12, 16px);
  background: var(--pp-clr-primary, #fff);
  min-width: 320px;
}
.pp-service-details .pp-service-details__title {
  font-family: var(--pp-font-Playfair-Display, serif);
  font-size: var(--pp-font-heading2);
  font-weight: 700;
  margin: 0;
  line-height: 1.1;
}
.pp-service-details .pp-service-details__subtitle {
  color: var(--pp-clr-secondary);
  font-size: var(--pp-font-base2-font, 18px);
  margin: 0;
  font-weight: 600;
  display: flex;
  gap: var(--pp-sp-8);
  align-items: center;
  cursor: pointer;
  text-decoration: underline;
}
.pp-service-details .pp-service-details__price {
  font-size: var(--pp-font-heading5, 22px);
  color: var(--pp-clr-secondary, #000);

  font-weight: 600;
}
.pp-service-details .pp-service-details__price strong {
  font-size: var(--pp-font-heading5, 22px);
  color: var(--pp-clr-secondary, #000);
  margin-top: var(--pp-sp-4, 4px);
  font-family: "Inter", sans-serif;
  font-weight: 500;
}
.pp-service-details .pp-service-details__description {
  font-size: var(--pp-font-small-font, 17px);
  color: var(--pp-clr-text-main, #000);
  margin: 0 0 var(--pp-sp-16, 16px) 0;
  font-weight: 400;
  border-bottom: 1px solid var(--pp-clr-border);
  padding-bottom: var(--pp-sp-32);
}
.pp-service-details .pp-service-details__section-title {
  font-size: var(--pp-font-heading5, 22px);
  font-weight: 700;
  margin: 0 0 var(--pp-sp-8, 8px) 0;
}
.pp-service-details .pp-service-details__section {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-8, 8px);
  margin-bottom: var(--pp-sp-16, 16px);
}

/*-- Option Cards & Lists --*/
.pp-service-details .pp-option-list {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-12, 12px);
}

/* Clickable option card */
.pp-service-details .pp-option-card {
  cursor: pointer;
  display: flex;
  align-items: flex-start;
  gap: var(--pp-sp-12, 12px);
  padding: var(--pp-sp-16, 16px);
  border: 1px solid var(--pp-clr-border, #d2d2d2);
  border-radius: var(--pp-bor-rad-12, 16px);
  background: var(--pp-clr-primary, #fff);
  transition: background 0.2s, border-color 0.2s;
  position: relative;
}
.pp-service-details .pp-option-card:hover{
  border-color: #000;
}
.pp-service-details .pp-option-card--selected {
  box-shadow: var(--pp-shdw-md);
  background-color: #f4f4f4;
  border-color: #000;
}
.pp-service-details .pp-option-card__content {
  pointer-events: none;
  width: 100%;
}
.pp-service-details .pp-option-card__header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.pp-service-details .pp-option-card__title {
  font-size: var(--pp-font-heading6, 20px);
  font-weight: 600;
}
.pp-service-details .pp-option-card__price h6 {
  font-weight: 600;
  font-size: var(--pp-font-base2-font, 18px);
  font-family: "Inter", sans-serif;
}
.pp-service-details .pp-option-card__price span {
  font-weight: 600;
  font-size: var(--pp-font-base2-font, 18px);
}
.pp-service-details .pp-option-card__price{
  font-weight: 600;
  font-size: var(--pp-font-heading6, 20px);

}


/* Package duration styling for rental category */
.pp-service-details .pp-package-duration {
  color: #888;
  font-size: var(--pp-font-small-font, 17px);
  font-weight: 400;
  margin-top: 4px;
  line-height: 1.2;
  margin-bottom: 10px;
}

/*-- Add-ons --*/
.pp-service-details .pp-addon-list {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-32, 32px);
  margin-top: var(--pp-font-extra-small-font);
}

.pp-service-details .pp-addon-item {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-10, 10px);
}

.pp-service-details .pp-addon-item__title {
  font-size: var(--pp-font-heading6, 20px);
  font-weight: 700;
  margin: 0;
  line-height: 0.8;
  color: var(--pp-clr-text-main, #000);
}

.pp-service-details .pp-addon-item__description {
  font-size: var(--pp-font-small-font, 17px);
  color: var(--pp-clr-text-main, #000);
  margin: 0;
  line-height: 1.5;
}

.pp-service-details .pp-addon-item__buttons {
  display: flex;
  gap: var(--pp-sp-12, 12px);
  align-items: center;
}
.pp-service-details .Addon-container {
  display: grid;
  gap: 0px;
  align-items: center;
  border: 1px solid #d2d2d2;
  border-radius: 12px;
  grid-template-columns: 1fr 1fr;
}
.pp-service-details .pp-addon-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--pp-sp-12, 12px) var(--pp-sp-16, 16px);
  border: 1px solid #d2d2d2;
  border-radius: 12px;
  background: var(--pp-clr-primary, #fff);
  color: var(--pp-clr-text-main, #000);
  font-size: var(--pp-font-heading6, 20px);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  text-align: center;
  height: 100%;
  font-family: var(--pp-font-Metro-Sans, sans-serif);
}

.pp-service-details .pp-addon-button--no {
  border-color: transparent;
  border-width: 1px;
}

.pp-service-details .pp-addon-button--yes {
  border-color: transparent;
  border-width: 1px;
}

.pp-service-details .pp-addon-button--selected {
  border-color: var(--pp-clr-secondary, #000);
  border-width: 2px;
}

.pp-service-details .pp-addon-button--no.pp-addon-button--selected {
  border-color: var(--pp-clr-secondary, #000);
  border-width: 2px;
}

.pp-service-details .pp-addon-button--yes span:first-child {
  font-weight: 600;
  margin-bottom: var(--pp-sp-4, 4px);
}

.pp-service-details .pp-addon-button--yes span:last-child {
  font-size: var(--pp-font-heading6, 20px);
  font-weight: 500;
  font-family: "Inter", sans-serif;
}

/*-- Multiple Workers Section --*/

.pp-service-details .pp-workers-label {
  font-size: var(--pp-font-small-font, 17px);
  color: var(--pp-clr-secondary);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8, 8px);
}

.pp-service-details .pp-workers-label::before {
  content: "🎯";
  font-size: 16px;
}

/* Animation for the section when it appears */

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/*-- Booking Form --*/
.pp-service-details .pp-booking-form {
  display: grid;
  grid-template-columns: 1fr; /* 2 columns for the grid */
  margin-bottom: var(--pp-sp-8, 8px);
  gap: var(--pp-sp-8, 8px);
}

/* Make the second and third fields span only the first column */
.pp-service-details .pp-booking-form .pp-field:nth-child(n + 3) {
  grid-column: 1 / 2; /* occupy first column only */
}

.pp-service-details .pp-field {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-8, 8px);
}
.pp-service-details .pp-label {
  font-size: var(--pp-font-small-font, 17px);
  color: var(--pp-clr-secondary);
  font-weight: 500;
}
.pp-service-details .pp-input,
.pp-service-details .pp-select {
  height: 40px;
  padding: 0 var(--pp-sp-12, 12px);
  border: 1px solid var(--pp-clr-border, #d2d2d2);
  border-radius: var(--pp-bor-rad-8, 8px);
  background: var(--pp-clr-primary, #fff);
  color: var(--pp-clr-text-main, #000);
  font-size: var(--pp-font-base2-font, 18px);
  font-family: var(--pp-font-Metro-Sans, sans-serif);
}
.pp-service-details .pp-input:focus,
.pp-service-details .pp-select:focus {
  outline: none;

  box-shadow: none;
}
/*-- Total section --*/
.pp-service-details .pp-total-row {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: var(--pp-sp-12, 12px) 0 var(--pp-font-heading3) 0;
  border-bottom: 1px solid var(--pp-clr-border, #d2d2d2);
}

.pp-service-details .pp-total-row__value {
  font-weight: 600;
  font-size: 28px;
  color: var(--pp-clr-secondary, #000);
  font-family: "Inter", sans-serif;
}

/*-- Actions --*/
.pp-service-details .pp-service-details__actions {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-12, 12px);
  margin-top: var(--pp-sp-12, 12px);
  width: 100%;
  align-items: flex-start;
}
.pp-service-details .querymessage {
  font-size: var(--pp-font-extra-small-font, 16px);
  font-weight: 600;
  text-decoration: underline;
  color: var(--pp-clr-secondary);
  display: flex;
  align-items: center;
  gap: 10px;
}
.pp-booking-form .custom-select-box{
  width: 128px;
}
.pp-service-details .querymessage span {
  background-color: var(--pp-clr-secondary);
  padding: 2px 8px;
  color: var(--pp-clr-primary);
  border-radius: 4px;
  font-size: var(--pp-font-extra-small-font, 16px);
}
.pp-service-details .small-size {
  margin-top: 30px;
}
.pp-service-details .small-size .guaranteeSection {
  padding: var(--pp-sp-32) var(--pp-sp-16);
}
.pp-service-details .small-size .guaranteeSection p {
  font-size: var(--pp-font-heading6);
}
.pp-service-details .small-size .guaranteeSection img {
  width: 100px;
  aspect-ratio: 1 / 1;
  top: 20%;
}
@media (max-width: 1024px) {
  .pp-service-details .pp-service-details__grid {
    display: grid;

    gap: 20px;
    align-items: flex-start;
  }
}
@media (max-width: 767px) {
  .pp-service-details .pp-service-details__grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--pp-sp-40);
    align-items: flex-start;
  }
  .pp-service-details .pp-service-details__main-image {
    width: 100%;
  }
}
@media (max-width: 700px) {
  .pp-service-details .pp-booking-form {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .pp-service-details .pp-addon-item__buttons {
    flex-direction: column;
    gap: var(--pp-sp-8, 8px);
  }

  .pp-service-details .pp-addon-button {
    min-width: 100%;
  }
}
@media (max-width: 412px) {
  .pp-service-details {
    padding: 32px 0;
  }
}
