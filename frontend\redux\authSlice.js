import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { authApi, vendorApi, setAccessToken, setUserData, clearAuthData, getUserData } from './apiUtils';
import { toast } from 'react-toastify';

/**
 * Async thunk for user registration
 */
export const registerUser = createAsyncThunk(
  'auth/register',
  async (userData, { rejectWithValue }) => {
    try {
      const response = await authApi.register(userData);

      // Show success message
      toast.success('Registration successful! Please check your email for verification.');

      return response;
    } catch (error) {
      // Show error toast
      toast.error(error.message || 'Registration failed. Please try again.');

      return rejectWithValue({
        message: error.message,
        status: error.status,
        data: error.data
      });
    }
  }
);

/**
 * Async thunk for user login
 */
export const loginUser = createAsyncThunk(
  'auth/login',
  async (credentials, { rejectWithValue }) => {
    try {
      const response = await authApi.login(credentials);

      // Store token and user data
      if (response.data?.accessToken) {
        setAccessToken(response.data.accessToken);
      }
      if (response.data?.user) {
        setUserData(response.data.user);
      }

      // For vendors, check onboarding status to determine redirect
      let vendorOnboardingStatus = null;
      if (response.data?.user?.role === 'vendor') {
        try {
          const onboardingResponse = await vendorApi.getOnboardingStatus();
          vendorOnboardingStatus = onboardingResponse.data;
        } catch (onboardingError) {
          console.warn('Could not fetch vendor onboarding status:', onboardingError);
          // Don't fail login if onboarding status check fails
        }
      }

      // Show success message
      toast.success(`Welcome back, ${response.data?.user?.firstName || 'User'}!`);

      return {
        ...response,
        vendorOnboardingStatus
      };
    } catch (error) {
      // Show error toast
      toast.error(error.message || 'Login failed. Please try again.');

      return rejectWithValue({
        message: error.message,
        status: error.status,
        data: error.data
      });
    }
  }
);

/**
 * Async thunk for user logout
 */
export const logoutUser = createAsyncThunk(
  'auth/logout',
  async (logoutData = {}, { rejectWithValue }) => {
    try {
      await authApi.logout(logoutData);
      clearAuthData();

      // Show success message
      toast.success('Logged out successfully');

      return {};
    } catch (error) {
      // Even if logout fails on server, clear local data
      clearAuthData();

      // Show warning message
      toast.warn('Logged out locally (server error)');

      return rejectWithValue({
        message: error.message,
        status: error.status,
        data: error.data
      });
    }
  }
);

/**
 * Async thunk for refreshing access token
 */
export const refreshAccessToken = createAsyncThunk(
  'auth/refresh',
  async (_, { rejectWithValue }) => {
    try {
      const response = await authApi.refreshToken();
      
      if (response.data?.accessToken) {
        setAccessToken(response.data.accessToken);
      }
      
      return response;
    } catch (error) {
      // If refresh fails, clear auth data
      clearAuthData();
      return rejectWithValue({
        message: error.message,
        status: error.status,
        data: error.data
      });
    }
  }
);

/**
 * Async thunk for getting user profile
 */
export const getUserProfile = createAsyncThunk(
  'auth/getProfile',
  async (_, { rejectWithValue }) => {
    try {
      const response = await authApi.getProfile();
      
      if (response.data?.user) {
        setUserData(response.data.user);
      }
      
      return response;
    } catch (error) {
      return rejectWithValue({
        message: error.message,
        status: error.status,
        data: error.data
      });
    }
  }
);

/**
 * Initial state for authentication
 */
const initialState = {
  user: getUserData(), // Load user from localStorage on init
  isAuthenticated: !!getUserData(),
  loading: false,
  error: null,
  registrationSuccess: false,
  requiresVerification: false,
  vendorOnboardingStatus: null, // Store vendor onboarding status
};

/**
 * Authentication slice
 */
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // Clear error state
    clearError: (state) => {
      state.error = null;
    },
    
    // Clear registration success state
    clearRegistrationSuccess: (state) => {
      state.registrationSuccess = false;
    },
    
    // Initialize auth state from localStorage (for app startup)
    initializeAuth: (state) => {
      const userData = getUserData();
      state.user = userData;
      state.isAuthenticated = !!userData;
    },
    
    // Manual logout (for cases where we need to logout without API call)
    forceLogout: (state) => {
      state.user = null;
      state.isAuthenticated = false;
      state.error = null;
      state.requiresVerification = false;
      state.vendorOnboardingStatus = null;
      clearAuthData();
    },
  },
  extraReducers: (builder) => {
    // Register user
    builder
      .addCase(registerUser.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.registrationSuccess = false;
      })
      .addCase(registerUser.fulfilled, (state, action) => {
        state.loading = false;
        state.registrationSuccess = true;
        state.requiresVerification = action.payload.data?.requiresVerification || false;
        // Don't set user as authenticated until email is verified
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || 'Registration failed';
      });

    // Login user
    builder
      .addCase(loginUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload.data?.user || null;
        state.isAuthenticated = true;
        state.error = null;
        state.requiresVerification = action.payload.data?.requiresVerification || false;
        // Store vendor onboarding status if available
        if (action.payload.vendorOnboardingStatus) {
          state.vendorOnboardingStatus = action.payload.vendorOnboardingStatus;
        }
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || 'Login failed';
        state.isAuthenticated = false;
        state.user = null;
        state.vendorOnboardingStatus = null;
      });

    // Logout user
    builder
      .addCase(logoutUser.pending, (state) => {
        state.loading = true;
      })
      .addCase(logoutUser.fulfilled, (state) => {
        state.loading = false;
        state.user = null;
        state.isAuthenticated = false;
        state.error = null;
        state.requiresVerification = false;
        state.vendorOnboardingStatus = null;
      })
      .addCase(logoutUser.rejected, (state, action) => {
        state.loading = false;
        // Still logout locally even if server request failed
        state.user = null;
        state.isAuthenticated = false;
        state.requiresVerification = false;
      });

    // Refresh token
    builder
      .addCase(refreshAccessToken.pending, (state) => {
        // Don't show loading for token refresh
      })
      .addCase(refreshAccessToken.fulfilled, (state) => {
        // Token refreshed successfully, maintain current state
      })
      .addCase(refreshAccessToken.rejected, (state) => {
        // Token refresh failed, logout user
        state.user = null;
        state.isAuthenticated = false;
        state.error = 'Session expired. Please login again.';
        state.requiresVerification = false;
      });

    // Get user profile
    builder
      .addCase(getUserProfile.pending, (state) => {
        state.loading = true;
      })
      .addCase(getUserProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload.data?.user || state.user;
      })
      .addCase(getUserProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || 'Failed to get user profile';
      });
  },
});

export const { 
  clearError, 
  clearRegistrationSuccess, 
  initializeAuth, 
  forceLogout 
} = authSlice.actions;

export default authSlice.reducer;

// Selectors
export const selectAuth = (state) => state.auth;
export const selectUser = (state) => state.auth.user;
export const selectIsAuthenticated = (state) => state.auth.isAuthenticated;
export const selectAuthLoading = (state) => state.auth.loading;
export const selectAuthError = (state) => state.auth.error;
export const selectRegistrationSuccess = (state) => state.auth.registrationSuccess;
export const selectRequiresVerification = (state) => state.auth.requiresVerification;
export const selectVendorOnboardingStatus = (state) => state.auth.vendorOnboardingStatus;
