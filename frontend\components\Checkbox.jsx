import React from 'react';

const Checkbox = ({ 
  id, 
  label, 
  checked, 
  onChange, 
  name, 
  required = false,
  disabled = false,
  className = '',
  ...props 
}) => {
  const handleChange = (e) => {
    if (onChange) {
      onChange(e);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      if (onChange) {
        const newEvent = {
          ...e,
          target: {
            ...e.target,
            checked: !checked
          }
        };
        onChange(newEvent);
      }
    }
  };

  return (
    <div className={`pp-checkbox-wrapper ${className}`}>
      <input
        type="checkbox"
        id={id}
        name={name}
        checked={checked}
        onChange={handleChange}
        required={required}
        disabled={disabled}
        className="pp-checkbox"
        onKeyDown={handleKeyDown}
        {...props}
      />
      {label && (
        <label 
          htmlFor={id} 
          className="pp-checkbox-label"
          tabIndex={disabled ? -1 : 0}
          onKeyDown={handleKeyDown}
        >
          {label}
        </label>
      )}
    </div>
  );
};

export default Checkbox;
