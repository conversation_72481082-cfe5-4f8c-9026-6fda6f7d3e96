import React from "react";
import { useSelector } from "react-redux";
import "../styles/VendorStore.css";
import ProductCard from "../components/ProductCard";
import VendorStoreheroImg from "../src/assets/images/VendorStoreHeroimg.svg"

// Example selector; replace 'selectHeroes' with your actual selector
const VendorStore = () => {
  const products = useSelector((state) => state.services.items); // or use selectHeroes(state)

  return (
    <div className="nero-heroes">
      <section className="nero-heroes__banner">
        <img
          src={VendorStoreheroImg}
          alt="Heroes"
          className="nero-heroes__banner-img"
          loading="lazy"
          decoding="async"
        />
        <h1 className="nero-heroes__title">Nero’s Heroes</h1>
      </section>
      <div className="nero-heroes__products-grid">
        {products.map((product) => (
          <ProductCard key={product.id} {...product} />
        ))}
      </div>
    </div>
  );
};

export default VendorStore;
