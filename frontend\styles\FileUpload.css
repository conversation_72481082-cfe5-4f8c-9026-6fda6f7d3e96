/* File Upload Component Styles */
.file-upload-container {
  margin-bottom: 1rem;
}

.file-upload-label {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}

.file-upload-label .required {
  color: #e74c3c;
  margin-left: 0.25rem;
}

.file-upload-description {
  font-size: 0.875rem;
  color: #666;
  margin: 0.25rem 0 0 0;
}

.file-upload-area {
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  background-color: #fafafa;
}

.file-upload-area:hover {
  border-color: #007bff;
  background-color: #f8f9ff;
}

.file-upload-area.error {
  border-color: #e74c3c;
  background-color: #fdf2f2;
}

.file-upload-area.success {
  border-color: #27ae60;
  background-color: #f2f8f2;
}

.file-upload-dropzone {
  cursor: pointer;
  padding: 2rem 1rem;
}

.file-upload-dropzone svg {
  color: #666;
  margin-bottom: 1rem;
}

.file-upload-dropzone p {
  margin: 0.5rem 0;
  color: #333;
}

.file-upload-hint {
  font-size: 0.875rem;
  color: #666 !important;
  font-style: italic;
}

.file-upload-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.file-preview-image {
  max-width: 200px;
  max-height: 200px;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.file-preview-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.file-preview-icon svg {
  font-size: 2rem;
  color: #666;
}

.file-preview-icon span {
  font-size: 0.875rem;
  color: #333;
  max-width: 200px;
  word-break: break-word;
  text-align: center;
}

.file-upload-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  justify-content: center;
}

.file-upload-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.file-upload-progress {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #007bff;
}

.file-upload-success {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #27ae60;
  font-weight: 500;
}

.file-upload-remove {
  background: none;
  border: none;
  color: #e74c3c;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.file-upload-remove:hover {
  background-color: #fdf2f2;
}

.file-upload-remove:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.file-upload-error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #e74c3c;
  font-size: 0.875rem;
  margin-top: 0.5rem;
  padding: 0.5rem;
  background-color: #fdf2f2;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
}

/* Spinner animation */
.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .file-upload-area {
    padding: 1rem;
  }
  
  .file-upload-dropzone {
    padding: 1.5rem 0.5rem;
  }
  
  .file-preview-image {
    max-width: 150px;
    max-height: 150px;
  }
  
  .file-upload-actions {
    flex-direction: column;
    gap: 0.5rem;
  }
}
