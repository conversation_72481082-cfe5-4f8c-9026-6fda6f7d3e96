// utils/fixCategories.js
const mongoose = require('mongoose');
const Category = require('../models/Category');

const FIXED_CATEGORIES = [
    {
        _id: new mongoose.Types.ObjectId("66a111111111111111111111"),
        name: "Characters",
        description: "Hire characters for your events",
        icon: "🎭",
        image: "/images/categories/characters.png",
        sortOrder: 1
    },
    {
        _id: new mongoose.Types.ObjectId("66a222222222222222222222"),
        name: "Entertainment",
        description: "Entertainment services like magicians, clowns, performers",
        icon: "🎉",
        image: "/images/categories/entertainment.png",
        sortOrder: 2
    },
    {
        _id: new mongoose.Types.ObjectId("66a333333333333333333333"),
        name: "Rentals",
        description: "Book rentals like photo booths, props, and furniture",
        icon: "📸",
        image: "/images/categories/rentals.png",
        sortOrder: 3
    },
    {
        _id: new mongoose.Types.ObjectId("66a444444444444444444444"),
        name: "Food",
        description: "Food trucks, catering, and event food services",
        icon: "🍔",
        image: "/images/categories/food.png",
        sortOrder: 4
    },
    {
        _id: new mongoose.Types.ObjectId("66a555555555555555555555"),
        name: "Music",
        description: "Musicians, DJs, and bands for your event",
        icon: "🎶",
        image: "/images/categories/music.png",
        sortOrder: 5
    }
];

const insertFixedCategories = async () => {
    for (const cat of FIXED_CATEGORIES) {
        const exists = await Category.findOne({ name: cat.name });
        if (!exists) {
            await Category.create(cat);
            console.log(`Inserted category: ${cat.name}`);
        }
    }
};

module.exports = insertFixedCategories;
