const express = require('express');
const router = express.Router();
const AdminController = require('../controllers/adminController');
const { auth, authorize } = require('../middlewares/auth');
const { validateRequest } = require('../middlewares/validation');
const { rateLimiting } = require('../middlewares/rateLimiting');
const Joi = require('joi');

// Admin authentication middleware - all routes require admin role
router.use(auth);
router.use(authorize(['admin']));

// Validation schemas
const updateUserStatusSchema = Joi.object({
  isActive: Joi.boolean().required(),
  reason: Joi.string().max(500)
});

const updateVendorStatusSchema = Joi.object({
  status: Joi.string().valid('pending', 'approved', 'rejected', 'suspended').required(),
  reason: Joi.string().max(500),
  feedback: Joi.string().max(1000)
});

const updateServiceStatusSchema = Joi.object({
  status: Joi.string().valid('active', 'inactive', 'suspended', 'under_review').required(),
  reason: Joi.string().max(500)
});

const createCategorySchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  slug: Joi.string().min(2).max(100),
  description: Joi.string().max(500),
  parentId: Joi.string().hex().length(24),
  icon: Joi.string().max(100),
  featured: Joi.boolean().default(false),
  sortOrder: Joi.number().min(0).default(0)
});

const updateCategorySchema = Joi.object({
  name: Joi.string().min(2).max(100),
  slug: Joi.string().min(2).max(100),
  description: Joi.string().max(500),
  parentId: Joi.string().hex().length(24),
  icon: Joi.string().max(100),
  featured: Joi.boolean(),
  sortOrder: Joi.number().min(0)
});

// Dashboard and analytics
router.get('/dashboard', AdminController.getDashboard);
router.get('/analytics/platform', AdminController.getPlatformAnalytics);
router.get('/analytics/financial', AdminController.getFinancialAnalytics);

// User management
router.get('/users', AdminController.getUsers);
router.get('/users/:userId', AdminController.getUserById);
router.put('/users/:userId/status', 
  rateLimiting.moderate, 
  validateRequest(updateUserStatusSchema), 
  AdminController.updateUserStatus
);

// Vendor management
router.get('/vendors/applications', AdminController.getVendorApplications);
router.put('/vendors/:vendorId/status', 
  rateLimiting.moderate, 
  validateRequest(updateVendorStatusSchema), 
  AdminController.updateVendorStatus
);

// Service management
router.get('/services', AdminController.getServices);
router.put('/services/:serviceId/status', 
  rateLimiting.moderate, 
  validateRequest(updateServiceStatusSchema), 
  AdminController.updateServiceStatus
);

// Booking management
router.get('/bookings', AdminController.getBookings);

// Category management
router.get('/categories', AdminController.getCategories);
router.post('/categories', 
  rateLimiting.moderate, 
  validateRequest(createCategorySchema), 
  AdminController.createCategory
);
router.put('/categories/:categoryId', 
  rateLimiting.moderate, 
  validateRequest(updateCategorySchema), 
  AdminController.updateCategory
);
router.delete('/categories/:categoryId', 
  rateLimiting.moderate, 
  AdminController.deleteCategory
);

// System monitoring and logs
router.get('/system/health', async (req, res) => {
  const startTime = process.hrtime();
  
  // Check database connection
  const mongoose = require('mongoose');
  const dbStatus = mongoose.connection.readyState === 1 ? 'connected' : 'disconnected';
  
  // Check memory usage
  const memoryUsage = process.memoryUsage();
  
  // Check uptime
  const uptime = process.uptime();
  
  // Calculate response time
  const [seconds, nanoseconds] = process.hrtime(startTime);
  const responseTime = seconds * 1000 + nanoseconds / 1000000;
  
  const health = {
    status: 'healthy',
    timestamp: new Date(),
    uptime: `${Math.floor(uptime / 3600)}h ${Math.floor((uptime % 3600) / 60)}m ${Math.floor(uptime % 60)}s`,
    database: {
      status: dbStatus,
      name: mongoose.connection.name
    },
    memory: {
      rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`,
      heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
      heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
      external: `${Math.round(memoryUsage.external / 1024 / 1024)}MB`
    },
    responseTime: `${responseTime.toFixed(2)}ms`,
    environment: process.env.NODE_ENV || 'development',
    nodeVersion: process.version
  };

  const { sendSuccess } = require('../utils/responseHelper');
  return sendSuccess(res, 'System health check completed', health);
});

router.get('/system/logs', async (req, res) => {
  const { level = 'info', limit = 100, startDate, endDate } = req.query;
  
  // This would typically read from log files or log database
  // For now, return mock log data
  const logs = [
    {
      timestamp: new Date(),
      level: 'info',
      message: 'User login successful',
      userId: '60f1b2b5e1b4c8a1b8c9d0e1',
      ip: '***********'
    },
    {
      timestamp: new Date(Date.now() - 60000),
      level: 'warning',
      message: 'High memory usage detected',
      details: { memoryUsage: '85%' }
    },
    {
      timestamp: new Date(Date.now() - 120000),
      level: 'error',
      message: 'Payment processing failed',
      error: 'Stripe API timeout',
      bookingId: '60f1b2b5e1b4c8a1b8c9d0e2'
    }
  ];

  const { sendSuccess } = require('../utils/responseHelper');
  return sendSuccess(res, 'System logs retrieved', {
    logs: logs.slice(0, parseInt(limit)),
    totalLogs: logs.length,
    level,
    dateRange: { startDate, endDate }
  });
});

// Content moderation
router.get('/moderation/reviews', async (req, res) => {
  const { status = 'flagged', page = 1, limit = 20 } = req.query;
  
  const Review = require('../models/Review');
  
  let query = {};
  if (status === 'flagged') {
    query.isFlagged = true;
  } else if (status === 'reported') {
    query['reports.0'] = { $exists: true };
  }
  
  const reviews = await Review.find(query)
    .populate('customerId', 'firstName lastName email')
    .populate('vendorId', 'firstName lastName')
    .populate('serviceId', 'title')
    .sort({ createdAt: -1 })
    .limit(limit * 1)
    .skip((page - 1) * limit);
    
  const total = await Review.countDocuments(query);
  
  const { sendSuccess } = require('../utils/responseHelper');
  return sendSuccess(res, 'Reviews for moderation retrieved', {
    reviews,
    pagination: {
      currentPage: parseInt(page),
      totalPages: Math.ceil(total / limit),
      totalItems: total,
      itemsPerPage: parseInt(limit)
    }
  });
});

router.get('/moderation/services', async (req, res) => {
  const { status = 'under_review', page = 1, limit = 20 } = req.query;
  
  const Service = require('../models/Service');
  
  const services = await Service.find({ status })
    .populate('vendorId', 'firstName lastName email')
    .populate('categoryId', 'name')
    .sort({ createdAt: -1 })
    .limit(limit * 1)
    .skip((page - 1) * limit);
    
  const total = await Service.countDocuments({ status });
  
  const { sendSuccess } = require('../utils/responseHelper');
  return sendSuccess(res, 'Services for moderation retrieved', {
    services,
    pagination: {
      currentPage: parseInt(page),
      totalPages: Math.ceil(total / limit),
      totalItems: total,
      itemsPerPage: parseInt(limit)
    }
  });
});

// Bulk operations
router.post('/bulk/users/status', rateLimiting.strict, async (req, res) => {
  const { userIds, isActive, reason } = req.body;
  
  const bulkUserStatusSchema = Joi.object({
    userIds: Joi.array().items(Joi.string().hex().length(24)).min(1).max(100).required(),
    isActive: Joi.boolean().required(),
    reason: Joi.string().max(500)
  });
  
  const { error } = bulkUserStatusSchema.validate(req.body);
  if (error) {
    const { sendBadRequest } = require('../utils/responseHelper');
    return sendBadRequest(res, error.details[0].message);
  }
  
  const User = require('../models/User');
  const result = await User.updateMany(
    { _id: { $in: userIds } },
    { isActive }
  );
  
  const { auditLogger } = require('../utils/logger');
  auditLogger.info('Bulk user status update', {
    userIds,
    isActive,
    reason,
    modifiedCount: result.modifiedCount,
    adminId: req.user.id
  });
  
  const { sendSuccess } = require('../utils/responseHelper');
  return sendSuccess(res, `${result.modifiedCount} users updated successfully`);
});

// Reports and exports
router.get('/reports/users', async (req, res) => {
  const { format = 'json', startDate, endDate } = req.query;
  
  let dateFilter = {};
  if (startDate && endDate) {
    dateFilter.createdAt = {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    };
  }
  
  const User = require('../models/User');
  const userStats = await User.aggregate([
    { $match: dateFilter },
    {
      $group: {
        _id: {
          role: '$role',
          month: { $month: '$createdAt' },
          year: { $year: '$createdAt' }
        },
        count: { $sum: 1 },
        activeCount: {
          $sum: { $cond: [{ $eq: ['$isActive', true] }, 1, 0] }
        },
        verifiedCount: {
          $sum: { $cond: [{ $eq: ['$isVerified', true] }, 1, 0] }
        }
      }
    },
    { $sort: { '_id.year': 1, '_id.month': 1 } }
  ]);
  
  if (format === 'csv') {
    // In a real implementation, you'd convert to CSV format
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename="users-report.csv"');
    return res.send('CSV export not implemented in this demo');
  }
  
  const { sendSuccess } = require('../utils/responseHelper');
  return sendSuccess(res, 'User report generated', {
    report: userStats,
    generatedAt: new Date(),
    dateRange: { startDate, endDate },
    format
  });
});

router.get('/reports/revenue', async (req, res) => {
  const { period = 'last_30_days', format = 'json' } = req.query;
  
  // Use the existing financial analytics but format for reporting
  const analytics = await AdminController.getFinancialAnalytics(req, { 
    json: (data) => data,
    status: () => ({ json: (data) => data })
  });
  
  if (format === 'csv') {
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename="revenue-report.csv"');
    return res.send('CSV export not implemented in this demo');
  }
  
  const { sendSuccess } = require('../utils/responseHelper');
  return sendSuccess(res, 'Revenue report generated', {
    ...analytics,
    generatedAt: new Date(),
    period,
    format
  });
});

// System configuration
router.get('/config', async (req, res) => {
  // Return non-sensitive configuration settings
  const config = {
    platform: {
      name: 'Party Pipeline',
      version: '1.0.0',
      maintenance: false,
      registrationOpen: true
    },
    features: {
      paymentProcessing: true,
      realTimeMessaging: true,
      emailNotifications: true,
      fileUploads: true
    },
    limits: {
      maxFileSize: 25 * 1024 * 1024, // 25MB
      maxImagesPerService: 10,
      maxServicesPerVendor: 50,
      sessionTimeout: 30 * 24 * 60 * 60 * 1000 // 30 days
    },
    integrations: {
      stripe: !!process.env.STRIPE_SECRET_KEY,
      storage: process.env.AWS_S3_BUCKET_NAME ? 's3' : 'local',
      email: !!process.env.EMAIL_API_KEY
    }
  };
  
  const { sendSuccess } = require('../utils/responseHelper');
  return sendSuccess(res, 'System configuration retrieved', config);
});

router.put('/config', rateLimiting.strict, async (req, res) => {
  const { maintenance, registrationOpen } = req.body;
  
  const configSchema = Joi.object({
    maintenance: Joi.boolean(),
    registrationOpen: Joi.boolean()
  });
  
  const { error } = configSchema.validate(req.body);
  if (error) {
    const { sendBadRequest } = require('../utils/responseHelper');
    return sendBadRequest(res, error.details[0].message);
  }
  
  // In a real implementation, this would update configuration in database
  const { auditLogger } = require('../utils/logger');
  auditLogger.info('System configuration updated', {
    changes: req.body,
    adminId: req.user.id
  });
  
  const { sendSuccess } = require('../utils/responseHelper');
  return sendSuccess(res, 'Configuration updated successfully', {
    updatedAt: new Date(),
    changes: req.body
  });
});

module.exports = router;
