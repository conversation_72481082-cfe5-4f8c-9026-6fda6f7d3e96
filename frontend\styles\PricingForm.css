/* =========================
   💰 Pricing Form
   ========================= */

.pf-pricing-form {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-32);
}

.pf-section {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-24);
}

.pf-section-title {
  font-size: var(--pp-font-heading5);
  font-weight: 600;
  color: var(--pp-clr-text-main);
  margin: 0;
  line-height: 1.3;
}

.pf-subsection-title {
  font-size: var(--pp-font-base2-font);
  font-weight: 600;
  color: var(--pp-clr-text-main);
  margin: 0 0 var(--pp-sp-16) 0;
  line-height: 1.3;
}

.pf-section-description {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  margin: 0 0 var(--pp-sp-16) 0;
  line-height: 1.5;
}

.pf-description {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);

  line-height: 1.5;
}

/* Form Groups */
.pf-form-group {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-8);
}

.pf-form-label {
  font-size: var(--pp-font-extra-small);
  font-weight: 500;
  color: var(--pp-clr-text-main);
  margin: 0;
}

.pf-form-input {
  width: 100%;
  padding: var(--pp-sp-12) var(--pp-sp-16);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-8);
  font-size: var(--pp-font-extra-small);
  font-family: var(--pp-font-Metro-Sans);
  background: var(--pp-clr-primary);
  transition: all 0.3s ease;
  color: var(--pp-clr-text-main);
}
.pf-form-input:focus {
  outline: none;
  border-color: var(--pp-clr-secondary);
}

.pf-form-input::placeholder {
  color: var(--pp-clr-text-gray);
}

.pf-form-textarea {
  width: 100%;
  padding: var(--pp-sp-12) var(--pp-sp-16);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-8);
  font-size: var(--pp-font-extra-small);
  font-family: var(--pp-font-Metro-Sans);
  background: var(--pp-clr-primary);
  transition: all 0.3s ease;
  color: var(--pp-clr-text-main);
  resize: vertical;
  min-height: var(--pp-textarea-min-height);
}

.pf-form-textarea:focus {
  outline: none;
  border-color: var(--pp-clr-secondary);
}

.pf-form-textarea::placeholder {
  color: var(--pp-clr-text-gray);
}

/* Form Rows */
.pf-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--pp-sp-16);
}

/* Price Inputs */
.pf-price-input {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8);
  position: relative;
}

.pf-currency {
  font-size: var(--pp-font-extra-small);
  font-weight: 600;
  color: var(--pp-clr-text-main);
  margin-right: var(--pp-sp-4);
  position: absolute;
  left: var(--pp-sp-16);
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}
.pf-price-input .pf-form-input {
  padding-left: calc(var(--pp-sp-16) + 14px);
}
.hourly-input {
  width: 100px !important;
}
.pf-unit {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  margin-left: var(--pp-sp-4);
}

/* Menu Items */
.pf-menu-item {
  border: 1px solid var(--pp-clr-border-light);
  border-radius: var(--pp-bor-rad-12);
  padding: var(--pp-sp-24);
  background-color: var(--pp-clr-primary);
}

.pf-note {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  font-style: italic;
  margin: var(--pp-sp-16) 0;
  padding: var(--pp-sp-12);
  background-color: var(--pp-clr-border-light);
  border-radius: var(--pp-bor-rad-8);
  border-left: 3px solid var(--pp-clr-text-gray);
}

.pf-btn-add {
  background-color: var(--pp-clr-secondary);
  color: var(--pp-clr-primary);
  border: none;
  border-radius: var(--pp-bor-rad-24);
  padding: var(--pp-sp-12) var(--pp-sp-24);
  font-size: var(--pp-font-extra-small);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: var(--pp-sp-16);
}

.pf-btn-add:hover {
  transform: translateY(-1px);
  box-shadow: var(--pp-shdw-sm);
}

/* Checkboxes */
.pf-checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-12);
  cursor: pointer;
  user-select: none;
}

.pf-checkbox {
  position: relative;
  width: var(--pp-sp-20);
  height: var(--pp-sp-20);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-4);
  background: var(--pp-clr-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.pf-checkbox:hover {
  border-color: var(--pp-clr-text-gray);
  transform: scale(1.05);
}

.pf-checkbox:focus {
  outline: none;
  border-color: var(--pp-clr-secondary);
}

.pf-checkbox:checked {
  background-color: var(--pp-clr-secondary) !important;
  border-color: var(--pp-clr-secondary) !important;
}

.pf-checkbox:checked::after {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: var(--pp-sp-12);
  height: var(--pp-sp-12);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.pf-checkbox-label {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-main);
  font-weight: 500;
  cursor: pointer;
}

/* Character Count */
.pf-char-count {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  text-align: right;
  margin-top: var(--pp-sp-4);
}

/* =========================
   📦 Package Cards
   ========================= */

.pf-packages-section {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-24);
}

.pf-package-card {
  border: 1px solid var(--pp-clr-border-light);
  border-radius: var(--pp-bor-rad-12);
  padding: var(--pp-sp-24);
  background-color: var(--pp-clr-primary);
  margin-bottom: var(--pp-sp-20);
  transition: all 0.3s ease;
  position: relative;
}

.pf-package-card:hover {
  border-color: var(--pp-clr-border);
  box-shadow: var(--pp-shdw-sm);
}

.pf-package-header {
  margin-bottom: var(--pp-sp-20);
  display: grid;
  justify-content: space-between;
  align-items: center;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.pf-package-form-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr auto;
  gap: var(--pp-sp-16);
  align-items: end;
  margin-bottom: var(--pp-sp-16);
}

.pf-package-form-row:first-child {
  grid-template-columns: 1fr;
}

.pf-package-form-group {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-8);
}

.pf-package-form-label {
  font-size: var(--pp-font-extra-small);
  font-weight: 500;
  color: var(--pp-clr-text-main);
  margin: 0;
}

.pf-package-form-input {
  width: 100%;
  padding: var(--pp-sp-12) var(--pp-sp-16);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-8);
  font-size: var(--pp-font-extra-small);
  font-family: var(--pp-font-Metro-Sans);
  background: var(--pp-clr-primary);
  transition: all 0.3s ease;
  color: var(--pp-clr-text-main);
}

.pf-package-form-input:focus {
  outline: none;
  border-color: var(--pp-clr-secondary);
}

.pf-package-form-input::placeholder {
  color: var(--pp-clr-text-gray);
}

.pf-package-form-textarea {
  width: 100%;
  padding: var(--pp-sp-12) var(--pp-sp-16);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-8);
  font-size: var(--pp-font-extra-small);
  font-family: var(--pp-font-Metro-Sans);
  background: var(--pp-clr-primary);
  transition: all 0.3s ease;
  color: var(--pp-clr-text-main);
  resize: vertical;
  min-height: var(--pp-textarea-min-height);
}

.pf-package-form-textarea:focus {
  outline: none;
  border-color: var(--pp-clr-secondary);
}

.pf-package-form-textarea::placeholder {
  color: var(--pp-clr-text-gray);
}

.pf-package-duration-input {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8);
}

.pf-package-price-input {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8);
  position: relative;
}

.pf-package-currency {
  font-size: var(--pp-font-extra-small);
  font-weight: 600;
  color: var(--pp-clr-text-main);
  margin-right: var(--pp-sp-4);
  position: absolute;
  left: var(--pp-sp-16);
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}
.pf-package-price-input .pf-package-form-input {
  padding-left: calc(var(--pp-sp-16) + 14px);
}

.pf-package-unit {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  margin-left: var(--pp-sp-4);
}

.pf-package-delete-group {
top: var(--pp-sp-20);
right: var(--pp-sp-20);
  position: absolute;
  padding: 0;
}

.pf-package-btn-delete {


  cursor: pointer;
 font-weight: 300;

  transition: all 0.3s ease;
  font-size: var(--pp-font-heading6);

}


.pf-package-btn-add {
  background-color: var(--pp-clr-secondary);
  color: var(--pp-clr-primary);
  border: none;
  border-radius: var(--pp-bor-rad-24);
  padding: var(--pp-sp-12) var(--pp-sp-24);
  font-size: var(--pp-font-extra-small);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: var(--pp-sp-16);
  align-self: flex-start;
}

.pf-package-btn-add:hover {
  transform: translateY(-1px);
  box-shadow: var(--pp-shdw-sm);
}

.pf-package-char-count {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  text-align: right;
  position: absolute;
  bottom: 12px;
  right: 5px;
}

/* =========================
   👥 Multiple Workers Grid
   ========================= */

.pf-multiple-workers-grid {
  display: grid;

  gap: var(--pp-sp-24);
}

.pf-multiple-workers-item {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-10);
  position: relative;
}

/* Responsive */
@media (max-width: 768px) {
  .pf-pricing-form {
    gap: var(--pp-sp-24);
  }

  .pf-section {
    gap: var(--pp-sp-20);
  }

  .pf-form-row {
    grid-template-columns: 1fr;
    gap: var(--pp-sp-12);
  }

  .pf-menu-item {
    padding: var(--pp-sp-20);
  }

  .pf-package-form-row {
    grid-template-columns: 125px 100px;
    gap: var(--pp-sp-12);
  
  }

  .pf-package-card {
    padding: var(--pp-sp-20);
  }

  .pf-multiple-workers-grid {
    gap: var(--pp-sp-20);
  }
  .pf-package-header {
 
    grid-template-columns: 1fr;
    gap: 0px;
  }
}

@media (max-width: 480px) {
  .pf-pricing-form {
    gap: var(--pp-sp-20);
  }

  .pf-section {
    gap: var(--pp-sp-16);
  }

  .pf-menu-item {
    padding: var(--pp-sp-16);
  }

  .pf-package-card {
    padding: var(--pp-sp-16);
  }

  .pf-form-input,
  .pf-form-textarea,
  .pf-package-form-input,
  .pf-package-form-textarea {
    padding: var(--pp-sp-10) var(--pp-sp-12);
  }

  .pf-multiple-workers-grid {
    gap: var(--pp-sp-16);
  }

}
@media (max-width:320px){
  .pf-package-form-row {
    grid-template-columns: 125px;
    gap: var(--pp-sp-12);
  
  }
}
/* =========================
   🔒 Fixed Pricing Method
   ========================= */

.pf-fixed-pricing-method {
  padding: var(--pp-sp-12) var(--pp-sp-16);
  border: 1px solid var(--pp-clr-border-light);
  border-radius: var(--pp-bor-rad-8);
  background-color: var(--pp-clr-border-light);
  font-size: var(--pp-font-extra-small);
  font-weight: 500;
  color: var(--pp-clr-text-gray);
  display: flex;
  align-items: center;
  cursor: not-allowed;
  user-select: none;
}
