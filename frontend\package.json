{"name": "party-pipeline", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.12", "axios": "^1.7.9", "gapi-script": "^1.2.0", "lenis": "^1.3.9", "lucide-react": "^0.542.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-google-recaptcha": "^3.1.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.8.1", "react-toastify": "^10.0.6", "recharts": "^3.1.2", "tailwindcss": "^4.1.12"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "6.3.5"}}