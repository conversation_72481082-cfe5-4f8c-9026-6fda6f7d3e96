.payment-visa{
  width: 40px;
}
.pp-payment-form {
    background: var(--pp-clr-primary);
    border-radius: var(--pp-bor-rad-16);
    
    display: flex;
    flex-direction: column;
    gap: var(--pp-sp-32);
    font-family: var(--pp-font-Metro-Sans);
  }

  .payment-card-input{
    display: flex;
    align-items: center;
    gap: var(--pp-sp-16);
  }
  @media (max-width: 768px) {
  
  }
  .pp-payment-form__h2 {
    font-family: var(--pp-font-Metro-Sans);
    font-size: var(--pp-font-heading5);
    font-weight: 600;
    
  }

  
  .pp-payment-form__section {
    display: flex;
    flex-direction: column;
    gap: var(--pp-sp-16);
  }
  .pp-payment-form__vendor-block {
    margin-bottom: var(--pp-sp-16);
  }
  .pp-payment-form__vendor-name {
    font-size: var(--pp-font-heading6);
    font-weight: 600;
    margin-bottom: var(--pp-sp-8);
  }
  .pp-payment-form__service-table {
    display: flex;
    flex-direction: column;
    width: 100%;
    background: var(--pp-clr-primary);
  }
  .pp-payment-form__table-header {
    font-weight: 600;
    
    border-bottom: 1px solid var(--pp-clr-secondary)!important;
  }
  .pp-payment-form__table-row {
    display: grid;
    grid-template-columns: 2.2fr 2fr 0.8fr 1.3fr 1.5fr 1.2fr;
    font-size: var(--pp-font-extra-small);
    gap: var(--pp-sp-8);
    align-items: flex-start;
    padding: var(--pp-sp-16) 0;
    border-bottom: 1px solid var(--pp-clr-border-light);
  }
  .pp-payment-form__table-row:last-child {
    border-bottom: none;
  }
  .pp-payment-form__service-name {
    font-weight: 600;
    font-size: var(--pp-font-extra-small);
  }
  .pp-payment-form__service-description {
    color: var(--pp-clr-text-gray);
    font-size: var(--pp-font-extra-small);
  }
  .pp-payment-form__summary-totals {
    margin-top: var(--pp-sp-16);
    margin-bottom: var(--pp-sp-8);
   
    display: flex;
    flex-direction: column;
    gap: var(--pp-sp-4);
  }
  .pp-payment-form__summary-row {
    display: flex;
    justify-content: space-between;
    font-size: var(--pp-font-extra-small);
    color: var(--pp-clr-text-main);
    font-weight: 600;
  }
  .pp-payment-form__summary-address {
    color: var(--pp-clr-text-gray);
    font-size: var(--pp-font-extra-small);
    
    margin: var(--pp-sp-4) 0;
  }
  .pp-payment-form__total-row {
    font-weight: 600;
    font-size: var(--pp-font-small-font);
    
    border-top: 1px solid var(--pp-clr-border-light);
    border-bottom: 1px solid var(--pp-clr-border-light);
    padding: var(--pp-sp-8) 0 var(--pp-sp-20) 0 ;
  }
  .pp-payment-form__payment-existing {
    margin-bottom: var(--pp-sp-10);
    max-width: 500px;
  }
  .pp-payment-form__input-label {
    font-size: var(--pp-font-extra-small);
    color: var(--pp-clr-text-main);
    font-weight: 600;
    margin-bottom: var(--pp-sp-4);
    display: block;
  }
  .pp-payment-form__payment-existing-wrap {
    margin-top: var(--pp-sp-4);
  }
  
  .pp-payment-form__payment-options {
    display: flex;
    flex-direction: column;
    gap: var(--pp-sp-12);
    margin: var(--pp-sp-8) 0 var(--pp-sp-16) 0;
  }
  .pp-payment-form__radio-option {
    display: flex;
    align-items: center;
    background: #F6F6F6;
   
    border-radius: var(--pp-bor-rad-8);
    gap: var(--pp-sp-12);
    font-size: var(--pp-font-extra-small);
    padding: var(--pp-sp-12) var(--pp-sp-16);
    font-family: var(--pp-font-Metro-Sans);
    cursor: pointer;
    transition: border-color .18s;
  }
.pp-payment-form__radio-option span{
  font-weight: 600;
}
  
  .pp-payment-form__radio-option:first-child {
    display: flex;
    align-items: center;
    background: #F6F6F6;
    border: none;
    
    border-radius: var(--pp-bor-rad-8);
    gap: var(--pp-sp-12);
    font-size: var(--pp-font-extra-small);
    padding: 0;
    font-family: var(--pp-font-Metro-Sans);
    cursor: pointer;
    transition: border-color .18s;
  }
  .pp-payment-form__radio-option input[type="radio"] {
    accent-color: var(--pp-clr-secondary);
    margin-right: var(--pp-sp-8);
  }
  .pp-payment-form__radio-option:hover {
    border-color: var(--pp-clr-secondary);
  }
  
  .pp-payment-form__cc-brands img,
  .pp-payment-form__pay-brand img {
    height: 22px;
    margin-left: var(--pp-sp-4);
    display: inline-block;
    vertical-align: middle;
  }
  .pp-cradit-card-form-input{
    width: 100%;
    padding: var(--pp-sp-16);
    
    border-radius: var(--pp-bor-rad-8);
    font-size: var(--pp-font-extra-small);
    font-family: var(--pp-font-Metro-Sans);
    background: #F6F6F6;
    transition: border-color 300ms ease;
  }
  .pp-payment-form__cc-brands {
    margin-left: auto;
    display: flex;
    gap: var(--pp-sp-4);
  }
  .pp-payment-form__pay-brand {
    margin-left: auto;
    display: flex;
    align-items: center;
  }
  .pp-payment-form__card-wrap {
    padding: var(--pp-sp-16);
    background: #F6F6F6;
  
    margin-bottom: var(--pp-sp-8);
    margin-top: var(--pp-sp-8);
  }
  .pp-payment-form__2col {
    display: grid;
    width: fit-content;
    gap: var(--pp-sp-16) var(--pp-sp-16);
  }

  @media (max-width: 1024px) {
    .pp-payment-form__2col{
      grid-template-columns: 1fr;
    }
  }
  @media (max-width: 700px) {
    .pp-payment-form__2col {
      grid-template-columns: 1fr;
      gap: var(--pp-sp-8);
    }
  }
  @media (max-width: 480px) {
    .pp-payment-form__2col {
      grid-template-columns: 1fr;
    }


    .pp-payment-form__cc-brands img,
  .pp-payment-form__pay-brand img {
    height: 18px;
  }
  }
  .pp-payment-form__field {
    display: flex;
    flex-direction: column;
    width: fit-content;
    gap: var(--pp-sp-4);
  }
  
  .pp-payment-form__input {
    width: 100%;
    padding: var(--pp-sp-16);
    border: 1px solid var(--pp-clr-border);
    border-radius: var(--pp-bor-rad-8);
    background: var(--pp-clr-primary);
    color: var(--pp-clr-text-main);
    font-size: var(--pp-font-extra-small);
    font-family: var(--pp-font-Metro-Sans);
    transition: border-color 0.18s;
  }
  .pp-payment-form__input:focus {
    border-color: var(--pp-clr-secondary);
    outline: none;
  }
  .pp-payment-form__privacy {
    font-size: var(--pp-font-extra-small);
    color: var(--pp-clr-text-gray);
    margin-bottom: var(--pp-sp-8);
  }
  .pp-payment-form__terms-row {
    display: flex;
    align-items: flex-start;
    gap: var(--pp-sp-10);
    margin-bottom: var(--pp-sp-16);
  }
  .pp-payment-form__checkbox {
    width: var(--pp-sp-20);
    height: var(--pp-sp-20);
    margin-top: 1.5px;
    accent-color: var(--pp-clr-secondary);
    border-radius: var(--pp-bor-rad-4);
  }
  .pp-payment-form__terms-label {
    font-size: var(--pp-font-extra-small);
    color: var(--pp-clr-text-main);
    line-height: 1.6;
  }
  .pp-payment-form__terms-label a {
    color: var(--pp-clr-blue-txt);
    text-decoration: underline;
    font-weight: 500;
  }
  .pp-payment-form__footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid var(--pp-clr-border-light);
    gap: var(--pp-sp-16);
    padding-top: var(--pp-sp-24);
  }
  .payment-card-expiration .pp-payment-form__field{
    width: 30%
  }
@media (max-width: 1040px){
.payment-card-expiration{
  flex-direction: column;
}  
.payment-card-expiration .pp-payment-form__field{
  width: 100%!important;
}
.payment-methods-heading{
  display: grid;
  gap: var(--pp-sp-4);
  width: 100%;
}
}


.pp-payment-form__select-wrap {
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid var(--pp-clr-border);
  border-radius: 8px;
  position: relative;
  flex: 1;
  
}

.pp-payment-form__select {
  padding: 8px 32px 8px 12px;
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-main);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: none;
  background: transparent;
  flex: 1;
  cursor: pointer;
  padding-right: 40px;
}

/* Custom dropdown arrow */
.pp-payment-form__select-wrap::after {
  content: '';
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  pointer-events: none;
}


  @media (max-width: 640px) {
    .pp-payment-form__footer {
      
      gap: var(--pp-sp-8);
      padding-top: var(--pp-sp-16);
    }
  }
  /* Make service table horizontally scrollable below 600px */
@media (max-width: 600px) {
  .pp-payment-form__service-table {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch; /* smooth mobile scroll */
    border: 1px solid var(--pp-clr-border-light);
    border-radius: var(--pp-bor-rad-8);
    padding-left: 5px;
  }

  /* Ensure rows keep min-width so scrolling kicks in */
  .pp-payment-form__table-row {
    min-width: 600px; /* adjust based on total content width */
  }
}
@media (max-width: 480px) {

  .pp-payment-form__radio-option{
    gap:0;
  }
  .pp-payment-form__select-wrap img{
    width: 40px;
  }
  .pp-payment-form__select{
    padding-right: 0;
  }
}
@media (max-width: 310px) {
  .pp-payment-form__card-wrap{
    padding: 0;
  }
}