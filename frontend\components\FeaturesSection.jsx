import starIcon from "../src/assets/images/fivestar.svg";
import yearsIcon from "../src/assets/images/twentyexperince.svg";
import piggyIcon from "../src/assets/images/piggyprice.svg";

export default function FeaturesSection() {
  const features = [
    { icon: starIcon, text: "Rated 5 Stars on Google" },
    { icon: yearsIcon, text: "Over 20 years of event experience" },
    { icon: piggyIcon, text: "Competitive pricing. Quality vendors." },
  ];

  return (
    <section className="features">
      {features.map((f, i) => (
        <div key={i} className="featureItem">
          <div className="innerfeatureItem">
            <img src={f.icon} alt="feature" className="featureIcon" loading="lazy" decoding="async" />
            <p className="featureText">{f.text}</p>
          </div>
        </div>
      ))}
    </section>
  );
}
