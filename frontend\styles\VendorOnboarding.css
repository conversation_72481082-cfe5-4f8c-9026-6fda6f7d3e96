.vo-root {
  padding: var(--pp-sp-40) 0;
  font-family: var(--pp-font-Metro-Sans), system-ui, -apple-system, "Segoe UI",
    Robot<PERSON>, Arial, sans-serif;
  background: var(--pp-clr-bg);
  color: var(--pp-clr-text-main);
}

.vo-stepper {
  display: flex;
  margin-bottom: var(--pp-sp-32);
  user-select: none;
}

.vo-step {
  flex: 1;
  display: flex;
  align-items: center;
  position: relative;
  font-weight: 600;
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  cursor: default;
  justify-content: center;
}

.vo-step.active {
  color: var(--pp-clr-secondary);
  font-weight: 700;
}

.vo-step.complete {
  color: var(--pp-clr-secondary);
}

.vo-step-indicator {
  width: 28px;
  height: 28px;
  border-radius: var(--pp-bor-rad-round);
  border: 2px solid currentColor;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--pp-sp-12);
  line-height: 1;
}

.vo-step.complete .vo-step-indicator {
  background: var(--pp-clr-secondary);
  color: var(--pp-clr-primary);
  border-color: var(--pp-clr-secondary);
}

.vo-step-line {
  position: absolute;
  height: 2px;
  background: var(--pp-clr-border);
  top: 50%;
  left: calc(100% + var(--pp-sp-12));
  right: -50%;
  z-index: -1;
}

.vo-step:last-child .vo-step-line {
  display: none;
}

.vo-card {
  background: var(--pp-clr-primary);
  border-radius: var(--pp-bor-rad-16);
  border: 1px solid var(--pp-clr-border);
  padding: var(--pp-sp-32);
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-24);
}
.vo-step-content {
  display: grid;
  gap: 20px;
}
.vo-step-content h2 {
  font-family: var(--pp-font-Metro-Sans);
  font-weight: 700;
  font-size: var(--pp-font-heading4);
  margin-bottom: var(--pp-sp-24);
}

.vo-radio-group {
  display: flex;
  gap: var(--pp-sp-24);
  margin-bottom: var(--pp-sp-32);
  width: 100%;
}

.vo-radio-label {
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-12);
  padding: var(--pp-sp-12) var(--pp-sp-24);
  min-width: 130px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--pp-sp-12);
  font-weight: 500;
  color: var(--pp-clr-text-main);
  user-select: none;
  transition: border-color 0.3s ease;
}

.vo-radio-label.selected {
  border-color: var(--pp-clr-secondary);
  background: var(--pp-clr-blue-bg);
}

.vo-radio-label input[type="radio"] {
  display: none;
}

.vo-radio-icon {
  font-size: 24px;
  color: var(--pp-clr-secondary);
}

.vo-input-group {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-8);
}

.vo-input-group label {
  font-weight: 600;
  font-size: var(--pp-font-small-font);
  color: var(--pp-clr-text-main);
  display: flex;
  align-items: center;
  gap: 6px;
}

.vo-info-icon {
  font-size: 16px;
  color: var(--pp-clr-text-gray);
  cursor: help;
}

.vo-input-group input,
.vo-input-group textarea,
.vo-input-group .custom-select-wrapper {
  padding: var(--pp-sp-12);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-8);
  font-size: var(--pp-font-base2-font);
  font-family: var(--pp-font-Metro-Sans);
  background: var(--pp-clr-primary);
}

.vo-input-2col {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--pp-sp-24);
}

.vo-input-3col {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: var(--pp-sp-16);
}

.vo-upload-icon {
  vertical-align: middle;
  margin-left: 6px;
  color: var(--pp-clr-secondary);
}

.vo-rep-section {
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-12);
  padding: var(--pp-sp-24);
  margin-bottom: var(--pp-sp-24);
  display: grid;
  gap: 20px;
}

.vo-rep-section legend {
  font-weight: 600;
  font-size: var(--pp-font-base-font);
  margin-bottom: var(--pp-sp-12);
}

.vo-checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8);
  cursor: pointer;
  font-weight: 500;
  font-size: var(--pp-font-base2-font);
  color: var(--pp-clr-text-main);
}

.vo-checkbox-label input {
  accent-color: var(--pp-clr-secondary);
  cursor: pointer;
}

.vo-buttons {
  display: flex;
  justify-content: flex-end;
  gap: var(--pp-sp-24);
}
@media (max-width: 992px) {
  .vo-step {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
  .vo-step-indicator {
    margin-right: 0;
  }
}
@media (max-width: 768px) {
  .vo-root {
    padding: var(--pp-sp-24) 0;
  }
  .vo-step-label {
    display: none;
  }
  .vo-input-2col,
  .vo-input-3col {
    grid-template-columns: 1fr;
    gap: var(--pp-sp-16);
  }

  .vo-root .pp-btn-primary,
  .vo-root .pp-btn-secondary {
    width: 100%;
  }
}

@media (max-width: 400px) {
  .vo-card {
    padding: 15px;
  }
  .vo-step-content h2 {
    margin-bottom: 0;
  }
  .vo-radio-group {
    margin-bottom: 0;
    gap: 10px;
  }
  .vo-radio-label {
    padding: var(--pp-sp-12);
    min-width: 0px;
    justify-content: space-between;
    gap: 10px;
  }

  .vo-rep-section {
    border: none;
    padding: 0px;
    margin-bottom: 30px;
  }
}

@media (max-width: 320px) {
  .vo-radio-group {
    flex-direction: column;
  }
  .vo-radio-label {
    width: fit-content;
  }
}

/* Document upload styles */
.vo-document-upload-section {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-24);
}

.vo-upload-success {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8);
  margin-top: var(--pp-sp-8);
  padding: var(--pp-sp-8) var(--pp-sp-12);
  background: var(--pp-clr-success-light);
  border: 1px solid var(--pp-clr-success);
  border-radius: var(--pp-bor-rad-small);
  color: var(--pp-clr-success-dark);
  font-size: var(--pp-font-small);
  font-weight: 500;
}

.vo-success-icon {
  color: var(--pp-clr-success);
  flex-shrink: 0;
}

.vo-uploading-indicator {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-12);
  margin-top: var(--pp-sp-16);
  padding: var(--pp-sp-12) var(--pp-sp-16);
  background: var(--pp-clr-info-light);
  border: 1px solid var(--pp-clr-info);
  border-radius: var(--pp-bor-rad-small);
  color: var(--pp-clr-info-dark);
  font-size: var(--pp-font-small);
  font-weight: 500;
}

.vo-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--pp-clr-info-light);
  border-top: 2px solid var(--pp-clr-info);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.vo-step-description {
  margin-bottom: var(--pp-sp-24);
  color: var(--pp-clr-text-gray);
  font-size: var(--pp-font-small);
  line-height: 1.5;
}