import React from "react";
import ContactForm from "../components/ContactForm";
import QASection from "../components/QASection";
import "../styles/ContactForm.css";
import "../styles/QASection.css";
import "../styles/Contactpage.css";
import about_hero from "../src/assets/images/about_hero.svg";

const ContactPage = () => {
  return (
    <div className="pp-max-container">
      {/* Banner */}
      <div className="pp-contact-banner">
        <img
          src={about_hero}
          alt="Happy kids celebration"
          className="pp-contact-banner-img"
          loading="lazy"
          decoding="async"
        />
        <h1 className="pp-contact-title-main">We'd love to hear from you!</h1>
      </div>

      {/* Form + Contact Info */}
      <div className="pp-max-container pp-contact-grid">
        <div>
          <ContactForm />
        </div>
        <div className="pp-contact-info contactpage-info">
          {/* Contact Details Section */}
          <div className="contactpage-details">
            <h3>Contact Details</h3>

            <div>
              <strong>Phone:</strong>
              <div>
                <a href="tel:3853422808" className="pp-contact-info-span">
                  (*************
                </a>
                <br />
                <a href="tel:8015551234" className="pp-contact-info-span">
                  (*************
                </a>
              </div>
            </div>
            <div>
              <strong>Email:</strong>
              <div>
                <a
                  href="mailto:<EMAIL>"
                  className="pp-contact-info-span"
                >
                  <EMAIL>
                </a>
                <br />
                <a
                  href="mailto:<EMAIL>"
                  className="pp-contact-info-span"
                >
                  <EMAIL>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Q&A Section */}
      <QASection />
    </div>
  );
};

export default ContactPage;
