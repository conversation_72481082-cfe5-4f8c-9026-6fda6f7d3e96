import React from "react";

export default function ReviewForm({ formData, onChange }) {
  const sections = [
    {
      title: "General Information",
      fields: [
        { label: "Title", value: formData.general?.title || "Not provided" },
        {
          label: "Description",
          value: formData.general?.description || "Not provided",
        },
        {
          label: "Customer Reviews",
          value: formData.general?.enableReviews ? "Enabled" : "Disabled",
        },
        {
          label: "Order Confirmation Email",
          value: formData.general?.orderConfirmation || "Not provided",
        },
      ],
    },
    {
      title: "Photos",
      fields: [
        { label: "Number of Photos", value: formData.photos?.length || 0 },
        {
          label: "Cover Photo",
          value: formData.photos?.find((p) => p.isCover) ? "Set" : "Not set",
        },
      ],
    },
    {
      title: "Pricing",
      fields: [
        {
          label: "Pricing Method",
          value: formData.pricing?.pricingMethod || "Not set",
        },
        {
          label: "Base Price",
          value:
            formData.pricing?.hourlyRate ||
            formData.pricing?.price ||
            "Not set",
        },
        {
          label: "Travel Fees",
          value: formData.pricing?.travelFees?.addTravelFee
            ? "Enabled"
            : "Disabled",
        },
        { label: "Add-ons", value: formData.pricing?.addOns?.length || 0 },
      ],
    },
    {
      title: "Scheduling",
      fields: [
        {
          label: "Duration Range",
          value: `${formData.scheduling?.duration?.minHours || 0}h ${
            formData.scheduling?.duration?.minMinutes || 0
          }m - ${formData.scheduling?.duration?.maxHours || 0}h ${
            formData.scheduling?.duration?.maxMinutes || 0
          }m`,
        },
        {
          label: "Available Hours",
          value: `${
            formData.scheduling?.availability?.availableFrom || "Not set"
          } - ${formData.scheduling?.availability?.availableTo || "Not set"}`,
        },
        {
          label: "Available Days",
          value: formData.scheduling?.availability?.availableDays?.length || 0,
        },
        {
          label: "Black-out Dates",
          value: formData.scheduling?.blackoutDates?.length || 0,
        },
      ],
    },
  ];

  const renderSection = (section, index) => (
    <div key={index} className="rf-section">
      <h4 className="rf-section-title">{section.title}</h4>
      <div className="rf-fields">
        {section.fields.map((field, fieldIndex) => (
          <div key={fieldIndex} className="rf-field">
            <span className="rf-field-label">{field.label}:</span>
            <span className="rf-field-value">{field.value}</span>
          </div>
        ))}
      </div>
    </div>
  );

  const hasAllRequiredData = () => {
    return (
      formData.general?.title &&
      formData.general?.description &&
      formData.photos?.length > 0
    );
  };

  const getCompletionPercentage = () => {
    let completed = 0;
    let total = 0;

    // General section
    if (formData.general?.title) completed++;
    if (formData.general?.description) completed++;
    total += 2;

    // Photos section
    if (formData.photos?.length > 0) completed++;
    total += 1;

    // Pricing section
    if (formData.pricing?.pricingMethod) completed++;
    if (formData.pricing?.hourlyRate || formData.pricing?.price) completed++;
    total += 2;

    // Scheduling section
    if (formData.scheduling?.duration) completed++;
    if (formData.scheduling?.availability) completed++;
    total += 2;

    return Math.round((completed / total) * 100);
  };

  return <div className="rf-review-form">review</div>;
}
