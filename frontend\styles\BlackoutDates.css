/* =========================
   🚫 Blackout Dates
   ========================= */

.bd-blackout-dates {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-24);
}

.bd-date-item {
  border: 1px solid var(--pp-clr-border-light);
  border-radius: var(--pp-bor-rad-12);
  padding: var(--pp-sp-24);
  background-color: var(--pp-clr-primary);
  position: relative;
}

.bd-new-date {
  border: 2px dashed var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-12);
  padding: var(--pp-sp-24);
  background-color: rgba(0, 0, 0, 0.02);
  position: relative;
}

.bd-date-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: var(--pp-sp-16);
  align-items: end;
}

.bd-date-group {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-12);
}

.bd-date-label {
  font-size: var(--pp-font-extra-small);
  font-weight: 500;
  color: var(--pp-clr-text-main);
  margin: 0;
}

.bd-date-time-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--pp-sp-8);
  align-items: center;
}

.bd-form-input {
  width: 100%;
  padding: var(--pp-sp-12) var(--pp-sp-16);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-8);
  font-size: var(--pp-font-extra-small);
  font-family: var(--pp-font-Metro-Sans);
  background: var(--pp-clr-primary);
  transition: all 0.3s ease;
  color: var(--pp-clr-text-main);
}

.bd-form-input:focus {
  outline: none;
  border-color: var(--pp-clr-secondary);
}

.bd-form-input::placeholder {
  color: var(--pp-clr-text-gray);
}

.bd-form-select,
.custom-select-wrapper.bd-form-select {
  width: 100%;
  padding: var(--pp-sp-12) var(--pp-sp-16);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-8);
  font-size: var(--pp-font-extra-small);
  font-family: var(--pp-font-Metro-Sans);
  background: var(--pp-clr-primary);
  transition: all 0.3s ease;
  color: var(--pp-clr-text-main);
  cursor: pointer;
}

.bd-form-select:focus,
.custom-select-wrapper.bd-form-select:focus {
  outline: none;
  border-color: var(--pp-clr-secondary);
}

/* Repeat Group */
.bd-repeat-group {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-8);
}

/* Checkboxes */
.bd-checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8);
  cursor: pointer;
  user-select: none;
}

.bd-checkbox {
  position: relative;
  width: var(--pp-sp-20);
  height: var(--pp-sp-20);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-4);
  background: var(--pp-clr-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.bd-checkbox:hover {
  border-color: var(--pp-clr-text-gray);
  transform: scale(1.05);
}

.bd-checkbox:focus {
  outline: none;
  border-color: var(--pp-clr-secondary);
}

.bd-checkbox:checked {
  background-color: var(--pp-clr-secondary) !important;
  border-color: var(--pp-clr-secondary) !important;
}

.bd-checkbox:checked::after {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: var(--pp-sp-12);
  height: var(--pp-sp-12);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.bd-checkbox-label {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-main);
  font-weight: 500;
  cursor: pointer;
}

/* Buttons */
.bd-btn-remove {
  background: transparent;
  border: none;
  color: var(--pp-clr-text-gray);
  cursor: pointer;
  padding: var(--pp-sp-8);
  border-radius: var(--pp-bor-rad-8);
  transition: all 0.3s ease;
  font-size: var(--pp-font-extra-small);
  position: absolute;
  top: var(--pp-sp-16);
  right: var(--pp-sp-16);
}

.bd-btn-remove:hover {
  background-color: var(--pp-clr-border-light);
  color: var(--pp-clr-secondary);
}

.bd-btn-add {
  background-color: var(--pp-clr-secondary);
  color: var(--pp-clr-primary);
  border: none;
  border-radius: var(--pp-bor-rad-24);
  padding: var(--pp-sp-12) var(--pp-sp-24);
  font-size: var(--pp-font-extra-small);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: var(--pp-sp-16);
  align-self: flex-start;
}

.bd-btn-add:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--pp-shdw-sm);
}

.bd-btn-add:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Responsive */
@media (max-width: 768px) {
  .bd-blackout-dates {
    gap: var(--pp-sp-20);
  }

  .bd-date-inputs {
    grid-template-columns: 1fr;
    gap: var(--pp-sp-12);
  }

  .bd-date-item,
  .bd-new-date {
    padding: var(--pp-sp-20);
  }

  .bd-btn-remove {
    position: static;
    align-self: flex-end;
    margin-top: var(--pp-sp-16);
  }
}

@media (max-width: 480px) {
  .bd-blackout-dates {
    gap: var(--pp-sp-16);
  }

  .bd-date-item,
  .bd-new-date {
    padding: var(--pp-sp-16);
  }

  .bd-date-inputs {
    gap: var(--pp-sp-10);
  }

  .bd-date-group {
    gap: var(--pp-sp-8);
  }

  .bd-form-input,
  .bd-form-select,
  .custom-select-wrapper.bd-form-select {
    padding: var(--pp-sp-10) var(--pp-sp-12);
  }
}
