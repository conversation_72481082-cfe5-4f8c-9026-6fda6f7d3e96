.BookingDetailsModal-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0,0,0,0.32);
    z-index: var(--pp-z-index-modal);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .BookingDetailsModal-container {
    background: var(--pp-clr-primary);
    border-radius: var(--pp-bor-rad-16);
    box-shadow: var(--pp-shdw-lg);
    width: 100%;
    max-width: 620px;
    display: flex;
    flex-direction: column;
    font-family: var(--pp-font-Metro-Sans);
    color: var(--pp-clr-text-main);
    max-height: 90vh;
    overflow-y: auto;
    
  }
  .BookingDetailsModal-close-div{
    display: flex;
    justify-content: flex-end;
    padding: var(--pp-sp-8);
    height: var(--pp-sp-40);
  }
  
  .BookingDetailsModal-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: var(--pp-sp-16);
    gap: var(--pp-sp-16);
  }
  
  .BookingDetailsModal-title {
    font-size: var(--pp-font-heading4);
    font-weight: 400;
  }
  
  .BookingDetailsModal-tag {
    font-size: var(--pp-font-extra-small);
    color: var(--pp-clr-text-gray);
    margin-top: var(--pp-sp-8);
  }
  
  

  
  .BookingDetailsModal-close {
    background: none;
    color: var(--pp-clr-text-main);
    border: none;
    font-size: var(--pp-font-heading4);
    cursor: pointer;
    align-self: flex-start;
    padding: var(--pp-sp-4);
    border-radius: var(--pp-bor-rad-round);
    transition: background 0.2s;
  }
  
  .BookingDetailsModal-close:hover {
    background: var(--pp-clr-border-light);
  }
  
  .BookingDetailsModal-divider {
    width: 100%;
    height: 1px;
    background: var(--pp-clr-border);
  }
  
  .BookingDetailsModal-main {
    display: flex;
    gap: var(--pp-sp-32);
    padding: var(--pp-sp-24);
    flex: 1;
  }
  
  .BookingDetailsModal-details {
    display: flex;
    flex-direction: column;
    width: 50%;
  }
  
  .BookingDetailsModal-sectionTitle {
    font-weight: 600;
    margin-bottom: var(--pp-sp-12);
    font-size: var(--pp-font-heading5);
  }
  
  .BookingDetailsModal-keyVals > div {
    margin-bottom: var(--pp-sp-12);
  }
  .BookingDetailsModal-keyVal-row{
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--pp-sp-12);
  }
  .BookingDetailsModal-keyVal-row div{
    font-size: var(--pp-font-extra-small);
  }
  .BookingDetailsModal-label {
    display: block;
    font-size: var(--pp-font-extra-small);
   
    font-weight: 600;
    margin-bottom: var(--pp-sp-4);
  }
  
  .BookingDetailsModal-time {
    color: var(--pp-clr-text-gray);
    font-size: var(--pp-font-extra-small);
    margin-bottom: var(--pp-sp-4);
  }
  
  .BookingDetailsModal-images {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--pp-sp-12);
    flex: 1;
    min-width: 110px;
  }
  
  .BookingDetailsModal-viewOrder {
  
    font-size: var(--pp-font-extra-small);
    align-self: flex-end;
    text-decoration: underline;
    margin-bottom: var(--pp-sp-12);
  }
  
  .BookingDetailsModal-imageList {
    display: grid;
    grid-template-columns: 1fr 1fr ;
    direction: rtl;
    gap: var(--pp-sp-8);
  }
  
  .BookingDetailsModal-img {
    width: 120px;
    height: 96px;
    object-fit: cover;
    border-radius: var(--pp-bor-rad-8);
    border: 1px solid var(--pp-clr-border);
    background: var(--pp-clr-border-light);
  }
  
  .BookingDetailsModal-footer {
    padding: 0 var(--pp-sp-24) var(--pp-sp-24);
    margin-top: auto;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    border-top: 1px solid var(--pp-clr-border);
    padding-top: var(--pp-sp-24);
  }
  
  /* Vendor Info Section */
  .BookingDetailsModal-kvrow {
    display: grid;
    grid-template-columns: 50px 1fr;
    gap: var(--pp-sp-16);
    font-size: var(--pp-font-extra-small);
    margin-bottom: var(--pp-sp-8);
  }
  .BookingDetailsModal-kvrow span{
    width: 100%;
  }
  .BookingDetailsModal-kvrow span:first-child{
    font-weight: 600;
  }

  /* Responsive */
  @media (max-width: 600px) {
    .BookingDetailsModal-container {
      max-width: 95vw;
      min-width: 0;
      
      border-radius: var(--pp-bor-rad-8);
    }
    .BookingDetailsModal-header{
      padding: var(--pp-sp-16) 0;
    }
    .BookingDetailsModal-footer, .BookingDetailsModal-details, .BookingDetailsModal-images {
      padding: var(--pp-sp-16) 0;
    }
    .BookingDetailsModal-main{
      padding:  var(--pp-sp-16) 0 ;
    }
    .BookingDetailsModal-keyVal-row{
        grid-template-columns: 1fr;
    }
  }
  @media (max-width: 480px) {
    .BookingDetailsModal-main {
        flex-direction: column-reverse;
        gap: var(--pp-sp-16);
        padding: var(--pp-sp-12);
      }
      .BookingDetailsModal-details{
        width: 100%;
      }
      
      .BookingDetailsModal-images{
        display: grid
       
        
      }
      .BookingDetailsModal-keyVal-row{
        gap: var(--pp-sp-2);
      }
      .BookingDetailsModal-label{
        margin-bottom: 0;
      }
 
    
  }