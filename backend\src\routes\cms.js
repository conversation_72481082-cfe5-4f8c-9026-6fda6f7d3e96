const express = require('express');
const router = express.Router();
const CMSController = require('../controllers/cmsController');
const { auth, optionalAuth, authorize } = require('../middlewares/auth');
const { validateRequest } = require('../middlewares/validation');
const { uploadSingle } = require('../middlewares/upload');
const { rateLimiting } = require('../middlewares/rateLimiting');
const Joi = require('joi');

// Validation schemas
const createPageSchema = Joi.object({
  title: Joi.string().min(5).max(200).required(),
  slug: Joi.string().min(3).max(100),
  content: Joi.string().min(50).required(),
  excerpt: Joi.string().max(500),
  type: Joi.string().valid('page', 'privacy', 'terms', 'about', 'faq', 'help').default('page'),
  status: Joi.string().valid('draft', 'published', 'archived').default('draft'),
  featured: Joi.boolean().default(false),
  featuredImage: Joi.string().uri(),
  seoTitle: Joi.string().max(60),
  seoDescription: Joi.string().max(160),
  seoKeywords: Joi.array().items(Joi.string().max(50)).max(10)
});

const updatePageSchema = Joi.object({
  title: Joi.string().min(5).max(200),
  slug: Joi.string().min(3).max(100),
  content: Joi.string().min(50),
  excerpt: Joi.string().max(500),
  type: Joi.string().valid('page', 'privacy', 'terms', 'about', 'faq', 'help'),
  status: Joi.string().valid('draft', 'published', 'archived'),
  featured: Joi.boolean(),
  featuredImage: Joi.string().uri(),
  seoTitle: Joi.string().max(60),
  seoDescription: Joi.string().max(160),
  seoKeywords: Joi.array().items(Joi.string().max(50)).max(10)
});

const createBlogSchema = Joi.object({
  title: Joi.string().min(5).max(200).required(),
  slug: Joi.string().min(3).max(100),
  content: Joi.string().min(100).required(),
  excerpt: Joi.string().max(500),
  category: Joi.string().max(50).required(),
  tags: Joi.array().items(Joi.string().max(30)).max(20),
  status: Joi.string().valid('draft', 'published', 'archived').default('draft'),
  featured: Joi.boolean().default(false),
  featuredImage: Joi.string().uri(),
  publishedAt: Joi.date(),
  seoTitle: Joi.string().max(60),
  seoDescription: Joi.string().max(160),
  seoKeywords: Joi.array().items(Joi.string().max(50)).max(10)
});

const updateBlogSchema = Joi.object({
  title: Joi.string().min(5).max(200),
  slug: Joi.string().min(3).max(100),
  content: Joi.string().min(100),
  excerpt: Joi.string().max(500),
  category: Joi.string().max(50),
  tags: Joi.array().items(Joi.string().max(30)).max(20),
  status: Joi.string().valid('draft', 'published', 'archived'),
  featured: Joi.boolean(),
  featuredImage: Joi.string().uri(),
  publishedAt: Joi.date(),
  seoTitle: Joi.string().max(60),
  seoDescription: Joi.string().max(160),
  seoKeywords: Joi.array().items(Joi.string().max(50)).max(10)
});

const bulkUpdateSchema = Joi.object({
  contentType: Joi.string().valid('page', 'blog').required(),
  ids: Joi.array().items(Joi.string().hex().length(24)).min(1).max(50).required(),
  status: Joi.string().valid('draft', 'published', 'archived').required()
});

const searchSchema = Joi.object({
  q: Joi.string().min(2).max(100).required(),
  type: Joi.string().valid('all', 'pages', 'blog').default('all'),
  page: Joi.number().min(1).default(1),
  limit: Joi.number().min(1).max(50).default(10)
});

// Public routes (no authentication required)

// Pages
router.get('/pages', optionalAuth, CMSController.getPages);
router.get('/pages/:slug', optionalAuth, CMSController.getPageBySlug);

// Blog
router.get('/blog', optionalAuth, CMSController.getBlogPosts);
router.get('/blog/:slug', optionalAuth, CMSController.getBlogPostBySlug);

// Featured content
router.get('/featured', CMSController.getFeaturedContent);

// Search
router.get('/search', validateRequest(searchSchema, 'query'), CMSController.searchContent);

// Static content endpoints for common pages
router.get('/static/privacy-policy', async (req, res) => {
  req.params.slug = 'privacy-policy';
  return CMSController.getPageBySlug(req, res);
});

router.get('/static/terms-of-service', async (req, res) => {
  req.params.slug = 'terms-of-service';
  return CMSController.getPageBySlug(req, res);
});

router.get('/static/about-us', async (req, res) => {
  req.params.slug = 'about-us';
  return CMSController.getPageBySlug(req, res);
});

router.get('/static/faq', async (req, res) => {
  req.params.slug = 'faq';
  return CMSController.getPageBySlug(req, res);
});

// Contact form submission (public)
router.post('/contact', rateLimiting.moderate, async (req, res) => {
  const contactSchema = Joi.object({
    name: Joi.string().min(2).max(100).required(),
    email: Joi.string().email().required(),
    subject: Joi.string().min(5).max(200).required(),
    message: Joi.string().min(20).max(2000).required(),
    phone: Joi.string().pattern(/^[\+]?[1-9][\d]{0,15}$/),
    company: Joi.string().max(100)
  });

  const { error } = contactSchema.validate(req.body);
  if (error) {
    const { sendBadRequest } = require('../utils/responseHelper');
    return sendBadRequest(res, error.details[0].message);
  }

  const { name, email, subject, message, phone, company } = req.body;

  // In a real implementation, this would save to database and send email
  const EmailService = require('../services/emailService');

  try {
    await EmailService.sendContactFormSubmission({
      name,
      email,
      subject,
      message,
      phone,
      company,
      submittedAt: require('../utils/dateHelper').toUTCDate(),
      ip: req.ip
    });

    const { apiLogger } = require('../utils/logger');
    apiLogger.info('Contact form submission', {
      name,
      email,
      subject,
      ip: req.ip
    });

    const { sendSuccess } = require('../utils/responseHelper');
    return sendSuccess(res, 'Thank you for your message. We will get back to you soon!', {
      submittedAt: require('../utils/dateHelper').toUTCDate()
    });

  } catch (error) {
    const { apiLogger } = require('../utils/logger');
    apiLogger.error('Contact form submission failed', {
      error: error.message,
      name,
      email
    });

    const { sendBadRequest } = require('../utils/responseHelper');
    return sendBadRequest(res, 'Failed to send message. Please try again later.');
  }
});

// Newsletter subscription (public)
router.post('/newsletter/subscribe', rateLimiting.moderate, async (req, res) => {
  const subscribeSchema = Joi.object({
    email: Joi.string().email().required(),
    firstName: Joi.string().min(2).max(50),
    interests: Joi.array().items(Joi.string().valid('events', 'vendors', 'tips', 'promotions')).max(4)
  });

  const { error } = subscribeSchema.validate(req.body);
  if (error) {
    const { sendBadRequest } = require('../utils/responseHelper');
    return sendBadRequest(res, error.details[0].message);
  }

  const { email, firstName, interests = [] } = req.body;

  // In a real implementation, this would integrate with email marketing service
  const { apiLogger } = require('../utils/logger');
  apiLogger.info('Newsletter subscription', {
    email,
    firstName,
    interests,
    ip: req.ip
  });

  const { sendSuccess } = require('../utils/responseHelper');
  return sendSuccess(res, 'Successfully subscribed to newsletter!', {
    email,
    subscribedAt: new Date(),
    interests
  });
});

// Protected routes (admin authentication required)

// Page management (Admin only)
router.post('/pages',
  auth,
  authorize(['admin']),
  rateLimiting.moderate,
  validateRequest(createPageSchema),
  CMSController.createPage
);

router.put('/pages/:pageId',
  auth,
  authorize(['admin']),
  rateLimiting.moderate,
  validateRequest(updatePageSchema),
  CMSController.updatePage
);

router.delete('/pages/:pageId',
  auth,
  authorize(['admin']),
  rateLimiting.moderate,
  CMSController.deletePage
);

// Blog management (Admin only)
router.post('/blog',
  auth,
  authorize(['admin']),
  rateLimiting.moderate,
  validateRequest(createBlogSchema),
  CMSController.createBlogPost
);

router.put('/blog/:blogId',
  auth,
  authorize(['admin']),
  rateLimiting.moderate,
  validateRequest(updateBlogSchema),
  CMSController.updateBlogPost
);

router.delete('/blog/:blogId',
  auth,
  authorize(['admin']),
  rateLimiting.moderate,
  CMSController.deleteBlogPost
);

// Content statistics (Admin only)
router.get('/stats',
  auth,
  authorize(['admin']),
  CMSController.getContentStats
);

// Bulk operations (Admin only)
router.post('/bulk/update-status',
  auth,
  authorize(['admin']),
  rateLimiting.strict,
  validateRequest(bulkUpdateSchema),
  CMSController.bulkUpdateStatus
);

// Media uploads for CMS content
router.post('/upload/featured-image',
  auth,
  authorize(['admin']),
  rateLimiting.moderate,
  uploadSingle('image', {
    folder: 'cms-featured',
    allowedFormats: ['jpg', 'jpeg', 'png', 'webp'],
    maxSize: 10 * 1024 * 1024 // 10MB
  }),
  async (req, res) => {
    if (!req.file) {
      const { sendBadRequest } = require('../utils/responseHelper');
      return sendBadRequest(res, 'No image file provided');
    }

    const { getFileUrl } = require('../config/storage');
    const imageUrl = getFileUrl(req.file);

    const { apiLogger } = require('../utils/logger');
    apiLogger.info('CMS featured image uploaded', {
      userId: req.user.id,
      imageUrl,
      originalName: req.file.originalname,
      size: req.file.size
    });

    const { sendSuccess } = require('../utils/responseHelper');
    return sendSuccess(res, 'Featured image uploaded successfully', {
      imageUrl,
      fileName: req.file.originalname,
      size: req.file.size
    });
  }
);

router.post('/upload/content-image',
  auth,
  authorize(['admin']),
  rateLimiting.moderate,
  uploadSingle('image', {
    folder: 'cms-content',
    allowedFormats: ['jpg', 'jpeg', 'png', 'webp', 'gif'],
    maxSize: 5 * 1024 * 1024 // 5MB
  }),
  async (req, res) => {
    if (!req.file) {
      const { sendBadRequest } = require('../utils/responseHelper');
      return sendBadRequest(res, 'No image file provided');
    }

    const { getFileUrl } = require('../config/storage');
    const imageUrl = getFileUrl(req.file);

    const { sendSuccess } = require('../utils/responseHelper');
    return sendSuccess(res, 'Content image uploaded successfully', {
      imageUrl,
      fileName: req.file.originalname,
      size: req.file.size,
      alt: req.body.alt || req.file.originalname
    });
  }
);

// Content templates and snippets (Admin only)
router.get('/templates', auth, authorize(['admin']), async (req, res) => {
  const templates = {
    pages: [
      {
        id: 'privacy_policy',
        name: 'Privacy Policy',
        description: 'Standard privacy policy template',
        content: 'Your privacy is important to us. This privacy policy explains how we collect, use, and protect your information...'
      },
      {
        id: 'terms_of_service',
        name: 'Terms of Service',
        description: 'Standard terms of service template',
        content: 'By using our service, you agree to the following terms and conditions...'
      },
      {
        id: 'about_us',
        name: 'About Us',
        description: 'Company information template',
        content: 'Welcome to Party Pipeline. We are dedicated to connecting customers with the best event service providers...'
      }
    ],
    blog: [
      {
        id: 'event_planning_tips',
        name: 'Event Planning Tips',
        description: 'Template for event planning blog posts',
        content: 'Planning the perfect event requires careful attention to detail. Here are our top tips...'
      },
      {
        id: 'vendor_spotlight',
        name: 'Vendor Spotlight',
        description: 'Template for featuring vendors',
        content: 'This month, we are excited to spotlight one of our amazing vendors...'
      }
    ]
  };

  const { sendSuccess } = require('../utils/responseHelper');
  return sendSuccess(res, 'Content templates retrieved successfully', templates);
});

// Content scheduling (Admin only)
router.post('/schedule', auth, authorize(['admin']), rateLimiting.moderate, async (req, res) => {
  const scheduleSchema = Joi.object({
    contentType: Joi.string().valid('page', 'blog').required(),
    contentId: Joi.string().hex().length(24).required(),
    action: Joi.string().valid('publish', 'archive', 'unpublish').required(),
    scheduledAt: Joi.date().min('now').required()
  });

  const { error } = scheduleSchema.validate(req.body);
  if (error) {
    const { sendBadRequest } = require('../utils/responseHelper');
    return sendBadRequest(res, error.details[0].message);
  }

  const { contentType, contentId, action, scheduledAt } = req.body;

  // In a real implementation, this would create a scheduled job
  const { apiLogger } = require('../utils/logger');
  apiLogger.info('Content scheduled', {
    contentType,
    contentId,
    action,
    scheduledAt,
    scheduledBy: req.user.id
  });

  const { sendSuccess } = require('../utils/responseHelper');
  return sendSuccess(res, `Content ${action} scheduled successfully`, {
    contentType,
    contentId,
    action,
    scheduledAt,
    scheduledBy: req.user.id
  });
});

// SEO analysis (Admin only)
router.post('/seo/analyze', auth, authorize(['admin']), rateLimiting.moderate, async (req, res) => {
  const seoSchema = Joi.object({
    title: Joi.string().required(),
    content: Joi.string().required(),
    slug: Joi.string().required(),
    seoTitle: Joi.string(),
    seoDescription: Joi.string(),
    seoKeywords: Joi.array().items(Joi.string())
  });

  const { error } = seoSchema.validate(req.body);
  if (error) {
    const { sendBadRequest } = require('../utils/responseHelper');
    return sendBadRequest(res, error.details[0].message);
  }

  const { title, content, slug, seoTitle, seoDescription, seoKeywords = [] } = req.body;

  // Simple SEO analysis
  const analysis = {
    title: {
      length: title.length,
      score: title.length >= 30 && title.length <= 60 ? 'good' : 'warning',
      recommendations: []
    },
    description: {
      length: seoDescription?.length || 0,
      score: seoDescription && seoDescription.length >= 120 && seoDescription.length <= 160 ? 'good' : 'warning',
      recommendations: []
    },
    content: {
      wordCount: content.split(' ').length,
      score: content.split(' ').length >= 300 ? 'good' : 'warning',
      recommendations: []
    },
    keywords: {
      count: seoKeywords.length,
      score: seoKeywords.length >= 3 && seoKeywords.length <= 7 ? 'good' : 'warning',
      recommendations: []
    },
    url: {
      slug,
      score: slug.length <= 60 && !/[^a-z0-9-]/.test(slug) ? 'good' : 'warning',
      recommendations: []
    }
  };

  // Add recommendations based on analysis
  if (analysis.title.score === 'warning') {
    analysis.title.recommendations.push('Title should be between 30-60 characters for optimal SEO');
  }

  if (analysis.description.score === 'warning') {
    analysis.description.recommendations.push('Meta description should be between 120-160 characters');
  }

  if (analysis.content.score === 'warning') {
    analysis.content.recommendations.push('Content should be at least 300 words for better SEO ranking');
  }

  const overallScore = Object.values(analysis).filter(item => item.score === 'good').length / Object.keys(analysis).length;

  const { sendSuccess } = require('../utils/responseHelper');
  return sendSuccess(res, 'SEO analysis completed', {
    analysis,
    overallScore: Math.round(overallScore * 100),
    recommendations: Object.values(analysis).flatMap(item => item.recommendations)
  });
});

module.exports = router;
