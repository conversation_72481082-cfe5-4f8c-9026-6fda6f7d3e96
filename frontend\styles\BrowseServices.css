.pp-browse-services {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--pp-sp-60) var(--pp-sp-24);
  background: var(--pp-clr-bg); /* page background */
}

.pp-browse-services__header {
  margin-bottom: var(--pp-sp-24);
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-24);
}

.pp-browse-services__filters {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: var(--pp-sp-20);
  justify-content: center;
  grid-template-columns: 1fr 1fr;
}

.pp-browse-services__filters-row {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-20);
  width: 100%;
  max-width: fit-content;
}

.pp-browse-services__label {
  font-size: var(--pp-font-extra-small);
  font-weight: 700;
  color: var(--pp-clr-text-main);
  margin-right: var(--pp-sp-16);
}

.pp-browse-services__filter-btn {
  background: transparent;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: var(--pp-clr-secondary);
  cursor: pointer;
  font-weight: 700;
  font-size: var(--pp-font-heading6);

}

.pp-browse-services__input {
  background: var(--pp-clr-bg-light, #f4f4f4);
  border-radius: var(--pp-bor-rad-24);
  font-size: var(--pp-font-extra-small);
  padding: var(--pp-sp-16) var(--pp-sp-20);
  min-width: 200px;
  font-family: var(--pp-font-Metro-Sans);

  outline: none;
  width: 100%;
}
.pp-browse-services__input::placeholder {
  font-weight: 600;
  font-size: var(--pp-font-base2-font);
  color: var(--pp-clr-secondary);
}
.pp-browse-services__search {
  position: relative;
  min-width: 40px;
  width: 392px;
}

.pp-browse-services__search-icon {
  position: absolute;
  right: var(--pp-sp-16);
  top: 50%;
  transform: translateY(-50%);
  font-size: var(--pp-font-base2-font);
  color: var(--pp-clr-secondary);
}

.pp-browse-services__categories {
  display: flex;
  gap: var(--pp-sp-20);
}

.pp-browse-services__control {
  display: flex;
  width: fit-content;
  gap: var(--pp-sp-8);
  align-items: center;
}


.pp-browse-services__control .custom-select-box{
  width: 170px;
}
.pp-browse-services__date {
  padding: 9px var(--pp-sp-16);
}

.pp-browse-services__chip {
  background: var(--pp-clr-bg-light, #f4f4f4);
  border-radius: 32px;
  border: none;
  font-size: var(--pp-font-extra-small);
  font-weight: 600;
  color: var(--pp-clr-text-main);
  padding:  var(--pp-sp-16) var(--pp-sp-20);
  cursor: pointer;
  transition: background 200ms, color 200ms;
  outline: none;
}

.pp-browse-services__chip--active {
  background: var(--pp-clr-secondary);
  color: var(--pp-clr-primary);
}
.pp-browse-services__categories-dropdown-wrape {
  display: none;
}
.pp-browse-services__categories-dropdown {
  font-size: var(--pp-font-base2-font);
  font-family: var(--pp-font-Metro-Sans);

  padding: 0;
}
.pp-browse-services__categories-dropdown:focus {
  outline: none;
  box-shadow: none; /* if any shadow appears */
}
.pp-browse-services__grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: var(--pp-sp-24);
}

/* Filter Drawer (<= 992px) */
.pp-filter-drawer {
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  width: 0;
  overflow: hidden;
  background: var(--pp-clr-primary);
  box-shadow: var(--pp-shdw-lg);
  z-index: var(--pp-z-index-modal);
  transition: width 250ms ease;
}

.pp-filter-drawer--open {
  width: min(88vw, 420px);
}

.pp-filter-drawer__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--pp-sp-16) var(--pp-sp-16);
  border-bottom: 1px solid var(--pp-clr-border);
}

.pp-filter-drawer__title {
  font-size: var(--pp-font-small-font);
  color: var(--pp-clr-text-main);
  font-weight: 600;
}

.pp-filter-drawer__close {
  background: transparent;
  border: none;
  color: var(--pp-clr-text-main);
  cursor: pointer;
}

.pp-filter-drawer__body {
  padding: var(--pp-sp-16);
  display: grid;
  gap: var(--pp-sp-16);
}

@media (max-width: 768px) {
  .pp-browse-services {
    padding: var(--pp-sp-32) var(--pp-sp-16);
  }
  .pp-browse-services__filters-row {
    gap: var(--pp-sp-10);
  }
  .pp-browse-services__categories {
    flex-wrap: wrap;
    margin-top: var(--pp-sp-12);
  }
}
@media (max-width: 600px) {
  .pp-browse-services__filters {
    display: flex;
    flex-direction: column;
    align-items: start;
    gap: var(--pp-sp-20);
    justify-content: space-between;
  }
  .pp-browse-services__categories {
    display: none;
  }
  .pp-browse-services__categories-dropdown-wrape {
    display: block;
    width: fit-content;
  }
  .pp-browse-services__categories-dropdown {
    display: block;
    width: 100%;
  }
  .pp-browse-services__filters-row {
    display: flex;
    align-items: center;
    gap: var(--pp-sp-12);
    width: 100%;
    max-width: 100%;
  }
  .pp-browse-services__search{
    max-width: 100%;
  }
}
