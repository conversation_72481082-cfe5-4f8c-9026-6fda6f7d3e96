const express = require('express');
const router = express.Router();
const BookingController = require('../controllers/bookingController');
const { auth, authorize } = require('../middlewares/auth');
const { validateRequest } = require('../middlewares/validation');
const { rateLimiting } = require('../middlewares/rateLimiting');
const Joi = require('joi');

// Validation schemas
const createBookingSchema = Joi.object({
  serviceId: Joi.string().hex().length(24).required(),
  eventDetails: Joi.object({
    date: Joi.date().min('now').required(),
    startTime: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).required(),
    duration: Joi.number().min(30).max(1440).required(), // 30 minutes to 24 hours
    attendeeCount: Joi.number().min(1).max(1000).required(),
    eventType: Joi.string().max(100),
    specialRequests: Joi.string().max(1000)
  }).required(),
  selectedOptions: Joi.object({
    package: Joi.string(),
    customizations: Joi.array().items(
      Joi.object({
        question: Joi.string().required(),
        answer: Joi.string().required()
      })
    )
  }).required(),
  addOns: Joi.array().items(Joi.object({
    title: Joi.string().required(),
    price: Joi.number().min(0).required(),
    quantity: Joi.number().min(1).default(1)
  })),
  notes: Joi.string().max(1000),
  contactInfo: Joi.object({
    primaryContact: Joi.object({
      name: Joi.string().required(),
      phone: Joi.string().required(),
      email: Joi.string().email().required()
    }).required(),
    emergencyContact: Joi.object({
      name: Joi.string(),
      phone: Joi.string(),
      relationship: Joi.string()
    })
  }).required(),
  location: Joi.object({
    address: Joi.string().required(),
    city: Joi.string().required(),
    state: Joi.string().required(),
    zipCode: Joi.string().required(),
    country: Joi.string().default('US'),
    lat: Joi.number().min(-90).max(90),
    lng: Joi.number().min(-180).max(180),
    venueNotes: Joi.string().max(500),
    parkingInfo: Joi.string().max(500),
    accessInstructions: Joi.string().max(500)
  }).required()
});

const updateBookingSchema = Joi.object({
  eventDetails: Joi.object({
    date: Joi.date().min('now'),
    startTime: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
    duration: Joi.number().min(30).max(1440),
    attendeeCount: Joi.number().min(1).max(1000),
    eventType: Joi.string().max(100),
    specialRequests: Joi.string().max(1000)
  }),
  notes: Joi.string().max(1000),
  vendorNotes: Joi.string().max(1000),
  contactInfo: Joi.object({
    primaryContact: Joi.object({
      name: Joi.string(),
      phone: Joi.string(),
      email: Joi.string().email()
    }),
    emergencyContact: Joi.object({
      name: Joi.string(),
      phone: Joi.string(),
      relationship: Joi.string()
    })
  }),
  location: Joi.object({
    type: Joi.string().valid('venue', 'home', 'outdoor', 'other'),
    address: Joi.object({
      street: Joi.string(),
      city: Joi.string(),
      state: Joi.string(),
      zipCode: Joi.string(),
      country: Joi.string()
    }),
    lat: Joi.number().min(-90).max(90),
    lng: Joi.number().min(-180).max(180),
    accessInstructions: Joi.string().max(500),
    parkingInfo: Joi.string().max(500)
  }),
  estimatedDuration: Joi.number().min(30)
});

const cancelBookingSchema = Joi.object({
  reason: Joi.string().max(500).required(),
  refundRequested: Joi.boolean().default(false)
});

const completeBookingSchema = Joi.object({
  rating: Joi.number().min(1).max(5),
  completionNotes: Joi.string().max(1000)
});

const paymentSchema = Joi.object({
  paymentMethodId: Joi.string().required(),
  savePaymentMethod: Joi.boolean().default(false)
});

// Protected routes (authentication required)
router.use(auth);

// Booking management routes
router.post('/', rateLimiting.moderate, validateRequest(createBookingSchema), BookingController.createBooking);
router.get('/', BookingController.getUserBookings);
router.get('/stats', BookingController.getBookingStats);

// Individual booking operations
router.get('/:bookingId', BookingController.getBookingById);
router.put('/:bookingId', rateLimiting.moderate, validateRequest(updateBookingSchema), BookingController.updateBooking);
router.delete('/:bookingId', rateLimiting.strict, validateRequest(cancelBookingSchema), BookingController.cancelBooking);

// Payment operations
router.post('/:bookingId/payment', rateLimiting.strict, validateRequest(paymentSchema), BookingController.processPayment);

// Booking completion (vendor only)
router.post('/:bookingId/complete',
  authorize(['vendor', 'admin']),
  rateLimiting.moderate,
  validateRequest(completeBookingSchema),
  BookingController.completeBooking
);

// Booking status tracking
router.get('/:bookingId/timeline', async (req, res) => {
  const { bookingId } = req.params;
  const userId = req.user.id;

  const Booking = require('../models/Booking');
  const booking = await Booking.findById(bookingId);

  if (!booking) {
    const { sendNotFound } = require('../utils/responseHelper');
    return sendNotFound(res, 'Booking not found');
  }

  // Check authorization
  const isAuthorized = booking.customerId.toString() === userId ||
    booking.vendorId.toString() === userId ||
    req.user.role === 'admin';

  if (!isAuthorized) {
    const { sendForbidden } = require('../utils/responseHelper');
    return sendForbidden(res, 'You are not authorized to view this booking timeline');
  }

  // Generate timeline based on booking status and history
  const timeline = [];

  timeline.push({
    event: 'booking_created',
    title: 'Booking Request Submitted',
    description: 'Your booking request has been submitted to the vendor',
    timestamp: booking.createdAt,
    status: 'completed'
  });

  if (booking.status !== 'pending') {
    timeline.push({
      event: 'booking_reviewed',
      title: booking.status === 'approved' ? 'Booking Approved' : 'Booking Rejected',
      description: booking.status === 'approved'
        ? 'The vendor has approved your booking request'
        : 'The vendor has declined your booking request',
      timestamp: booking.approvedAt || booking.rejectedAt,
      status: 'completed'
    });
  }

  if (booking.status === 'confirmed') {
    timeline.push({
      event: 'payment_completed',
      title: 'Payment Confirmed',
      description: 'Payment has been processed successfully',
      timestamp: booking.confirmedAt,
      status: 'completed'
    });

    timeline.push({
      event: 'event_scheduled',
      title: 'Event Scheduled',
      description: 'Your event is confirmed and scheduled',
      timestamp: booking.eventDetails.date,
      status: new Date() > booking.eventDetails.date ? 'completed' : 'upcoming'
    });
  }

  if (booking.status === 'completed') {
    timeline.push({
      event: 'booking_completed',
      title: 'Event Completed',
      description: 'The event has been successfully completed',
      timestamp: booking.metadata?.completedAt,
      status: 'completed'
    });
  }

  const { sendSuccess } = require('../utils/responseHelper');
  return sendSuccess(res, 'Booking timeline retrieved successfully', {
    timeline,
    currentStatus: booking.status
  });
});

// Booking documents and attachments
router.post('/:bookingId/attachments',
  rateLimiting.moderate,
  require('../middlewares/upload').uploadMultiple('files', {
    folder: 'booking-attachments',
    allowedFormats: ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif'],
    maxSize: 10 * 1024 * 1024, // 10MB per file
    maxFiles: 5
  }),
  async (req, res) => {
    const { bookingId } = req.params;
    const userId = req.user.id;

    const Booking = require('../models/Booking');
    const booking = await Booking.findById(bookingId);

    if (!booking) {
      const { sendNotFound } = require('../utils/responseHelper');
      return sendNotFound(res, 'Booking not found');
    }

    // Check authorization
    const isAuthorized = booking.customerId.toString() === userId ||
      booking.vendorId.toString() === userId;

    if (!isAuthorized) {
      const { sendForbidden } = require('../utils/responseHelper');
      return sendForbidden(res, 'You are not authorized to add attachments to this booking');
    }

    if (!req.files || req.files.length === 0) {
      const { sendBadRequest } = require('../utils/responseHelper');
      return sendBadRequest(res, 'No files provided');
    }

    const { getFileUrl } = require('../config/storage');
    const attachments = req.files.map(file => ({
      fileName: file.originalname,
      fileUrl: getFileUrl(file),
      fileType: file.mimetype,
      fileSize: file.size,
      uploadedBy: userId,
      uploadedAt: require('../utils/dateHelper').toUTCDate()
    }));

    // Add attachments to booking
    booking.attachments = booking.attachments || [];
    booking.attachments.push(...attachments);
    await booking.save();

    const { sendSuccess } = require('../utils/responseHelper');
    return sendSuccess(res, 'Attachments uploaded successfully', {
      attachments,
      totalAttachments: booking.attachments.length
    });
  }
);

// Get booking attachments
router.get('/:bookingId/attachments', async (req, res) => {
  const { bookingId } = req.params;
  const userId = req.user.id;

  const Booking = require('../models/Booking');
  const booking = await Booking.findById(bookingId).select('attachments customerId vendorId');

  if (!booking) {
    const { sendNotFound } = require('../utils/responseHelper');
    return sendNotFound(res, 'Booking not found');
  }

  // Check authorization
  const isAuthorized = booking.customerId.toString() === userId ||
    booking.vendorId.toString() === userId ||
    req.user.role === 'admin';

  if (!isAuthorized) {
    const { sendForbidden } = require('../utils/responseHelper');
    return sendForbidden(res, 'You are not authorized to view attachments for this booking');
  }

  const { sendSuccess } = require('../utils/responseHelper');
  return sendSuccess(res, 'Booking attachments retrieved successfully', {
    attachments: booking.attachments || [],
    count: booking.attachments?.length || 0
  });
});

// Booking communication log
router.get('/:bookingId/communications', async (req, res) => {
  const { bookingId } = req.params;
  const userId = req.user.id;

  const Booking = require('../models/Booking');
  const booking = await Booking.findById(bookingId);

  if (!booking) {
    const { sendNotFound } = require('../utils/responseHelper');
    return sendNotFound(res, 'Booking not found');
  }

  // Check authorization
  const isAuthorized = booking.customerId.toString() === userId ||
    booking.vendorId.toString() === userId ||
    req.user.role === 'admin';

  if (!isAuthorized) {
    const { sendForbidden } = require('../utils/responseHelper');
    return sendForbidden(res, 'You are not authorized to view communications for this booking');
  }

  // Get messages related to this booking
  const Message = require('../models/Message');
  const messages = await Message.find({ bookingId })
    .populate('senderId', 'firstName lastName profileImage role')
    .sort({ createdAt: 1 });

  const { sendSuccess } = require('../utils/responseHelper');
  return sendSuccess(res, 'Booking communications retrieved successfully', {
    messages,
    totalMessages: messages.length
  });
});

module.exports = router;
