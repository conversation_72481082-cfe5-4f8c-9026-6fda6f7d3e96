{"info": {"name": "Party Pipeline API - Complete", "description": "Complete API collection for Party Pipeline - Event Marketplace Platform with all endpoints", "version": "2.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "http://localhost:5000", "type": "string"}, {"key": "accessToken", "value": "", "type": "string"}, {"key": "refreshToken", "value": "", "type": "string"}, {"key": "userId", "value": "", "type": "string"}, {"key": "vendorId", "value": "", "type": "string"}, {"key": "serviceId", "value": "", "type": "string"}, {"key": "bookingId", "value": "", "type": "string"}, {"key": "cartItemId", "value": "", "type": "string"}, {"key": "messageId", "value": "", "type": "string"}, {"key": "reviewId", "value": "", "type": "string"}, {"key": "categoryId", "value": "", "type": "string"}, {"key": "transactionId", "value": "", "type": "string"}], "item": [{"name": "🔐 Authentication", "item": [{"name": "Register Customer", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"Doe\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePass123!\",\n  \"confirmPassword\": \"SecurePass123!\",\n  \"phone\": \"+**********\",\n  \"role\": \"customer\",\n  \"agreeToTerms\": true,\n  \"marketingConsent\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}}}, {"name": "Register Vendor", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"VendorPass123!\",\n  \"confirmPassword\": \"VendorPass123!\",\n  \"phone\": \"+1234567891\",\n  \"role\": \"vendor\",\n  \"businessName\": \"Amazing Events Co\",\n  \"businessType\": \"individual\",\n  \"description\": \"We provide amazing event services.\",\n  \"marketingConsent\": true,\n  \"agreeToTerms\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data.user.role === 'vendor') {", "        pm.environment.set('vendorId', response.data.user.id);", "    }", "}"]}}]}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePass123!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('accessToken', response.data.tokens.accessToken);", "    pm.environment.set('refreshToken', response.data.tokens.refreshToken);", "    pm.environment.set('userId', response.data.user.id);", "}"]}}]}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refreshToken}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/refresh", "host": ["{{baseUrl}}"], "path": ["api", "auth", "refresh"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('accessToken', response.data.accessToken);", "}"]}}]}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/auth/logout", "host": ["{{baseUrl}}"], "path": ["api", "auth", "logout"]}}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"verification_token_here\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/verify-email", "host": ["{{baseUrl}}"], "path": ["api", "auth", "verify-email"]}}}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/forgot-password", "host": ["{{baseUrl}}"], "path": ["api", "auth", "forgot-password"]}}}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"reset_token_here\",\n  \"password\": \"NewSecurePass123!\",\n  \"confirmPassword\": \"NewSecurePass123!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/reset-password", "host": ["{{baseUrl}}"], "path": ["api", "auth", "reset-password"]}}}]}, {"name": "👤 Users", "item": [{"name": "Get My Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/users/me", "host": ["{{baseUrl}}"], "path": ["api", "users", "me"]}}}, {"name": "Update My Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON> Updated\",\n  \"lastName\": \"<PERSON><PERSON> Updated\",\n  \"phone\": \"+**********\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users/me", "host": ["{{baseUrl}}"], "path": ["api", "users", "me"]}}}, {"name": "Get User Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/users/me/stats", "host": ["{{baseUrl}}"], "path": ["api", "users", "me", "stats"]}}}, {"name": "Get My Addresses", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/users/me/addresses", "host": ["{{baseUrl}}"], "path": ["api", "users", "me", "addresses"]}}}, {"name": "Add Address", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"home\",\n  \"street\": \"123 Main St\",\n  \"city\": \"New York\",\n  \"state\": \"NY\",\n  \"zipCode\": \"10001\",\n  \"country\": \"US\",\n  \"isDefault\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/users/me/addresses", "host": ["{{baseUrl}}"], "path": ["api", "users", "me", "addresses"]}}}, {"name": "Upload Profile Image", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "image", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/api/users/me/profile-image", "host": ["{{baseUrl}}"], "path": ["api", "users", "me", "profile-image"]}}}]}, {"name": "🏢 Vendors", "item": [{"name": "Get Vendor Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/vendors/dashboard", "host": ["{{baseUrl}}"], "path": ["api", "vendors", "dashboard"]}}}, {"name": "Get Vendor Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/vendors/profile", "host": ["{{baseUrl}}"], "path": ["api", "vendors", "profile"]}}}, {"name": "Update Vendor Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"businessName\": \"Amazing Events Co Updated\",\n  \"description\": \"We provide exceptional event services for all occasions\",\n  \"businessType\": \"company\",\n  \"website\": \"https://amazingevents.com\",\n  \"serviceAreas\": [\n    {\n      \"city\": \"New York\",\n      \"state\": \"NY\",\n      \"radius\": 50\n    }\n  ],\n  \"specialties\": [\"Weddings\", \"Corporate Events\", \"Birthday Parties\"]\n}"}, "url": {"raw": "{{baseUrl}}/api/vendors/profile", "host": ["{{baseUrl}}"], "path": ["api", "vendors", "profile"]}}}, {"name": "Start Stripe Onboarding", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"businessInfo\": {\n    \"legalBusinessName\": \"Amazing Events Co LLC\",\n    \"businessType\": \"company\",\n    \"address\": {\n      \"street\": \"456 Business Ave\",\n      \"city\": \"New York\",\n      \"state\": \"NY\",\n      \"zipCode\": \"10002\",\n      \"country\": \"US\"\n    }\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/vendors/onboarding", "host": ["{{baseUrl}}"], "path": ["api", "vendors", "onboarding"]}}}, {"name": "Get Onboarding Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/vendors/onboarding/status", "host": ["{{baseUrl}}"], "path": ["api", "vendors", "onboarding", "status"]}}}, {"name": "Get Verification Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/vendors/verification/status", "host": ["{{baseUrl}}"], "path": ["api", "vendors", "verification", "status"]}}}, {"name": "Update Stripe Account", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"updateData\": {\n    \"individual\": {\n      \"address\": {\n        \"line1\": \"123 Updated Street\",\n        \"city\": \"New York\",\n        \"state\": \"NY\",\n        \"postal_code\": \"10001\",\n        \"country\": \"US\"\n      },\n      \"phone\": \"+**********\"\n    }\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/vendors/stripe/update-account", "host": ["{{baseUrl}}"], "path": ["api", "vendors", "stripe", "update-account"]}}}, {"name": "Submit Verification Document", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "document", "type": "file", "src": "path/to/document.pdf"}, {"key": "documentType", "value": "identity_document", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/api/vendors/verification/submit-document", "host": ["{{baseUrl}}"], "path": ["api", "vendors", "verification", "submit-document"]}}}, {"name": "Get Vendor Services", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/vendors/services?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "vendors", "services"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Create Service", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Premium Wedding DJ Service\",\n  \"description\": \"Professional DJ service for weddings with premium equipment and music library\",\n  \"categoryId\": \"{{categoryId}}\",\n  \"pricing\": {\n    \"method\": \"package\",\n    \"packages\": [\n      {\n        \"title\": \"Basic Package\",\n        \"price\": 800,\n        \"description\": \"4 hours of DJ service with basic sound system\",\n        \"duration\": 240,\n        \"included\": [\"DJ Service\", \"Basic Sound System\", \"Microphone\"]\n      },\n      {\n        \"title\": \"Premium Package\",\n        \"price\": 1200,\n        \"description\": \"6 hours of DJ service with premium equipment\",\n        \"duration\": 360,\n        \"included\": [\"DJ Service\", \"Premium Sound System\", \"Wireless Microphones\", \"Uplighting\"]\n      }\n    ]\n  },\n  \"addOns\": [\n    {\n      \"name\": \"Extra Lighting\",\n      \"description\": \"Professional uplighting package\",\n      \"price\": 200,\n      \"required\": false\n    },\n    {\n      \"name\": \"Additional Microphone\",\n      \"description\": \"Wireless microphone for speeches\",\n      \"price\": 50,\n      \"required\": false\n    }\n  ],\n  \"availability\": {\n    \"schedule\": [\n      {\n        \"dayOfWeek\": 1,\n        \"isAvailable\": true,\n        \"timeSlots\": [{\"startTime\": \"09:00\", \"endTime\": \"17:00\"}]\n      },\n      {\n        \"dayOfWeek\": 2,\n        \"isAvailable\": true,\n        \"timeSlots\": [{\"startTime\": \"09:00\", \"endTime\": \"17:00\"}]\n      },\n      {\n        \"dayOfWeek\": 3,\n        \"isAvailable\": true,\n        \"timeSlots\": [{\"startTime\": \"09:00\", \"endTime\": \"17:00\"}]\n      },\n      {\n        \"dayOfWeek\": 4,\n        \"isAvailable\": true,\n        \"timeSlots\": [{\"startTime\": \"09:00\", \"endTime\": \"17:00\"}]\n      },\n      {\n        \"dayOfWeek\": 5,\n        \"isAvailable\": true,\n        \"timeSlots\": [{\"startTime\": \"09:00\", \"endTime\": \"17:00\"}]\n      },\n      {\n        \"dayOfWeek\": 6,\n        \"isAvailable\": true,\n        \"timeSlots\": [{\"startTime\": \"10:00\", \"endTime\": \"18:00\"}]\n      },\n      {\n        \"dayOfWeek\": 0,\n        \"isAvailable\": false,\n        \"timeSlots\": []\n      }\n    ],\n    \"minDuration\": 60,\n    \"maxDuration\": 480,\n    \"advanceBooking\": 7,\n    \"maxAdvanceBooking\": 365\n  },\n  \"location\": {\n    \"type\": \"mobile\",\n    \"serviceRadius\": 50\n  },\n  \"tags\": [\"wedding\", \"dj\", \"music\", \"party\"]\n}"}, "url": {"raw": "{{baseUrl}}/api/vendors/services", "host": ["{{baseUrl}}"], "path": ["api", "vendors", "services"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('serviceId', response.data._id);", "}"]}}]}, {"name": "Get Vendor Bookings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/vendors/bookings?status=pending", "host": ["{{baseUrl}}"], "path": ["api", "vendors", "bookings"], "query": [{"key": "status", "value": "pending"}]}}}, {"name": "Approve Booking", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"Great! I'm available for your event. Looking forward to working with you!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/vendors/bookings/{{bookingId}}/approve", "host": ["{{baseUrl}}"], "path": ["api", "vendors", "bookings", "{{bookingId}}", "approve"]}}}, {"name": "Get Vendor Balance", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/vendors/balance", "host": ["{{baseUrl}}"], "path": ["api", "vendors", "balance"]}}}, {"name": "<PERSON> Vendor Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/vendors/analytics?period=last_30_days", "host": ["{{baseUrl}}"], "path": ["api", "vendors", "analytics"], "query": [{"key": "period", "value": "last_30_days"}]}}}, {"name": "Get Vendor Bookings Report", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/vendors/reports/bookings?period=last_30_days&format=json&status=completed", "host": ["{{baseUrl}}"], "path": ["api", "vendors", "reports", "bookings"], "query": [{"key": "period", "value": "last_30_days", "description": "Options: last_7_days, last_30_days, last_90_days, this_year"}, {"key": "format", "value": "json", "description": "Options: json, csv"}, {"key": "status", "value": "completed", "description": "Optional filter by booking status"}, {"key": "startDate", "value": "2023-01-01", "description": "Custom start date (YYYY-MM-DD)", "disabled": true}, {"key": "endDate", "value": "2023-12-31", "description": "Custom end date (YYYY-MM-DD)", "disabled": true}]}}}, {"name": "Get Vendor Revenue Report", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/vendors/reports/revenue?period=last_30_days&format=json&groupBy=day", "host": ["{{baseUrl}}"], "path": ["api", "vendors", "reports", "revenue"], "query": [{"key": "period", "value": "last_30_days", "description": "Options: last_7_days, last_30_days, last_90_days, this_year"}, {"key": "format", "value": "json", "description": "Options: json, csv"}, {"key": "groupBy", "value": "day", "description": "Options: day, week, month"}, {"key": "startDate", "value": "2023-01-01", "description": "Custom start date (YYYY-MM-DD)", "disabled": true}, {"key": "endDate", "value": "2023-12-31", "description": "Custom end date (YYYY-MM-DD)", "disabled": true}]}}}, {"name": "Get Vendor Performance Report", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/vendors/reports/performance?period=last_30_days&format=json", "host": ["{{baseUrl}}"], "path": ["api", "vendors", "reports", "performance"], "query": [{"key": "period", "value": "last_30_days", "description": "Options: last_7_days, last_30_days, last_90_days, this_year"}, {"key": "format", "value": "json", "description": "Options: json, csv"}, {"key": "startDate", "value": "2023-01-01", "description": "Custom start date (YYYY-MM-DD)", "disabled": true}, {"key": "endDate", "value": "2023-12-31", "description": "Custom end date (YYYY-MM-DD)", "disabled": true}]}}}, {"name": "Download Vendor Bookings Report CSV", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/vendors/reports/bookings?format=csv&period=last_30_days", "host": ["{{baseUrl}}"], "path": ["api", "vendors", "reports", "bookings"], "query": [{"key": "format", "value": "csv"}, {"key": "period", "value": "last_30_days"}]}}}, {"name": "Download Vendor Revenue Report CSV", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/vendors/reports/revenue?format=csv&period=last_30_days&groupBy=month", "host": ["{{baseUrl}}"], "path": ["api", "vendors", "reports", "revenue"], "query": [{"key": "format", "value": "csv"}, {"key": "period", "value": "last_30_days"}, {"key": "groupBy", "value": "month"}]}}}, {"name": "Download Vendor Performance Report CSV", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/vendors/reports/performance?format=csv&period=last_30_days", "host": ["{{baseUrl}}"], "path": ["api", "vendors", "reports", "performance"], "query": [{"key": "format", "value": "csv"}, {"key": "period", "value": "last_30_days"}]}}}]}, {"name": "🛍️ Services", "item": [{"name": "Get All Services", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/services?page=1&limit=20&sortBy=newest", "host": ["{{baseUrl}}"], "path": ["api", "services"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "sortBy", "value": "newest"}]}}}, {"name": "Search Services", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/services/search?q=wedding&category={{categoryId}}&minPrice=500&maxPrice=2000", "host": ["{{baseUrl}}"], "path": ["api", "services", "search"], "query": [{"key": "q", "value": "wedding"}, {"key": "category", "value": "{{categoryId}}"}, {"key": "minPrice", "value": "500"}, {"key": "maxPrice", "value": "2000"}]}}}, {"name": "Get Service by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/services/{{serviceId}}", "host": ["{{baseUrl}}"], "path": ["api", "services", "{{serviceId}}"]}}}, {"name": "Check Service Availability", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/services/{{serviceId}}/availability?date=2025-12-15&startTime=18:00&duration=240", "host": ["{{baseUrl}}"], "path": ["api", "services", "{{serviceId}}", "availability"], "query": [{"key": "date", "value": "2025-12-15"}, {"key": "startTime", "value": "18:00"}, {"key": "duration", "value": "240"}]}}}, {"name": "Calculate Service Price", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"selectedPackage\": \"Premium Package\",\n  \"duration\": 360,\n  \"attendees\": 100,\n  \"addOns\": [\n    {\n      \"title\": \"Extra Lighting\",\n      \"quantity\": 1,\n      \"price\": 200\n    }\n  ],\n  \"location\": {\n    \"lat\": 40.7128,\n    \"lng\": -74.0060\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/services/{{serviceId}}/calculate-price", "host": ["{{baseUrl}}"], "path": ["api", "services", "{{serviceId}}", "calculate-price"]}}}, {"name": "Get Service Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/services/categories", "host": ["{{baseUrl}}"], "path": ["api", "services", "categories"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.length > 0) {", "        pm.environment.set('categoryId', response.data[0]._id);", "    }", "}"]}}]}, {"name": "Get Featured Services", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/services/featured?limit=6", "host": ["{{baseUrl}}"], "path": ["api", "services", "featured"], "query": [{"key": "limit", "value": "6"}]}}}]}, {"name": "📅 Bookings", "item": [{"name": "Create Booking", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"serviceId\": \"{{serviceId}}\",\n  \"eventDetails\": {\n    \"date\": \"2024-06-15T00:00:00.000Z\",\n    \"startTime\": \"18:00\",\n    \"duration\": 360,\n    \"attendeeCount\": 100,\n    \"eventType\": \"Wedding Reception\",\n    \"specialRequests\": \"Please bring extra microphones for speeches\"\n  },\n  \"selectedOptions\": {\n    \"package\": \"Premium Package\",\n    \"customizations\": [{\"question\": \"Special lighting needed?\", \"answer\": \"Yes, uplighting\"}]\n  },\n  \"addOns\": [\n    {\n      \"title\": \"Extra Lighting\",\n      \"price\": 200,\n      \"quantity\": 1\n    }\n  ],\n  \"notes\": \"This is for our wedding reception. Very important day!\",\n  \"contactInfo\": {\n    \"primaryContact\": {\n      \"name\": \"<PERSON>\",\n      \"phone\": \"+**********\",\n      \"email\": \"<EMAIL>\"\n    },\n    \"emergencyContact\": {\n      \"name\": \"<PERSON> Doe\",\n      \"phone\": \"+1234567891\",\n      \"relationship\": \"Spouse\"\n    }\n  },\n  \"location\": {\n    \"address\": \"123 Wedding Venue St\",\n    \"city\": \"New York\",\n    \"state\": \"NY\",\n    \"zipCode\": \"10001\",\n    \"country\": \"US\",\n    \"lat\": 40.7128,\n    \"lng\": -74.0060,\n    \"venueNotes\": \"Enter through main entrance\",\n    \"parkingInfo\": \"Free parking available in lot behind venue\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/bookings", "host": ["{{baseUrl}}"], "path": ["api", "bookings"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('bookingId', response.data.booking._id);", "}"]}}]}, {"name": "Get User Bookings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/bookings?page=1&limit=10&sortBy=newest", "host": ["{{baseUrl}}"], "path": ["api", "bookings"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "sortBy", "value": "newest"}]}}}, {"name": "Get Booking by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/bookings/{{bookingId}}", "host": ["{{baseUrl}}"], "path": ["api", "bookings", "{{bookingId}}"]}}}, {"name": "Update Booking", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"notes\": \"Updated notes for the booking\",\n  \"eventDetails\": {\n    \"specialRequests\": \"Updated special requests\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/bookings/{{bookingId}}", "host": ["{{baseUrl}}"], "path": ["api", "bookings", "{{bookingId}}"]}}}, {"name": "Cancel Booking", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Event postponed due to weather concerns\",\n  \"refundRequested\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/bookings/{{bookingId}}", "host": ["{{baseUrl}}"], "path": ["api", "bookings", "{{bookingId}}"]}}}, {"name": "Process Booking Payment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"paymentMethodId\": \"pm_card_visa\",\n  \"savePaymentMethod\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/bookings/{{bookingId}}/payment", "host": ["{{baseUrl}}"], "path": ["api", "bookings", "{{bookingId}}", "payment"]}}}, {"name": "Get Booking Timeline", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/bookings/{{bookingId}}/timeline", "host": ["{{baseUrl}}"], "path": ["api", "bookings", "{{bookingId}}", "timeline"]}}}]}, {"name": "🛒 Cart", "item": [{"name": "Get Cart", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/cart", "host": ["{{baseUrl}}"], "path": ["api", "cart"]}}}, {"name": "Add to Cart", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"serviceId\": \"{{serviceId}}\",\n  \"eventDetails\": {\n    \"date\": \"2024-07-20T00:00:00.000Z\",\n    \"startTime\": \"19:00\",\n    \"duration\": 240,\n    \"attendeeCount\": 75,\n    \"eventType\": \"Birthday Party\"\n  },\n  \"selectedOptions\": {\n    \"package\": \"Basic Package\"\n  },\n  \"addOns\": [],\n  \"notes\": \"Birthday party for my daughter\",\n  \"location\": {\n    \"address\": \"456 Party St\",\n    \"city\": \"New York\",\n    \"state\": \"NY\",\n    \"zipCode\": \"10002\",\n    \"country\": \"US\",\n    \"lat\": 40.7149,\n    \"lng\": -74.0059\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/cart/add", "host": ["{{baseUrl}}"], "path": ["api", "cart", "add"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.cartItem) {", "        pm.environment.set('cartItemId', response.data.cartItem._id);", "    }", "}"]}}]}, {"name": "Update Cart Item", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"eventDetails\": {\n    \"attendeeCount\": 85\n  },\n  \"notes\": \"Updated notes for cart item\"\n}"}, "url": {"raw": "{{baseUrl}}/api/cart/items/{{cartItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "cart", "items", "{{cartItemId}}"]}}}, {"name": "Remove from Cart", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/cart/items/{{cartItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "cart", "items", "{{cartItemId}}"]}}}, {"name": "Apply Promo Code", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"promoCode\": \"WELCOME10\"\n}"}, "url": {"raw": "{{baseUrl}}/api/cart/promo-code", "host": ["{{baseUrl}}"], "path": ["api", "cart", "promo-code"]}}}, {"name": "Get Checkout Summary", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/cart/checkout/summary", "host": ["{{baseUrl}}"], "path": ["api", "cart", "checkout", "summary"]}}}, {"name": "Validate <PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/cart/checkout/validate", "host": ["{{baseUrl}}"], "path": ["api", "cart", "checkout", "validate"]}}}, {"name": "Clear Cart", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/cart/clear", "host": ["{{baseUrl}}"], "path": ["api", "cart", "clear"]}}}]}, {"name": "💳 Payments", "item": [{"name": "Create Payment Intent", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"bookingId\": \"{{bookingId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/payments/intent", "host": ["{{baseUrl}}"], "path": ["api", "payments", "intent"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.paymentIntent) {", "        pm.environment.set('transactionId', response.data.paymentIntent.id);", "    }", "}"]}}]}, {"name": "Confirm Payment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"paymentIntentId\": \"pi_**********\"\n}"}, "url": {"raw": "{{baseUrl}}/api/payments/confirm", "host": ["{{baseUrl}}"], "path": ["api", "payments", "confirm"]}}}, {"name": "Get Payment History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/payments/history?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "payments", "history"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Get Payment Methods", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/payments/methods", "host": ["{{baseUrl}}"], "path": ["api", "payments", "methods"]}}}, {"name": "Add Payment Method", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"paymentMethodId\": \"pm_card_visa\"\n}"}, "url": {"raw": "{{baseUrl}}/api/payments/methods", "host": ["{{baseUrl}}"], "path": ["api", "payments", "methods"]}}}, {"name": "Process Refund", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 500,\n  \"reason\": \"Event cancelled by customer\"\n}"}, "url": {"raw": "{{baseUrl}}/api/payments/bookings/{{bookingId}}/refund", "host": ["{{baseUrl}}"], "path": ["api", "payments", "bookings", "{{bookingId}}", "refund"]}}}, {"name": "Get Transaction Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/payments/transactions/{{transactionId}}", "host": ["{{baseUrl}}"], "path": ["api", "payments", "transactions", "{{transactionId}}"]}}}]}, {"name": "💬 Messages", "item": [{"name": "Get Conversations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/messages/conversations?page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["api", "messages", "conversations"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Get Messages with User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/messages/conversations/{{vendorId}}?page=1&limit=50", "host": ["{{baseUrl}}"], "path": ["api", "messages", "conversations", "{{vendorId}}"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}]}}}, {"name": "Send Message", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"recipientId\": \"{{vendorId}}\",\n  \"content\": \"Hi! I'm interested in your DJ service for my wedding. Are you available on June 15th?\",\n  \"bookingId\": \"{{bookingId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/messages/send", "host": ["{{baseUrl}}"], "path": ["api", "messages", "send"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.message) {", "        pm.environment.set('messageId', response.data.message._id);", "    }", "}"]}}]}, {"name": "Start Conversation", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"recipientId\": \"{{vendorId}}\",\n  \"bookingId\": \"{{bookingId}}\",\n  \"initialMessage\": \"Hello! I'd like to discuss my upcoming event booking.\"\n}"}, "url": {"raw": "{{baseUrl}}/api/messages/start-conversation", "host": ["{{baseUrl}}"], "path": ["api", "messages", "start-conversation"]}}}, {"name": "<PERSON> as Read", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/messages/messages/{{messageId}}/read", "host": ["{{baseUrl}}"], "path": ["api", "messages", "messages", "{{messageId}}", "read"]}}}, {"name": "Search Messages", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/messages/search?query=wedding&page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["api", "messages", "search"], "query": [{"key": "query", "value": "wedding"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Get Unread Count", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/messages/unread-count", "host": ["{{baseUrl}}"], "path": ["api", "messages", "unread-count"]}}}, {"name": "Upload Message Attachment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/api/messages/upload", "host": ["{{baseUrl}}"], "path": ["api", "messages", "upload"]}}}]}, {"name": "⭐ Reviews", "item": [{"name": "Get Service Reviews", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/reviews/services/{{serviceId}}?page=1&limit=10&sortBy=newest", "host": ["{{baseUrl}}"], "path": ["api", "reviews", "services", "{{serviceId}}"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "sortBy", "value": "newest"}]}}}, {"name": "Get Vendor Reviews", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/reviews/vendors/{{vendorId}}?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "reviews", "vendors", "{{vendorId}}"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Create Review", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"bookingId\": \"{{bookingId}}\",\n  \"rating\": {\n    \"overall\": 5,\n    \"quality\": 5,\n    \"communication\": 5,\n    \"value\": 4,\n    \"timeliness\": 5\n  },\n  \"title\": \"Absolutely Amazing DJ Service!\",\n  \"content\": \"The DJ service was exceptional! They played exactly what we wanted and kept the dance floor packed all night. Highly professional and responsive to our needs. Would definitely book again!\",\n  \"photos\": [],\n  \"isPublic\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/reviews", "host": ["{{baseUrl}}"], "path": ["api", "reviews"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('reviewId', response.data.review._id);", "}"]}}]}, {"name": "Get My Reviews", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/reviews/my-reviews?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "reviews", "my-reviews"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Update Review", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Updated Review Title\",\n  \"content\": \"Updated review content with more details about the experience.\"\n}"}, "url": {"raw": "{{baseUrl}}/api/reviews/{{reviewId}}", "host": ["{{baseUrl}}"], "path": ["api", "reviews", "{{reviewId}}"]}}}, {"name": "Add Vendor Response", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"response\": \"Thank you so much for this wonderful review! It was a pleasure working with you and your family. We're thrilled that you had such a great experience!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/reviews/{{reviewId}}/respond", "host": ["{{baseUrl}}"], "path": ["api", "reviews", "{{reviewId}}", "respond"]}}}, {"name": "Mark <PERSON> as Helpful", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/reviews/{{reviewId}}/helpful", "host": ["{{baseUrl}}"], "path": ["api", "reviews", "{{reviewId}}", "helpful"]}}}, {"name": "Upload Review Photos", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "photos", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/api/reviews/upload-photos", "host": ["{{baseUrl}}"], "path": ["api", "reviews", "upload-photos"]}}}]}, {"name": "🔧 Admin", "item": [{"name": "Get Admin Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/admin/dashboard?period=last_30_days", "host": ["{{baseUrl}}"], "path": ["api", "admin", "dashboard"], "query": [{"key": "period", "value": "last_30_days"}]}}}, {"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/admin/users?page=1&limit=20&role=customer", "host": ["{{baseUrl}}"], "path": ["api", "admin", "users"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "role", "value": "customer"}]}}}, {"name": "Get User by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/admin/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "admin", "users", "{{userId}}"]}}}, {"name": "Update User Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"isActive\": false,\n  \"reason\": \"Account suspended for policy violation\"\n}"}, "url": {"raw": "{{baseUrl}}/api/admin/users/{{userId}}/status", "host": ["{{baseUrl}}"], "path": ["api", "admin", "users", "{{userId}}", "status"]}}}, {"name": "Get Vendor Applications", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/admin/vendors/applications?status=pending&page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["api", "admin", "vendors", "applications"], "query": [{"key": "status", "value": "pending"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Approve Vendor", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"approved\",\n  \"feedback\": \"All documentation verified. Welcome to the platform!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/admin/vendors/{{vendorId}}/status", "host": ["{{baseUrl}}"], "path": ["api", "admin", "vendors", "{{vendorId}}", "status"]}}}, {"name": "Get All Services (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/admin/services?status=active&page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["api", "admin", "services"], "query": [{"key": "status", "value": "active"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Get All Bookings (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/admin/bookings?page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["api", "admin", "bookings"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Get Financial Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/admin/analytics/financial?period=last_30_days", "host": ["{{baseUrl}}"], "path": ["api", "admin", "analytics", "financial"], "query": [{"key": "period", "value": "last_30_days"}]}}}, {"name": "Get Platform Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/admin/analytics/platform?period=last_30_days", "host": ["{{baseUrl}}"], "path": ["api", "admin", "analytics", "platform"], "query": [{"key": "period", "value": "last_30_days"}]}}}, {"name": "Get System Health", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/admin/system/health", "host": ["{{baseUrl}}"], "path": ["api", "admin", "system", "health"]}}}, {"name": "Manage Categories", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/admin/categories", "host": ["{{baseUrl}}"], "path": ["api", "admin", "categories"]}}}, {"name": "Create Category", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Entertainment\",\n  \"slug\": \"entertainment\",\n  \"description\": \"Entertainment services for events\",\n  \"icon\": \"music\",\n  \"featured\": true,\n  \"sortOrder\": 1\n}"}, "url": {"raw": "{{baseUrl}}/api/admin/categories", "host": ["{{baseUrl}}"], "path": ["api", "admin", "categories"]}}}]}, {"name": "📄 CMS", "item": [{"name": "Get All Pages", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/cms/pages?status=published&page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["api", "cms", "pages"], "query": [{"key": "status", "value": "published"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Get Page by Slug", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/cms/pages/about-us", "host": ["{{baseUrl}}"], "path": ["api", "cms", "pages", "about-us"]}}}, {"name": "Get All Blog Posts", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/cms/blog?status=published&page=1&limit=10&sortBy=newest", "host": ["{{baseUrl}}"], "path": ["api", "cms", "blog"], "query": [{"key": "status", "value": "published"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "sortBy", "value": "newest"}]}}}, {"name": "Get Blog Post by Slug", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/cms/blog/event-planning-tips", "host": ["{{baseUrl}}"], "path": ["api", "cms", "blog", "event-planning-tips"]}}}, {"name": "Get Featured Content", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/cms/featured", "host": ["{{baseUrl}}"], "path": ["api", "cms", "featured"]}}}, {"name": "Search Content", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/cms/search?q=wedding&type=all&page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "cms", "search"], "query": [{"key": "q", "value": "wedding"}, {"key": "type", "value": "all"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Contact Form Submission", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"subject\": \"Inquiry about vendor registration\",\n  \"message\": \"Hi, I'm interested in becoming a vendor on your platform. Could you please provide more information about the registration process?\",\n  \"phone\": \"+**********\",\n  \"company\": \"Amazing Events Co\"\n}"}, "url": {"raw": "{{baseUrl}}/api/cms/contact", "host": ["{{baseUrl}}"], "path": ["api", "cms", "contact"]}}}, {"name": "Newsletter Subscription", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"firstName\": \"<PERSON>\",\n  \"interests\": [\"events\", \"vendors\", \"tips\"]\n}"}, "url": {"raw": "{{baseUrl}}/api/cms/newsletter/subscribe", "host": ["{{baseUrl}}"], "path": ["api", "cms", "newsletter", "subscribe"]}}}, {"name": "Create Page (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"About Party Pipeline\",\n  \"slug\": \"about-us\",\n  \"content\": \"Welcome to Party Pipeline, your premier destination for finding the perfect event services...\",\n  \"excerpt\": \"Learn more about Party Pipeline and our mission\",\n  \"type\": \"about\",\n  \"status\": \"published\",\n  \"featured\": true,\n  \"seoTitle\": \"About Party Pipeline - Event Marketplace\",\n  \"seoDescription\": \"Discover how Party Pipeline connects customers with the best event service providers\"\n}"}, "url": {"raw": "{{baseUrl}}/api/cms/pages", "host": ["{{baseUrl}}"], "path": ["api", "cms", "pages"]}}}, {"name": "Create Blog Post (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"10 Essential Tips for Planning Your Dream Wedding\",\n  \"slug\": \"wedding-planning-tips\",\n  \"content\": \"Planning a wedding can be overwhelming, but with the right approach and tips, you can create the perfect day...\",\n  \"excerpt\": \"Expert tips to help you plan the wedding of your dreams\",\n  \"category\": \"Wedding Planning\",\n  \"tags\": [\"wedding\", \"planning\", \"tips\", \"bride\", \"groom\"],\n  \"status\": \"published\",\n  \"featured\": true,\n  \"seoTitle\": \"Wedding Planning Tips - Party Pipeline Blog\",\n  \"seoDescription\": \"Get expert wedding planning tips and advice from Party Pipeline\"\n}"}, "url": {"raw": "{{baseUrl}}/api/cms/blog", "host": ["{{baseUrl}}"], "path": ["api", "cms", "blog"]}}}]}, {"name": "🔧 System", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}, {"name": "API Root", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/", "host": ["{{baseUrl}}"], "path": [""]}}}]}]}