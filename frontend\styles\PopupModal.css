
/* =========================
   🪟 PopupModal (reusable)
   ========================= */
   .PopupModal-overlay {
    position: fixed;
    inset: 0;
    background: var(--pp-overlay-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--pp-z-index-modal);
    padding: var(--pp-sp-24);
  }
  .PopupModal-closeContainer {
    display: flex;
    justify-content: flex-end;
    padding: var(--pp-sp-16) var(--pp-sp-16) 0 var(--pp-sp-16);
  }
  .PopupModal-container {
    position: relative;
    width: 951px;
    
    background: var(--pp-clr-primary);
    border-radius: var(--pp-bor-rad-16);
    box-shadow: var(--pp-shdw-xl);
    color: var(--pp-clr-text-main);
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 2 * var(--pp-sp-24));
    overflow: hidden;
  }
  
  .PopupModal-close {
   
    width: 36px;
    height: 36px;
    display: grid;
    place-items: center;
    
    border-radius: var(--pp-bor-rad-round);
    background: var(--pp-clr-primary);
    color: var(--pp-clr-text-main);
    cursor: pointer;
  }
  
  .PopupModal-close:hover {
    background: #f7f7f7;
  }
  
  .PopupModal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--pp-sp-16);
    padding: var(--pp-sp-24) var(--pp-sp-24) var(--pp-sp-12) var(--pp-sp-24);
  }
  
  .PopupModal-title {
    font-size: var(--pp-font-heading5);
    font-weight: 600;
  }
  
  .PopupModal-headerRight {
    display: flex;
    align-items: center;
    gap: var(--pp-sp-16);
  }
  
  .PopupModal-content {
    padding: 0 var(--pp-sp-24) var(--pp-sp-24) var(--pp-sp-24);
    overflow: auto;
    flex: 1;
  }
  
  .PopupModal-footer {
    padding: var(--pp-sp-16) var(--pp-sp-24);
    border-top: 1px solid var(--pp-clr-border-light);
    background: var(--pp-clr-primary);
  }
  
  @media (max-width: 640px) {
    .PopupModal-overlay {
      padding: var(--pp-sp-12);
    }
    .PopupModal-container {
      border-radius: var(--pp-bor-rad-12);
    }
    .PopupModal-header {
      padding: var(--pp-sp-16) var(--pp-sp-16) var(--pp-sp-8) var(--pp-sp-16);
    }
    .PopupModal-content {
      padding: 0 var(--pp-sp-16) var(--pp-sp-16) var(--pp-sp-16);
    }
    .PopupModal-footer {
      padding: var(--pp-sp-12) var(--pp-sp-16);
    }
  }