.CustomerMessages-root {
    height: calc(100vh - (119px));
    background: var(--pp-clr-bg);
    color: var(--pp-clr-text-main);
    font-family: var(--pp-font-Metro-Sans);
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  .CustomerMessages-mainOnly {
    flex: 1;
    display: grid;
    grid-template-columns: 360px 1fr;
    gap: 24px;
    min-height: 0;
  }
  
  .CustomerMessages-chatlist {
    border-right: 1px solid var(--pp-clr-border-light);
    background: var(--pp-clr-primary);
  padding-right: 10px;
    display: flex;
    flex-direction: column;
    min-width: 0;
    height: 100%;
    overflow-y: auto;
    transition: transform 300ms ease, opacity 300ms ease;
  }
  .CustomerMessages-section{
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--pp-sp-8);
    background: var(--pp-clr-bg);
  }
  
  .CustomerMessages-messagesTitle {
    font-size: var(--pp-font-heading4);
    font-weight: 400;
    margin-left: var(--pp-sp-16);
    margin-bottom: var(--pp-sp-8);
    
  }
  .CustomerMessages-listDivider {
    height: 2px;
    background: var(--pp-clr-border-light);
    width: calc(100% - var(--pp-sp-32));
    margin-bottom: var(--pp-sp-12);
  }
  .CustomerMessages-contacts {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: var(--pp-sp-4);
  }
  .CustomerMessages-contactItem {
    display: flex;
    align-items: center;
    gap: var(--pp-sp-12);
    cursor: pointer;
    padding: var(--pp-sp-8) var(--pp-sp-8) var(--pp-sp-8) var(--pp-sp-8);
    border-radius: var(--pp-bor-rad-8);
    transition: background 0.2s;
  }
  .CustomerMessages-contactItem:hover,
  .CustomerMessages-contactItem--active {
    background: var(--pp-clr-border-light);
  }
  .CustomerMessages-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--pp-bor-rad-round);
    object-fit: cover;
  }
  .CustomerMessages-contactMain {
    flex: 1;
    min-width: 0;
  }
  .CustomerMessages-contactTop {
    display: flex;
    justify-content: space-between;
    gap: var(--pp-sp-8);
    font-size: var(--pp-font-small-font);
    font-weight: 600;
  }
  .CustomerMessages-name {
    color: var(--pp-clr-text-main);
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .CustomerMessages-date {
    color: var(--pp-clr-text-gray);
    font-size: var(--pp-font-extra-small);
    font-weight: 400;
  }
  .CustomerMessages-preview {
    font-size: var(--pp-font-extra-small);
    color: var(--pp-clr-text-gray);
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 170px;
  }
  .CustomerMessages-badge {
    background: #BBF6C4;
    color: #126F37;
    border-radius: 50%;
    padding: 3px 0 1px 0;
    font-size: 12px;
    width: 22px;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    margin-left: var(--pp-sp-24);
  }
  
  .CustomerMessages-chatThread {
    display: grid;
    grid-template-rows: auto 1fr auto;
    gap: var(--pp-sp-16);
    padding: var(--pp-sp-16);
    min-width: 0;
    width: 100%;
    height: calc(100vh - (80px + 122px));
    background: var(--pp-clr-bg);
    overflow: hidden;
  }
  .CustomerMessages-threadHeader,
  .CustomerMessages-compose {
    position: sticky;
    z-index: 100;
    background: var(--pp-clr-bg);
  }
  .CustomerMessages-threadHeader { top: 0; padding-bottom: var(--pp-sp-12); border-bottom: 1px solid var(--pp-clr-border-light); }
  .CustomerMessages-compose { bottom: 0; padding-top: var(--pp-sp-12); border-top: 1px solid var(--pp-clr-border-light); }
  .CustomerMessages-threadHeader {
    font-size: var(--pp-font-heading5);
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--pp-sp-8);
  }
  .CustomerMessages-threadTitle {
    font-size: var(--pp-font-heading5);
    font-weight: 700;
  }
  .CustomerMessages-bookedBtn {
    background: var(--pp-clr-primary);
    border: 1px solid var(--pp-clr-border);
    color: var(--pp-clr-text-main);
    padding: var(--pp-sp-8) var(--pp-sp-24);
    border-radius: var(--pp-bor-rad-24);
    font-size: var(--pp-font-extra-small);
    cursor: pointer;
    transition: border 0.2s;
  }
  .CustomerMessages-bookedBtn:hover {
    border-color: var(--pp-clr-blue-txt);
  }
  

  .CustomerMessages-messagesArea {
    display: flex;
    flex-direction: column;
    gap: var(--pp-sp-16);
    overflow-y: auto;
    min-height: 0;
    padding: 0 var(--pp-sp-10);
    -webkit-overflow-scrolling: touch;
  }
  
  .CustomerMessages-messageRow {
    display: flex;
    
    gap: var(--pp-sp-16);
  }
  
  
  .CustomerMessages-msgAvatar {
    width: 38px;
    height: 38px;
    border-radius: var(--pp-bor-rad-round);
    object-fit: cover;
    flex-shrink: 0;
  }
  .CustomerMessages-msgMeta {
    display: flex;
    align-items: center;
    gap: var(--pp-sp-8);
    margin-bottom: var(--pp-sp-4);
  }
  .CustomerMessages-msgSender {
    font-size: var(--pp-font-extra-small);
    font-weight: 600;
  }
  .CustomerMessages-msgDate {
    font-size: var(--pp-font-extra-small);
    color: var(--pp-clr-text-gray);
  }
  
  .CustomerMessages-msgText {
    font-size: var(--pp-font-extra-small);
    color: var(--pp-clr-text-main);
    margin-bottom: var(--pp-sp-4);
    word-break: break-word;
    background: none;
  }
  
  .CustomerMessages-compose {
    
    display: flex;
  
    gap: var(--pp-sp-12);
    border: 1px solid var(--pp-clr-border);
    border-radius: var(--pp-bor-rad-8);
    background: var(--pp-clr-primary);
    padding: 8px var(--pp-sp-16);
    width: 100%;
  }
  
  .CustomerMessages-input {
    flex: 1;
    border: none;
    font-size: var(--pp-font-extra-small);
    background: transparent;
    outline: none;
    color: var(--pp-clr-text-main);
    padding: var(--pp-sp-4) 0;
  }
  .CustomerMessages-sendBtn {
    background: var(--pp-clr-primary);
    border: none;
    border-radius: var(--pp-bor-rad-round);
    font-size: 26px;
    color:black;
    cursor: pointer;
    padding: 6px var(--pp-sp-8);
    transition: background 0.2s;
  }
  .CustomerMessages-sendBtn:hover {
    background: var(--pp-clr-bg);
  }
  
  .CustomerMessages-mobileBtn {
    display: none;
    background: transparent;
    border: none;
    color: var(--pp-clr-secondary);
    cursor: pointer;
    font-size: var(--pp-font-base-font);
    margin-right: var(--pp-sp-8);
  }

  /* Responsive behavior mirrored from vendor */
  @media (max-width: 1024px) {
    .CustomerMessages-mainOnly { grid-template-columns: 320px 1fr; }
  }
  @media (max-width: 768px) {
    .CustomerMessages-mainOnly {
      grid-template-columns: 1fr;
      height: calc(100vh - 40px);
    }
    .CustomerMessages-chatThread { height: calc(100vh-155px); overflow: hidden; }
    .CustomerMessages-messagesArea { overflow-y: auto; }
    .CustomerMessages-threadHeader { display: flex; flex-direction: column; gap: 10px; align-items: flex-start; }
    .CustomerMessages-threadHeader, .CustomerMessages-compose { position: sticky; }
    .CustomerMessages-mobileBtn { display: inline-flex; align-items: center; justify-content: center; }
    /* Show only one panel at a time */
    .CustomerMessages-root.CustomerMessages--chat-active .CustomerMessages-chatlist { display: none; }
    .CustomerMessages-root:not(.CustomerMessages--chat-active) .CustomerMessages-chatThread { display: none; }
    .CustomerMessages-chatlist {
      border-right: none;
   
    }
  }



  @media (max-width: 420px) {
    .CustomerMessages-chatThread { padding: 0; }
  }
  