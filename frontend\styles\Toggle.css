/* =========================
   🔄 Toggle Switch Component
   ========================= */

.pp-toggle-container {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-8);
}

.pp-toggle-wrapper {
  display: flex;
  flex-direction: column-reverse;
  align-items: flex-start;

  gap: var(--pp-sp-12);
  cursor: pointer;
  user-select: none;
  font-family: var(--pp-font-Metro-Sans);
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-main);
  font-weight: 500;
  position: relative;
}

.pp-toggle-input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.pp-toggle-switch {
  position: relative;
  width: 64px;
  height: 36px;
  background-color: var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-round);
  transition: all 0.3s ease;
  flex-shrink: 0;
  border: none;
}

.pp-toggle-switch-on {
  background-color: var(--pp-clr-secondary);
}

.pp-toggle-switch-off {
  background-color: var(--pp-clr-border);
}

.pp-toggle-switch-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pp-toggle-handle {
  position: absolute;
  left: 2px;
  top: 4px;
  width: 28px;
  transform: translateY(-50%);
  height: 28px;
  background-color: var(--pp-clr-primary);
  border-radius: var(--pp-bor-rad-round);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--pp-shdw-sm);
}

.pp-toggle-switch-on .pp-toggle-handle {
  transform: translateX(30px);
  background-color: var(--pp-clr-primary);
}

.pp-toggle-switch-off .pp-toggle-handle {
  transform: translateX(0);
  background-color: var(--pp-clr-primary);
}

.pp-toggle-checkmark {
  color: var(--pp-clr-secondary);
  width: 15.86px;
  height: 17px;
}

.pp-toggle-label {
  color: var(--pp-clr-text-main);
  font-weight: 600;
  cursor: pointer;
}
.pp-toggle-label p {
  font-weight: 400;
}
.pp-toggle-description {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);

  line-height: 1.5;
}

/* Hover States */
.pp-toggle-wrapper:hover .pp-toggle-switch:not(.pp-toggle-switch-disabled) {
  transform: scale(1.02);
}

.pp-toggle-wrapper:hover .pp-toggle-switch-off:not(.pp-toggle-switch-disabled) {
  background-color: var(--pp-clr-border-dark);
}

/* Focus States */
.pp-toggle-input:focus + .pp-toggle-switch {
  outline: none;
}

/* Responsive */
@media (max-width: 768px) {
  .pp-toggle-description {
    margin-left: var(--pp-sp-48);
  }
}

@media (max-width: 480px) {
  .pp-toggle-description {
    margin-left: var(--pp-sp-40);
  }
  .pp-toggle-switch {
    
    width: 60px;
    height: 26px;
    
  
  }
.pp-toggle-handle {

  left: 2px;
  top: 3px;
  width: 20px;
  transform: translateY(-50%);
  height: 20px;

}
.pp-toggle-switch-on .pp-toggle-handle {
  transform: translateX(35px);
  background-color: var(--pp-clr-primary);
}
}
