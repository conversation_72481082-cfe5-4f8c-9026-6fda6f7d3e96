// import { useState, useEffect } from "react";
// import { useAuth } from "../redux/useAuth";
// import { vendorApi, userApi } from "../redux/apiUtils";
// import CustomerProfile from "./CustomerProfile";
// import { toast } from "react-toastify";

// export default function MyAccount() {
//   const { user, getProfile } = useAuth();
//   const [loading, setLoading] = useState(false);
//   const [profileData, setProfileData] = useState(null);

//   useEffect(() => {
//     if (user) {
//       initializeProfileData();
//     }
//   }, [user]);

//   const initializeProfileData = async () => {
//     try {
//       setLoading(true);
//       let data = {};
      
//       if (user.role === "vendor") {
//         // Get vendor profile from backend
//         try {
//           const response = await vendorApi.getProfile();
//           data = {
//             firstName: response.data.user.firstName || "",
//             lastName: response.data.user.lastName || "",
//             email: response.data.user.email || "",
//             phone: response.data.user.phone || "",
//             storeName: response.data.vendorProfile?.businessName || "",
//             profileImage: response.data.user.profileImage || "/assets/images/default.png",
//           };
//         } catch (error) {
//           // Fallback to user data if vendor profile fails
//           data = {
//             firstName: user.firstName || "",
//             lastName: user.lastName || "",
//             email: user.email || "",
//             phone: user.phone || "",
//             storeName: "",
//             profileImage: user.profileImage || "/assets/images/default.png",
//           };
//         }
//       } else {
//         // Get customer profile 
//         data = {
//           firstName: user.firstName || "",
//           lastName: user.lastName || "",
//           email: user.email || "",
//           phone: user.phone || "",
//           profileImage: user.profileImage || "https://via.placeholder.com/150x150/e5e5e5/666666?text=Profile",
//         };
//       }
      
//       setProfileData(data);
//     } catch (error) {
//       console.error("Error loading profile:", error);
//       toast.error("Failed to load profile data");
//       // Set fallback data from user
//       setProfileData({
//         firstName: user?.firstName || "",
//         lastName: user?.lastName || "",
//         email: user?.email || "",
//         phone: user?.phone || "",
//         storeName: user?.role === "vendor" ? "" : undefined,
//         profileImage: user?.profileImage || "https://via.placeholder.com/150x150/e5e5e5/666666?text=Profile",
//       });
//     } finally {
//       setLoading(false);
//     }
//   };

//   const handleProfileUpdate = async (updatedData) => {
//     try {
//       setLoading(true);

//       if (user.role === "vendor") {
//         // Update user profile
//         await userApi.updateProfile({
//           firstName: updatedData.firstName,
//           lastName: updatedData.lastName,
//           phone: updatedData.phone,
//           profileImage: updatedData.profileImage
//         });

//         // Update vendor profile if storeName changed
//         if (updatedData.storeName) {
//           try {
//             await vendorApi.updateProfile({
//               businessName: updatedData.storeName
//             });
//           } catch (error) {
//             console.warn("Could not update vendor business name:", error);
//           }
//         }
//       } else {
//         // Update customer profile
//         await userApi.updateProfile({
//           firstName: updatedData.firstName,
//           lastName: updatedData.lastName,
//           phone: updatedData.phone,
//           profileImage: updatedData.profileImage
//         });
//       }

//       // Refresh user profile in auth state using Redux action
//       await getProfile();

//       toast.success("Profile updated successfully!");
//       setProfileData(updatedData);
//     } catch (error) {
//       console.error("Error updating profile:", error);
//       toast.error("Failed to update profile");
//     } finally {
//       setLoading(false);
//     }
//   };

//   if (!user) {
//     return (
//       <div className="pp-max-container my-account-page">
//         <div className="my-account-empty-state">
//           <h2 className="my-account-title">My Account</h2>
//           <p className="my-account-message">Please log in to view your account information.</p>
//         </div>
//       </div>
//     );
//   }

//   if (loading && !profileData) {
//     return (
//       <div className="pp-max-container my-account-page">
//         <div className="my-account-loading-state">
//           <h2 className="my-account-title">My Account</h2>
//           <p className="my-account-message">Loading your profile...</p>
//         </div>
//       </div>
//     );
//   }

//   return (
//     <div className="pp-max-container my-account-page">
//       <div className="my-account-header">
//         <h1 className="my-account-title">My Account</h1>
//         <p className="my-account-subtitle">
//           Manage your {user.role === 'vendor' ? 'business' : 'personal'} profile and account settings
//         </p>
//       </div>
//       <CustomerProfile
//         type={user.role}
//         initialData={profileData}
//         onSubmit={handleProfileUpdate}
//         loading={loading}
//       />
//     </div>
//   );
// }
