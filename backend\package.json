{"name": "party-pipeline-backend", "version": "1.0.0", "description": "Backend API for Party Pipeline - Event Marketplace Platform", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "seed:dummy": "node src/scripts/seed_dummy_data.js"}, "keywords": ["event", "marketplace", "party", "booking", "stripe"], "author": "Party Pipeline Team", "license": "MIT", "dependencies": {"@aws-sdk/client-s3": "^3.884.0", "@aws-sdk/lib-storage": "^3.884.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "moment-timezone": "^0.5.45", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "node-cron": "^4.2.1", "nodemailer": "^6.9.7", "redis": "^4.6.11", "slugify": "^1.6.6", "socket.io": "^4.7.4", "stripe": "^14.8.0", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/jest": "^29.5.8", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}