const socketIo = require('socket.io');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const MessageSocketHandler = require('./messageSocket');
const { apiLogger } = require('../utils/logger');

/**
 * Socket.IO Configuration and Setup
 * Handles real-time functionality for the application
 */
class SocketManager {
  constructor(server) {
    this.server = server;
    this.io = null;
    this.messageHandler = null;
  }

  /**
   * Initialize Socket.IO server
   */
  initialize() {
    this.io = socketIo(this.server, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:3000",
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling'],
      pingTimeout: 60000,
      pingInterval: 25000
    });

    // Authentication middleware
    this.io.use(this.authenticateSocket.bind(this));

    // Initialize message handler
    this.messageHandler = new MessageSocketHandler(this.io);
    this.messageHandler.initialize();

    // Global connection handling
    this.setupGlobalHandlers();

    apiLogger.info('Socket.IO server initialized');

    return this.io;
  }

  /**
   * Authenticate socket connections using JWT
   */
  async authenticateSocket(socket, next) {
    try {
      const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');

      if (!token) {
        apiLogger.warn('Socket connection attempted without token', {
          socketId: socket.id,
          ip: socket.handshake.address
        });
        return next(new Error('Authentication token required'));
      }

      // Verify JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      // Get user from database
      const user = await User.findById(decoded.id).select('-password -refreshTokens');
      
      if (!user || !user.isActive) {
        apiLogger.warn('Socket authentication failed - invalid user', {
          socketId: socket.id,
          userId: decoded.id
        });
        return next(new Error('Invalid user'));
      }

      // Attach user to socket
      socket.user = user;
      
      apiLogger.info('Socket authenticated successfully', {
        socketId: socket.id,
        userId: user._id,
        userRole: user.role
      });

      next();

    } catch (error) {
      apiLogger.error('Socket authentication error', {
        socketId: socket.id,
        error: error.message
      });
      next(new Error('Authentication failed'));
    }
  }

  /**
   * Setup global socket event handlers
   */
  setupGlobalHandlers() {
    this.io.on('connection', (socket) => {
      // Handle general events
      this.handleHeartbeat(socket);
      this.handleUserActivity(socket);
      this.handleErrorReporting(socket);
      
      // Handle admin events
      if (socket.user.role === 'admin') {
        this.handleAdminEvents(socket);
      }

      // Handle vendor-specific events
      if (socket.user.role === 'vendor') {
        this.handleVendorEvents(socket);
      }

      // Handle booking-related events
      this.handleBookingEvents(socket);
    });
  }

  /**
   * Handle heartbeat/ping events
   */
  handleHeartbeat(socket) {
    socket.on('ping', () => {
      socket.emit('pong', { timestamp: new Date() });
    });

    socket.on('heartbeat', (data) => {
      socket.emit('heartbeat_ack', {
        timestamp: new Date(),
        serverTime: Date.now(),
        clientTime: data?.timestamp
      });
    });
  }

  /**
   * Handle user activity tracking
   */
  handleUserActivity(socket) {
    socket.on('user_activity', (data) => {
      const { page, action, timestamp } = data;
      
      // Track user activity for analytics
      apiLogger.debug('User activity tracked', {
        userId: socket.user.id,
        socketId: socket.id,
        page,
        action,
        timestamp
      });

      // Could store in analytics database or send to analytics service
    });

    socket.on('page_view', (data) => {
      const { page, referrer, timestamp } = data;
      
      apiLogger.debug('Page view tracked', {
        userId: socket.user.id,
        page,
        referrer,
        timestamp
      });
    });
  }

  /**
   * Handle error reporting from client
   */
  handleErrorReporting(socket) {
    socket.on('client_error', (data) => {
      const { error, context, timestamp, userAgent } = data;
      
      apiLogger.error('Client-side error reported', {
        userId: socket.user.id,
        socketId: socket.id,
        error,
        context,
        timestamp,
        userAgent
      });

      // Could send to error tracking service like Sentry
    });
  }

  /**
   * Handle admin-specific events
   */
  handleAdminEvents(socket) {
    // Join admin room for broadcasts
    socket.join('admin_room');

    socket.on('admin_broadcast', (data) => {
      const { message, type, targetRole } = data;
      
      if (targetRole) {
        // Broadcast to specific role
        this.io.emit('admin_announcement', {
          message,
          type,
          from: 'System Administrator',
          timestamp: new Date(),
          targetRole
        });
      } else {
        // Broadcast to all users
        this.io.emit('admin_announcement', {
          message,
          type,
          from: 'System Administrator',
          timestamp: new Date()
        });
      }

      apiLogger.info('Admin broadcast sent', {
        adminId: socket.user.id,
        message,
        type,
        targetRole
      });
    });

    socket.on('get_real_time_stats', () => {
      const stats = {
        connectedUsers: this.messageHandler.getConnectedUsersCount(),
        connectedSockets: this.io.engine.clientsCount,
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        timestamp: new Date()
      };

      socket.emit('real_time_stats', stats);
    });
  }

  /**
   * Handle vendor-specific events
   */
  handleVendorEvents(socket) {
    // Join vendor room for vendor-specific broadcasts
    socket.join('vendor_room');

    socket.on('vendor_availability_update', async (data) => {
      const { serviceId, available, reason } = data;
      
      // Broadcast availability change to interested customers
      socket.broadcast.emit('service_availability_changed', {
        serviceId,
        available,
        reason,
        vendorId: socket.user.id,
        timestamp: new Date()
      });

      apiLogger.info('Vendor availability updated', {
        vendorId: socket.user.id,
        serviceId,
        available,
        reason
      });
    });
  }

  /**
   * Handle booking-related events
   */
  handleBookingEvents(socket) {
    socket.on('booking_status_update', (data) => {
      const { bookingId, status, reason } = data;
      
      // This would typically be called from booking controller
      // Here for demonstration of real-time booking updates
      this.emitBookingUpdate(bookingId, status, reason, socket.user.id);
    });

    socket.on('join_booking_room', (data) => {
      const { bookingId } = data;
      socket.join(`booking_${bookingId}`);
      
      socket.emit('joined_booking_room', {
        bookingId,
        timestamp: new Date()
      });
    });

    socket.on('leave_booking_room', (data) => {
      const { bookingId } = data;
      socket.leave(`booking_${bookingId}`);
      
      socket.emit('left_booking_room', {
        bookingId,
        timestamp: new Date()
      });
    });
  }

  /**
   * Emit booking status update to relevant parties
   */
  emitBookingUpdate(bookingId, status, reason, updatedBy) {
    this.io.to(`booking_${bookingId}`).emit('booking_updated', {
      bookingId,
      status,
      reason,
      updatedBy,
      timestamp: new Date()
    });

    // If message handler exists, send booking notification
    if (this.messageHandler) {
      // This would be called with proper booking data in real implementation
      // this.messageHandler.sendBookingNotification(bookingData, notificationType);
    }
  }

  /**
   * Send notification to specific user
   */
  sendNotificationToUser(userId, notification) {
    if (this.messageHandler) {
      return this.messageHandler.sendNotificationToUser(userId, notification);
    }
    return false;
  }

  /**
   * Broadcast to all users
   */
  broadcastToAll(event, data) {
    this.io.emit(event, {
      ...data,
      timestamp: new Date()
    });
  }

  /**
   * Broadcast to users with specific role
   */
  broadcastToRole(role, event, data) {
    this.io.to(`${role}_room`).emit(event, {
      ...data,
      timestamp: new Date()
    });
  }

  /**
   * Get server statistics
   */
  getStats() {
    return {
      connectedSockets: this.io.engine.clientsCount,
      connectedUsers: this.messageHandler?.getConnectedUsersCount() || 0,
      rooms: this.io.sockets.adapter.rooms.size,
      uptime: process.uptime(),
      timestamp: new Date()
    };
  }

  /**
   * Gracefully shutdown socket server
   */
  shutdown() {
    if (this.io) {
      apiLogger.info('Shutting down Socket.IO server');
      
      // Notify all connected clients
      this.io.emit('server_shutdown', {
        message: 'Server is shutting down for maintenance',
        timestamp: new Date()
      });

      // Close all connections
      this.io.close();
    }
  }
}

module.exports = SocketManager;
