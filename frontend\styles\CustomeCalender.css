.datepicker-container {
  display: inline-block;
  position: relative;
}

.input-wrapper {
  width: fit-content;
}

.datepicker-input {
  padding: 12px 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  width: fit-content;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  position: relative;
  background: white !important;
}
.datepicker-icon {
  cursor: pointer;
  color: #bababa;
}

.calendar {
  width: 320px;
  padding: 16px;
  background: #fff;
  border: 1px solid #eee;
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: absolute;
  top: 105%;
  z-index: 999;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.calendar-title {
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  gap: 6px;
  align-items: center;
}
.calendar-title select,
.calendar-title input {
  font-size: 14px;
  padding: 2px 4px;
  border-radius: 4px;
  border: 1px solid #ccc;
}

.nav-btn {
  background: none;
  border: 1px solid #d2d2d2;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: background 0.2s;
}
.nav-btn:hover {
  background: #f1f1f1;
}

.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 8px;
}
.weekday {
  padding: 6px 0;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
  font-size: 14px;
}

.calendar-day {
  height: 40px;
  width: 40px;
  border: none;
  background: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}
.calendar-day:hover {
  background: #f1f1f1;
}
.calendar-day.selected {
  background: #f4f4f4;
  color: #000000;
  font-weight: bold;
}
.calendar-day.empty {
  cursor: default;
  background: none;
}
