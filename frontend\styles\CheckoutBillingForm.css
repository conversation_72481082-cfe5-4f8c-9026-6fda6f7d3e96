.pp-checkout-billing .booking-details-modal__overlay {
  position: fixed;
  z-index: var(--pp-z-index-modal);
  inset: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: center;
  align-items: center;
}
.pp-formaddress-row{
  display: grid
;
    grid-template-columns:1fr 1fr;
    gap: var(--pp-sp-16);
}
.pp-form-name-row{
  display: grid;
    grid-template-columns:1fr 1fr;
    gap: var(--pp-sp-16);
}

.pp-checkout-billing .booking-details-modal {
  background: var(--pp-clr-primary);
  border-radius: var(--pp-bor-rad-12);
  box-shadow: var(--pp-shdw-lg);
  padding: var(--pp-sp-40) var(--pp-sp-32) var(--pp-sp-32) var(--pp-sp-32);
  max-width: 740px;
  width: 100%;
  font-family: var(--pp-font-Metro-Sans);
  color: var(--pp-clr-text-main);
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-32);
  min-width: 340px;
}

.pp-checkout-billing .booking-details-modal__header {
  display: flex;
  justify-content: space-between;
  align-items: start;
  gap: var(--pp-sp-20);
}

.pp-checkout-billing .booking-details-modal__title {
  font-family: var(--pp-font-Playfair-Display);
  font-size: var(--pp-font-heading4);
  color: var(--pp-clr-text-main);
  font-weight: 700;
  margin-bottom: var(--pp-sp-8);
  margin-top: 0;
}

.pp-checkout-billing .booking-details-modal__status {
  display: inline-block;
  font-size: var(--pp-font-base2-font);
  color: var(--pp-clr-feature-icon-green);
  margin-bottom: var(--pp-sp-4);
  margin-top: var(--pp-sp-8);
}

.pp-checkout-billing .booking-details-modal__meta {
  font-size: var(--pp-font-extra-small);
}

.pp-checkout-billing .booking-details-modal__meta a {
  color: var(--pp-clr-blue-txt);
  text-decoration: underline;
  font-weight: 500;
}

.pp-checkout-billing .booking-details-modal__action-btn {
  background: var(--pp-clr-secondary);
  color: var(--pp-clr-primary);
  border: none;
  border-radius: var(--pp-bor-rad-24);
  padding: var(--pp-sp-12) var(--pp-sp-24);
  font-size: var(--pp-font-small-font);
  font-weight: 600;
  cursor: pointer;
  align-self: flex-start;
  box-shadow: var(--pp-shdw-sm);
  transition: background 200ms;
}
.pp-checkout-billing{
  display: grid;
  width: 70%;
  gap: var(--pp-sp-16);
}
.pp-checkout-billing__title{
  font-size: var(--pp-font-heading5);
  font-weight: 600;
}
.pp-checkout-billing .booking-details-modal__action-btn:hover {
  background: var(--pp-clr-feature-icon-green);
  color: var(--pp-clr-secondary);
}

.pp-checkout-billing .booking-details-modal__details {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-12);
}

.pp-checkout-billing .booking-details-modal__details-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--pp-sp-40);
  align-items: flex-start;
  margin-top: var(--pp-sp-12);
}

.pp-checkout-billing .booking-details-modal__details-grid > div {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-10);
}

.pp-checkout-billing .booking-details-modal__artists {
  display: flex;
  gap: var(--pp-sp-16);
  justify-content: flex-end;
}

.pp-checkout-billing .booking-details-modal__artist {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--pp-sp-4);
}

.pp-checkout-billing .booking-details-modal__artist img {
  width: var(--pp-sp-40);
  height: var(--pp-sp-40);
  border-radius: var(--pp-bor-rad-round);
  object-fit: cover;
  background: var(--pp-clr-border-light);
  border: 2px solid var(--pp-clr-border);
}

.pp-checkout-billing .booking-details-modal__location {
  margin-top: var(--pp-sp-16);
}

.pp-checkout-billing .booking-details-modal__location h3 {
  margin: 0 0 var(--pp-sp-8) 0;
  font-size: var(--pp-font-heading6);
  font-weight: 500;
  color: var(--pp-clr-text-main);
}

.pp-checkout-billing .booking-details-modal__map {
  width: 100%;
  height: 220px;
  background: var(--pp-clr-border-light);
  border-radius: var(--pp-bor-rad-8);
  overflow: hidden;
  margin-top: var(--pp-sp-8);
  border: 1px solid var(--pp-clr-border-light);
}

.pp-checkout-billing .booking-details-modal__payment {
  margin-top: var(--pp-sp-24);
}

.pp-checkout-billing .booking-details-modal__payment h3 {
  margin: 0 0 var(--pp-sp-8) 0;
  font-size: var(--pp-font-heading6);
  font-weight: 500;
  color: var(--pp-clr-text-main);
}

.pp-checkout-billing .booking-details-modal__table {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  background: var(--pp-clr-guarantee-section-bg);
  border-radius: var(--pp-bor-rad-8);
  box-shadow: var(--pp-shdw-md);
  margin-bottom: var(--pp-sp-4);
}

.pp-checkout-billing .booking-details-modal__table-row {
  display: contents;
}

.pp-checkout-billing .booking-details-modal__table-header span,
.pp-checkout-billing .booking-details-modal__table-footer span {
  font-weight: 500;
}

.pp-checkout-billing .booking-details-modal__table-header {
  background: var(--pp-clr-guarantee-badge-bg);
  color: var(--pp-clr-text-main);
}

.pp-checkout-billing .booking-details-modal__table-footer {
  background: var(--pp-clr-bg);
  color: var(--pp-clr-secondary);
}

.pp-checkout-billing .booking-details-modal__table-header,
.pp-checkout-billing .booking-details-modal__table-row,
.pp-checkout-billing .booking-details-modal__table-footer {
  font-size: var(--pp-font-extra-small);
}

.pp-checkout-billing .booking-details-modal__refund {
  margin-top: var(--pp-sp-4);
  font-size: var(--pp-font-extra-small);
}

.pp-checkout-billing .booking-details-modal__refund a {
  color: var(--pp-clr-blue-txt);
  font-weight: 500;
  text-decoration: underline;
}

.pp-checkout-billing .booking-details-modal__history {
  margin-top: var(--pp-sp-24);
  font-size: var(--pp-font-small-font);
  color: var(--pp-clr-text-gray);
}

.pp-checkout-billing .booking-details-modal__history h3 {
  margin: 0 0 var(--pp-sp-4) 0;
  font-size: var(--pp-font-heading6);
  font-weight: 500;
  color: var(--pp-clr-text-main);
}
.pp-checkout-billing__actions{
  display: flex;
  gap: var(--pp-sp-16);
  justify-content: flex-end;
}

@media (max-width: 900px) {
  .pp-checkout-billing .booking-details-modal {
    max-width: 98vw;
    padding: var(--pp-sp-24) var(--pp-sp-12);
  }
  .pp-checkout-billing .booking-details-modal__details-grid {
    grid-template-columns: 1fr;
    gap: var(--pp-sp-24);
  }
  .pp-form-divider{
    margin: 0;
  }
  .pp-checkout-billing{
    width: 100%;
  }
}

@media (max-width: 550px) {
  .pp-checkout-billing .booking-details-modal {
    border-radius: var(--pp-bor-rad-8);
    padding: var(--pp-sp-16);
    min-width: unset;
  }
  .pp-checkout-billing .booking-details-modal__map {
    height: 180px;
  }
  .pp-formaddress-row{
    grid-template-columns: 1fr;
    gap: var(--pp-sp-8);
  }
  .pp-checkout-billing{
    gap: var(--pp-sp-8);
  }
  .pp-form-name-row{
    gap: var(--pp-sp-8);
  }
}
@media (max-width: 350px) {
  .pp-form-row{
    flex-direction: column;
  }
  .pp-form-name-row{
    flex-direction: column;
  }
}