import React, { useEffect, useState } from "react";
import CustomSelect from "../../components/CustomeSelect";
import "../../styles/CheckoutFormSteps.css";

const stepsData = [
  { num: 1, label: "Billing Info" },
  { num: 2, label: "Event Info" },
  { num: 3, label: "Payment" },
];

export default function CheckoutFormSteps({ onStepChange, activeStep = 1 }) {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkSize = () => {
      setIsMobile(window.innerWidth <= 480);
    };
    checkSize();
    window.addEventListener("resize", checkSize);
    return () => window.removeEventListener("resize", checkSize);
  }, []);

  return (
    <nav className="pp-checkout-page__steps" aria-label="Checkout progress">
      {isMobile ? (
        <CustomSelect
          className="pp-step-dropdown"
          value={activeStep}
          onChange={(val) => onStepChange && onStepChange(Number(val))}
          options={stepsData.map((s) => ({ label: `${s.num}. ${s.label}` , value: s.num }))}
        />
      ) : (
        <ol className="pp-steps" role="list">
          {stepsData.map((step) => (
            <li
              key={step.num}
              className={`pp-step ${
                activeStep === step.num ? "pp-step--active" : ""
              }`}
              onClick={() => onStepChange && onStepChange(step.num)}
              style={{ cursor: "pointer" }}
            >
              <span className="pp-step__index">{step.num}.</span>
              <span className="pp-step__label">{step.label}</span>
            </li>
          ))}
        </ol>
      )}
    </nav>
  );
}
