import { Suspense, lazy, useEffect } from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  useLocation,
  useNavigate,
} from "react-router-dom";
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Navbar from "../components/Navbar";
import Footer from "../components/Footer";
import ProtectedRoute from "../components/ProtectedRoute";
import PublicRoute from "../components/PublicRoute";
import { useAuth } from "../redux/useAuth";
import CreateServiceModal from "../components/CreateServiceModal";
import ScrollToTopButton from "../components/ScrollToTopButton";
import { useLenis, scrollToTop } from "./hooks/useLenis";

// MessageRedirect component for role-based message routing
const MessageRedirect = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (user) {
      if (user.role === 'vendor') {
        navigate('/vendor/messages');
      } else if (user.role === 'customer') {
        navigate('/customer-messages');
      } else {
        // Fallback for unknown roles
        navigate('/');
      }
    }
  }, [user, navigate]);

  return (
    <div style={{ padding: "2rem", textAlign: "center" }}>
      Redirecting to messages...
    </div>
  );
};

// Route-based code splitting
const HomePage = lazy(() => import("../pages/HomePage"));
const LoginPage = lazy(() => import("../pages/LoginPage"));
const SignupPage = lazy(() => import("../pages/SignupPage"));
const ForgotPassword = lazy(() => import("../pages/ForgotPassword"));
const ResetPassword = lazy(() => import("../pages/ResetPassword"));
const BrowseServices = lazy(() => import("../pages/BrowseServices"));
const ServiceDetails = lazy(() => import("../pages/ServiceDetails"));
const VendorDashboard = lazy(() => import("../pages/VendorDashboard"));
const BookingsPage = lazy(() => import("../pages/VendorBookingsPage"));
const VendorLayout = lazy(() => import("../Layouts/VendorLayout"));
const MessagesPage = lazy(() => import("../pages/VendorMessagesPage"));
const ServicesPage = lazy(() => import("../pages/VendorServicesPage"));
const ReportsPage = lazy(() => import("../pages/VendorReportsPage"));
const ReviewsPage = lazy(() => import("../pages/VendorReviewsPage"));
const TravelPage = lazy(() => import("../pages/VendorTravelPage"));
const BalancePage = lazy(() => import("../pages/VendorBalancePage"));
const VendorOnboarding = lazy(() => import("../pages/VendorOnboarding"));
const ContactPage = lazy(() => import("../pages/ContactPage"));
const CustomerMessages = lazy(() => import("../pages/CustomerMessages"));
const CustomerEvent = lazy(() => import("../pages/CustomerEvent"));
const CustomerLayout = lazy(() => import("../Layouts/CustomerLayout"));
const CustomerAddresses = lazy(() => import("../pages/CustomerAddresses"));
const CustomerSettings = lazy(() => import("../components/CustomerSettings"));
const CustomerPaymentMethods = lazy(() =>
  import("../pages/CustomerPaymentMethods")
);
const CheckoutPage = lazy(() => import("../pages/CheckoutPage"));
const CustomerOrders = lazy(() => import("../pages/CustomerOrders"));
const CustomerProfile = lazy(() => import("../pages/CustomerProfile"));

const CartPage = lazy(() => import("../pages/CartPage"));
const VendorStore = lazy(() => import("../pages/VendorStore"));

function AppContent() {
  const location = useLocation();
  const { user } = useAuth();

  const hideNavAndFooter = [
    "/login",
    "/signup",
    "/forgot-password",
    "/reset-password",
  ].includes(location.pathname);

  // Initialize Lenis smooth scrolling
  // useLenis();

  // Scroll to top on route change
  useEffect(() => {
    scrollToTop();
  }, [location.pathname]);

  return (
    <>
      {!hideNavAndFooter && <Navbar role={user?.role || "guest"} />}
      <main>
        <Suspense
          fallback={
            <div style={{ padding: "2rem", textAlign: "center" }}>
              Loading...
            </div>
          }
        >
          <Routes>
            {/* common pages */}
            <Route path="/" element={<HomePage />} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/signup" element={<SignupPage />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/reset-password" element={<ResetPassword />} />
            <Route path="/browse-services" element={<BrowseServices />} />
            <Route path="/services/:id" element={<ServiceDetails />} />
            <Route path="/contact" element={<ContactPage />} />
            <Route path="/cart" element={<CartPage />} />
            <Route path="/checkout" element={<CheckoutPage />} />

            {/* customer routes */}
            <Route path="/customer-messages" element={<CustomerMessages />} />
            <Route path="/customer-event" element={<CustomerEvent />} />
            <Route path="/my-events" element={<CustomerEvent />} /> {/* Alias for customer event */}
            <Route path="/messages" element={<ProtectedRoute><MessageRedirect /></ProtectedRoute>} /> {/* Role-based redirect */}
            <Route element={<CustomerLayout />}>
              <Route
                path="/customer-account-profile"
                element={<CustomerProfile />}
              />
              <Route
                path="/customer-payment-methods"
                element={<CustomerPaymentMethods />}
              />
              <Route path="/customer-orders" element={<CustomerOrders />} />
              <Route
                path="/customer-addresses"
                element={<CustomerAddresses />}
              />
              <Route path="/customer-settings" element={<CustomerSettings />} />
            </Route>
            <Route path="/checkout" element={<CheckoutPage />} />

            {/* shared profile routes (protected) */}
            <Route path="/customer-account-profile" element={<ProtectedRoute><CustomerProfile /></ProtectedRoute>} />

            {/* vendor dashboard page routes */}
            <Route path="/vendor" element={<VendorLayout />}>
              <Route index element={<VendorDashboard />} />
              <Route path="bookings" element={<BookingsPage />} />
              <Route path="messages" element={<MessagesPage />} />
              <Route path="services" element={<ServicesPage />} />
              <Route path="reports" element={<ReportsPage />} />
              <Route path="reviews" element={<ReviewsPage />} />
              <Route path="travel" element={<TravelPage />} />
              <Route path="balance" element={<BalancePage />} />
            </Route>
            <Route path="/vendorstore" element={<VendorStore />} />

            {/* vendoronbording routes */}
            <Route path="/vendor-onboarding" element={<VendorOnboarding />} />
          </Routes>
        </Suspense>
        <CreateServiceModal />
      </main>
      {!hideNavAndFooter && <Footer />}
      <ScrollToTopButton />
    </>
  );
}

function App() {
  return (
    <Router>
      <AppContent />
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </Router>
  );
}

export default App;
