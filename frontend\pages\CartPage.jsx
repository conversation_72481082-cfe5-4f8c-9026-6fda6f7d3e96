import React, { useState } from "react";
import CustomSelect from "../components/CustomeSelect";
import "../styles/CartPage.css"; // Correct relative path based on provided folder setup

import Entertainment from "../src/assets/images/Entertainment-category.png";
import Characters from "../src/assets/images/character-category.png";
import Rentals from "../src/assets/images/Rental-category.png";
import Food from "../src/assets/images/Food-category.png";

// MOCK DATA (should come from Redux)
const cartGroups = [
  {
    title: "Nero’s Heroes",
    items: [
      {
        img: Entertainment,
        name: "Storm Trooper",
        time: "Jun 22, 2023\n4:00pm–5:30pm",
        performers: 1,
        price: "$75/hr",
        duration: "1.5 hrs",
        amount: "$112.50",
      },
      {
        img: Characters,
        name: "Darth Vader",
        time: "Jun 22, 2023\n4:00pm–5:30pm",
        performers: 1,
        price: "$75/hr",
        duration: "1.5 hrs",
        amount: "$112.50",
      },
      {
        img: Rentals,
        name: "<PERSON> Skywalker",
        time: "Jun 22, 2023\n4:30pm–5:30pm",
        performers: 1,
        price: "$75/hr",
        duration: "1 hr",
        amount: "$75",
      },
    ],
    performerLabel: "# of Performers",
  },
  {
    title: "Twisted",
    items: [
      {
        img: Food,
        name: "Bumble Photo Kiosk",
        time: "Entertainer Package\nJun 22, 2023\n4:45pm–5:30pm",
        performers: 2,
        price: "$50",
        duration: "1.5 hrs",
        amount: "$150",
      },
    ],
    performerLabel: "# of Artists",
  },
];

const summary = {
  subtotal: "$375.00",
  travelFees: "$66.00",
  address: (
    <>
      1720 N 272 E<br />
      Apt 5E<br />
      Salt Lake City, UT 85515
    </>
  ),
  total: "$441.00",
  items: 4,
};

// Mobile quantity input with + and - buttons
function MobileQuantityControl({ value, onChange }) {
  return (
    <div className="cart-quantity-control">
      <button
        className="cart-quantity-btn"
        onClick={() => onChange(Math.max(1, value - 1))}
        type="button"
        aria-label="Decrease"
      >
        -
      </button>
      <span className="cart-quantity-val">{value}</span>
      <button
        className="cart-quantity-btn"
        onClick={() => onChange(value + 1)}
        type="button"
        aria-label="Increase"
      >
        +
      </button>
    </div>
  );
}

export default function CartPage() {
  // For demonstration, manage mobile quantities in local state;
  // In production, use Redux or context to sync globally.
  const [mobileQuantities, setMobileQuantities] = useState({
    "0-0": 1,
    "0-1": 1,
    "0-2": 1,
    "1-0": 2,
  });

  // State to manage dropdown values for performers
  const [performerCounts, setPerformerCounts] = useState({
    "0-0": 1,
    "0-1": 1,
    "0-2": 1,
    "1-0": 2,
  });

  // Simple window check for mobile viewport.
  // Could be improved with useEffect and resize listener for real app.
  const isMobile =
    typeof window !== "undefined" && window.innerWidth <= 414;

  return (
    <div className="cart-page-root">
      <div>
        <h1>Cart</h1>
   <div className="cartgridroot">
   <div className="cart-groups">
          {cartGroups.map((group, groupIdx) => (
            <div key={group.title}>
              <div className="cart-group-title">{group.title}</div>
              <div className="cart-table">
                {!isMobile && (
                  <div className="cart-table-header">
                    <div>Service</div>
                    <div>{group.performerLabel}</div>
                    <div>Unit Price</div>
                    <div>Duration</div>
                    <div>Amount</div>
                    <div></div>
                  </div>
                )}
                {group.items.map((item, itemIdx) => {
                  const key = `${groupIdx}-${itemIdx}`;
                  return !isMobile ? (
                    <div
                      className="cart-table-row"
                      key={item.name + itemIdx}
                    >
                      <div className="cart-service-cell">
                        <img
                          src={item.img}
                          alt={item.name}
                          className="cart-service-img"
                        />
                        <div className="cart-service-details">
                          <span className="cart-service-name">
                            {item.name}
                          </span>
                          <span className="cart-service-time">
                            {item.time.split("\n").map((line, i) => (
                              <span key={i}>
                                {line}
                                <br />
                              </span>
                            ))}
                          </span>
                        </div>
                      </div>
                      <CustomSelect
                        className="cart-table-select"
                        value={performerCounts[key] || item.performers}
                        onChange={(newValue) => {
                          setPerformerCounts(prev => ({
                            ...prev,
                            [key]: newValue
                          }));
                        }}
                        options={[1,2,3,4,5].map((v) => ({ label: String(v), value: v }))}
                      />
                      <div>{item.price}</div>
                      <div>{item.duration}</div>
                      <div>{item.amount}</div>
                      <button
                        className="cart-remove-btn"
                        aria-label="Remove"
                      >
                        ×
                      </button>
                    </div>
                  ) : (
                    <div
                      className="cart-table-row"
                      key={item.name + itemIdx}
                    >
                      {/* Section 1: Service Image */}
                      <img
                        src={item.img}
                        alt={item.name}
                        className="cart-service-img"
                      />
                      {/* Section 2: Details */}
                      <div className="cart-service-details">
                        <span className="cart-service-name">
                          {item.name}
                        </span>
                       <div>
                         <span className="cart-service-time">
                          {item.time.split("\n").map((line, i) => (
                            <span key={i}>
                              {line}
                              <br />
                            </span>
                          ))}
                        </span>
                      </div>
                      {/* Section 3: Price, Duration, Quantity, Amount and Remove Button */}
                      <div className="cart-mobile-info-row ">
                   <div className="grid gap-2">
                   <span className="cart-mobile-unit">{item.price}</span>
                      <div className="flex gap-2">
                      <span className="cart-mobile-duration">
                          {item.duration}
                        </span>
                        <span className="cart-mobile-amount">{item.amount}</span>
                      </div>
                        <MobileQuantityControl
                          value={mobileQuantities[key]}
                          onChange={(val) =>
                            setMobileQuantities((q) => ({ ...q, [key]: val }))
                          }
                        />
                        
                   </div>
                        <button
                          className="cart-remove-btn"
                          aria-label="Remove"
                        >
                          ×
                        </button>
                      </div>
                       </div>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      
      <div className="cart-summary-card">
        <div className="cart-summary-heading">
          Summary{" "}
          <span
            style={{
              color: "var(--pp-clr-text-gray)",
              fontFamily: "var(--pp-font-Metro-Sans)",
              fontWeight: 400,
              fontSize: "var(--pp-font-small-font)",
            }}
          >
            ({summary.items} Items)
          </span>
        </div>
        <div className="cart-summary-row">
          <span>Subtotal</span>
          <span>{summary.subtotal}</span>
        </div>
        <div className="cart-summary-row">
          <span>Travel Fees</span>
          <span>{summary.travelFees}</span>
        </div>
        <div className="cart-summary-address">{summary.address}</div>
        <div className="cart-total-row">
          <span>Total</span>
          <span>{summary.total}</span>
        </div>
        <button className="cart-checkout-btn">Proceed to Checkout</button>
      </div>
      </div>
   </div>
    </div>
  );
}
