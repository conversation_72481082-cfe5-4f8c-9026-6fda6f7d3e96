# Stripe Connect Custom UI Implementation Documentation

## Overview

This documentation covers the complete Stripe Connect implementation with custom UI for the Party Pipeline marketplace platform. The implementation uses Stripe's Custom Connect accounts to maintain full control over the user experience while handling vendor onboarding, payment processing, and account management.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [API Endpoints Documentation](#api-endpoints-documentation)
3. [Frontend Implementation](#frontend-implementation)
4. [Backend Services](#backend-services)
5. [Code Examples](#code-examples)
6. [Integration Guide](#integration-guide)
7. [Configuration](#configuration)
8. [Error Handling](#error-handling)
9. [Best Practices](#best-practices)

## Architecture Overview

### Technology Stack
- **Backend**: Node.js, Express.js, MongoDB
- **Frontend**: React, Redux Toolkit, Axios
- **Payment Processing**: Stripe Connect (Custom accounts)
- **File Upload**: AWS S3 (for verification documents)
- **Authentication**: JWT tokens

### Key Components
- **Custom Onboarding Flow**: Multi-step form with validation
- **Payment Processing**: Destination charges with platform fees
- **Webhook Handling**: Secure event processing
- **Document Verification**: Custom file upload system
- **Account Management**: Real-time status updates

## API Endpoints Documentation

### Vendor Onboarding Endpoints

#### POST `/api/vendors/onboarding`
**Purpose**: Start the vendor onboarding process with custom Stripe Connect account creation

**Request Body**:
```json
{
  "businessType": "individual|company",
  "personalInfo": {
    "firstName": "string",
    "lastName": "string",
    "email": "string",
    "phone": "string",
    "dateOfBirth": "YYYY-MM-DD",
    "ssnLast4": "string",
    "address": {
      "line1": "string",
      "city": "string",
      "state": "string",
      "postalCode": "string",
      "country": "US"
    }
  },
  "businessInfo": {
    "name": "string",
    "ein": "string",
    "address": {
      "line1": "string",
      "city": "string",
      "state": "string",
      "postalCode": "string",
      "country": "US"
    },
    "website": "string",
    "mcc": "string",
    "productDescription": "string"
  },
  "bankAccount": {
    "accountHolderName": "string",
    "routingNumber": "string",
    "accountNumber": "string",
    "accountType": "checking|savings"
  },
  "compliance": {
    "tosAgree": true,
    "infoAccurate": true,
    "ipAddress": "string",
    "userAgent": "string",
    "mcc": "string"
  }
}
```

**Response**:
```json
{
  "success": true,
  "message": "Onboarding completed successfully",
  "data": {
    "stripeAccountId": "acct_xxx",
    "onboardingCompleted": true,
    "verificationStatus": "pending|approved|rejected"
  }
}
```

#### GET `/api/vendors/onboarding/status`
**Purpose**: Get current onboarding status

**Response**:
```json
{
  "success": true,
  "data": {
    "onboardingCompleted": true,
    "payoutsEnabled": true,
    "verificationStatus": "approved",
    "requirements": {
      "currently_due": [],
      "eventually_due": [],
      "past_due": []
    }
  }
}
```

### Payment Processing Endpoints

#### POST `/api/payments/create-intent`
**Purpose**: Create payment intent for booking with platform fees

**Request Body**:
```json
{
  "bookingId": "string"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "clientSecret": "pi_xxx_secret_xxx",
    "paymentIntentId": "pi_xxx",
    "amount": 10000,
    "platformFee": 500,
    "vendorPayout": 9200
  }
}
```

#### POST `/api/payments/webhooks/stripe`
**Purpose**: Handle Stripe webhooks (public endpoint with signature verification)

**Headers**:
- `stripe-signature`: Webhook signature

**Supported Events**:
- `payment_intent.succeeded`
- `payment_intent.payment_failed`
- `account.updated`
- `charge.dispute.created`
- `transfer.created`

### Account Management Endpoints

#### POST `/api/vendors/stripe/update-account`
**Purpose**: Update Stripe account with additional information

**Request Body**:
```json
{
  "updateData": {
    "individual": {
      "first_name": "string",
      "last_name": "string",
      "phone": "string"
    },
    "business_profile": {
      "url": "string",
      "product_description": "string"
    }
  }
}
```

#### POST `/api/vendors/verification/submit-document`
**Purpose**: Submit verification documents

**Content-Type**: `multipart/form-data`

**Form Data**:
- `document`: File (PDF, JPG, PNG - max 10MB)
- `documentType`: "identity_document|address_verification|business_license"

### Financial Endpoints

#### GET `/api/vendors/balance`
**Purpose**: Get vendor account balance

**Response**:
```json
{
  "success": true,
  "data": {
    "available": 15000,
    "pending": 2500,
    "currency": "usd",
    "lastUpdated": "2024-01-15T10:30:00Z"
  }
}
```

#### POST `/api/vendors/payout`
**Purpose**: Request payout to bank account

**Request Body**:
```json
{
  "amount": 10000,
  "description": "Weekly payout"
}
```

## Frontend Implementation

### Custom Onboarding Component

The main onboarding component is located at `frontend/pages/VendorOnboarding.jsx` and implements a multi-step form:

**Key Features**:
- 5-step onboarding process
- Real-time validation
- Progress tracking
- Error handling
- Redux state management

**Steps**:
1. Personal Information
2. Identification
3. Business Information  
4. Bank Account Information
5. Compliance & Agreements

### Redux Integration

**Slice**: `frontend/redux/vendorSlice.js`

**Key Actions**:
- `startVendorOnboarding`: Submit onboarding data
- `getOnboardingStatus`: Check current status
- `updateStripeAccount`: Update account information

**State Structure**:
```javascript
{
  onboarding: {
    loading: false,
    error: null,
    status: {
      onboardingCompleted: false,
      payoutsEnabled: false,
      verificationStatus: 'pending'
    }
  }
}
```

### Payment Processing UI

**Component**: `frontend/components/checkout/PaymentForm.jsx`

**Features**:
- Multiple payment methods support
- Saved payment methods
- Real-time validation
- Stripe Elements integration (ready for implementation)

### Document Upload Component

**Component**: `frontend/pages/VendorVerification.jsx`

**Features**:
- Drag & drop file upload
- Progress tracking
- File type validation
- Error handling
- Real-time status updates

## Backend Services

### StripeService Class

**Location**: `backend/src/services/stripeService.js`

**Key Methods**:

#### `createVendorAccount(vendor, stripeConnectData)`
Creates a new Stripe Connect account with custom onboarding data.

#### `getVendorAccountStatus(stripeAccountId)`
Retrieves current account status and verification requirements.

#### `createPaymentIntent(paymentData)`
Creates payment intent with platform fees and destination charges.

#### `processBookingPayment(paymentData)`
High-level method for processing booking payments.

#### `handleWebhook(payload, signature)`
Processes Stripe webhooks with signature verification.

#### `calculateBookingFees(amount)`
Calculates platform and Stripe fees for transactions.

### Stripe Configuration

**Location**: `backend/src/config/stripe.js`

**Key Functions**:
- `createConnectAccount`: Creates Custom Connect accounts
- `createPaymentIntent`: Creates payment intents with application fees
- `verifyWebhookSignature`: Validates webhook signatures
- `calculatePlatformFee`: 5% platform fee calculation
- `calculateStripeFee`: Stripe processing fee calculation (2.9% + $0.30)

### Fee Structure

**Platform Fee**: 5% of transaction amount
**Stripe Fee**: 2.9% + $0.30 per transaction
**Vendor Payout**: Transaction amount - Platform fee - Stripe fee

## Code Examples

### Creating a Stripe Connect Account

```javascript
// Backend - StripeService.createVendorAccount
const stripeAccount = await StripeService.createVendorAccount(user, {
  businessType: 'individual',
  individual: {
    first_name: 'John',
    last_name: 'Doe',
    email: '<EMAIL>',
    phone: '+**********',
    ssn_last_4: '1234',
    address: {
      line1: '123 Main St',
      city: 'New York',
      state: 'NY',
      postal_code: '10001',
      country: 'US'
    }
  },
  compliance: {
    tosAgree: true,
    infoAccurate: true,
    ipAddress: '***********',
    userAgent: 'Mozilla/5.0...'
  }
});
```

### Processing Payment with Platform Fees

```javascript
// Backend - Payment processing
const paymentResult = await StripeService.processBookingPayment({
  booking: bookingData,
  paymentMethodId: 'pm_xxx',
  customerId: 'cus_xxx',
  vendorStripeAccountId: 'acct_xxx'
});
```

### Frontend Onboarding Submission

```javascript
// Frontend - Redux action
const onboardingData = {
  businessType: 'individual',
  personalInfo: {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    // ... other fields
  },
  compliance: {
    tosAgree: true,
    infoAccurate: true,
    ipAddress: await getClientIP(),
    userAgent: navigator.userAgent
  }
};

const result = await dispatch(startVendorOnboarding(onboardingData)).unwrap();
```

### Webhook Handling

```javascript
// Backend - Webhook processing
app.post('/api/payments/webhooks/stripe', 
  express.raw({ type: 'application/json' }),
  async (req, res) => {
    const sig = req.headers['stripe-signature'];
    
    try {
      const event = stripe.webhooks.constructEvent(
        req.body, 
        sig, 
        process.env.STRIPE_WEBHOOK_SECRET
      );
      
      switch (event.type) {
        case 'payment_intent.succeeded':
          await handlePaymentSuccess(event.data.object);
          break;
        case 'account.updated':
          await handleAccountUpdate(event.data.object);
          break;
      }
      
      res.json({ received: true });
    } catch (err) {
      res.status(400).send(`Webhook Error: ${err.message}`);
    }
  }
);
```

## Integration Guide

### Step 1: Environment Setup

**Backend Environment Variables**:
```env
STRIPE_SECRET_KEY=sk_test_xxx
STRIPE_PUBLISHABLE_KEY=pk_test_xxx
STRIPE_WEBHOOK_SECRET=whsec_xxx
FRONTEND_URL=http://localhost:3000
```

**Frontend Environment Variables**:
```env
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_xxx
VITE_API_BASE_URL=http://localhost:5000/api
```

### Step 2: Install Dependencies

**Backend**:
```bash
npm install stripe express mongoose multer aws-sdk
```

**Frontend**:
```bash
npm install @reduxjs/toolkit react-redux axios react-toastify
```

### Step 3: Database Models

Ensure your vendor model includes Stripe-related fields:
```javascript
const vendorSchema = new mongoose.Schema({
  stripeAccountId: String,
  stripeOnboardingCompleted: { type: Boolean, default: false },
  stripePayoutsEnabled: { type: Boolean, default: false },
  verification: {
    status: { type: String, enum: ['pending', 'approved', 'rejected'], default: 'pending' },
    submittedAt: Date,
    approvedAt: Date
  }
});
```

### Step 4: Webhook Configuration

1. Create webhook endpoint in Stripe Dashboard
2. Set endpoint URL: `https://yourdomain.com/api/payments/webhooks/stripe`
3. Select events: `payment_intent.succeeded`, `account.updated`, `charge.dispute.created`
4. Copy webhook secret to environment variables

### Step 5: Frontend Integration

1. Set up Redux store with vendor slice
2. Create onboarding form component
3. Implement payment processing UI
4. Add document upload functionality

### Step 6: Testing

1. Use Stripe test mode
2. Test onboarding flow with test data
3. Verify webhook handling
4. Test payment processing
5. Validate fee calculations

## Configuration

### Stripe Connect Settings

**Account Type**: Custom
**Capabilities**: `card_payments`, `transfers`
**Business Type**: Support both `individual` and `company`
**Country**: US (expandable to other countries)

### Platform Fee Configuration

Current implementation uses 5% platform fee, configurable in:
- `backend/src/config/stripe.js` - `calculatePlatformFee` function
- `backend/src/models/Transaction.js` - Fee calculation methods

### MCC (Merchant Category Code)

Default MCC: `7299` (Miscellaneous Personal Services)
Configurable per vendor based on business type.

## Error Handling

### Common Error Scenarios

1. **Incomplete Onboarding**: Handle missing required fields
2. **Payment Failures**: Retry logic and user notifications
3. **Webhook Failures**: Logging and monitoring
4. **Account Restrictions**: Guide users through resolution

### Error Response Format

```json
{
  "success": false,
  "message": "Error description",
  "error": {
    "code": "STRIPE_ERROR_CODE",
    "type": "validation_error",
    "details": {}
  }
}
```

## Best Practices

### Security
- Always verify webhook signatures
- Use HTTPS for all endpoints
- Implement rate limiting
- Sanitize user inputs
- Log security events

### Performance
- Cache account status when possible
- Use database indexes for Stripe account IDs
- Implement proper error boundaries
- Optimize file upload handling

### User Experience
- Provide clear progress indicators
- Show helpful error messages
- Auto-save form progress
- Real-time validation feedback

### Monitoring
- Log all Stripe API calls
- Monitor webhook processing
- Track onboarding completion rates
- Alert on payment failures

This implementation provides a complete custom Stripe Connect solution that maintains full control over the user experience while leveraging Stripe's powerful payment infrastructure.

## Advanced Features

### Account Status Monitoring

The system continuously monitors Stripe account status and updates local database accordingly:

```javascript
// Real-time status checking
const accountStatus = await StripeService.getVendorAccountStatus(stripeAccountId);

// Update local database
vendorProfile.stripeOnboardingCompleted = accountStatus.status.onboardingCompleted;
vendorProfile.stripePayoutsEnabled = accountStatus.status.payoutsEnabled;
```

### Verification Requirements Handling

Automatic handling of Stripe's verification requirements:

```javascript
// Process verification requirements from webhooks
static async processVerificationRequirements(vendorProfile, stripeAccount) {
  const requirements = stripeAccount.requirements;

  if (requirements.currently_due.length === 0 &&
      requirements.past_due.length === 0) {
    vendorProfile.verification.status = 'approved';
    vendorProfile.verification.approvedAt = new Date();
  } else if (requirements.past_due.length > 0) {
    vendorProfile.verification.status = 'rejected';
  }
}
```

### Custom Document Upload System

Secure document handling with AWS S3 integration:

```javascript
// Document upload with validation
router.post('/verification/submit-document',
  rateLimiting.moderate,
  uploadMultiple('document', {
    folder: 'verification-documents',
    allowedFormats: ['jpg', 'jpeg', 'png', 'pdf'],
    maxSize: 10 * 1024 * 1024, // 10MB
    maxFiles: 1
  }),
  VendorController.submitVerificationDocument
);
```

### Platform Fee Calculation

Flexible fee structure with detailed breakdown:

```javascript
// Fee calculation with transparency
static calculateBookingFees(amount) {
  const platformFee = calculatePlatformFee(amount); // 5%
  const stripeFee = calculateStripeFee(amount);     // 2.9% + $0.30
  const vendorPayout = amount - platformFee - stripeFee;

  return {
    subtotal: amount,
    platformFee,
    stripeFee,
    vendorPayout,
    total: amount
  };
}
```

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. Onboarding Failures

**Issue**: Account creation fails with missing information
**Solution**:
- Validate all required fields before submission
- Check MCC code validity
- Ensure proper address formatting

```javascript
// Validation example
const validateOnboardingData = (data) => {
  const required = ['firstName', 'lastName', 'email', 'ssnLast4'];
  const missing = required.filter(field => !data.personalInfo[field]);

  if (missing.length > 0) {
    throw new Error(`Missing required fields: ${missing.join(', ')}`);
  }
};
```

#### 2. Payment Intent Creation Errors

**Issue**: Payment intent fails due to account restrictions
**Solution**:
- Check account status before creating payment intents
- Ensure payouts are enabled
- Verify account capabilities

```javascript
// Pre-payment validation
const canAcceptPayments = accountStatus.status.payoutsEnabled &&
                         accountStatus.status.onboardingCompleted;

if (!canAcceptPayments) {
  throw new Error('Vendor account not ready for payments');
}
```

#### 3. Webhook Processing Failures

**Issue**: Webhooks fail signature verification
**Solution**:
- Verify webhook secret configuration
- Check endpoint URL configuration
- Ensure raw body parsing

```javascript
// Proper webhook setup
app.use('/api/payments/webhooks/stripe', express.raw({type: 'application/json'}));
```

### Debugging Tools

#### Logging Configuration

Comprehensive logging for troubleshooting:

```javascript
// Enhanced logging in stripeService.js
paymentLogger.info('Payment intent created', {
  bookingId: booking._id,
  customerId,
  vendorId: booking.vendorId._id,
  amount: booking.serviceDetails.totalCost,
  paymentIntentId: paymentIntent.id,
  platformFee: calculatePlatformFee(amount),
  timestamp: new Date().toISOString()
});
```

#### Test Data

Use Stripe's test data for development:

```javascript
// Test card numbers
const testCards = {
  visa: '****************',
  visaDebit: '****************',
  mastercard: '****************',
  amex: '***************',
  declined: '****************'
};

// Test bank accounts
const testBankAccount = {
  country: 'US',
  currency: 'usd',
  routing_number: '*********',
  account_number: '************'
};
```

## Migration Guide

### From Hosted Onboarding to Custom UI

If migrating from Stripe's hosted onboarding:

1. **Data Migration**: Export existing account data
2. **UI Replacement**: Replace hosted links with custom forms
3. **Webhook Updates**: Update webhook handling for custom flow
4. **Testing**: Thoroughly test the new flow

### Version Compatibility

**Stripe API Version**: `2023-10-16`
**Node.js**: `>=18.0.0`
**React**: `>=18.0.0`

## Performance Optimization

### Caching Strategies

```javascript
// Cache account status to reduce API calls
const CACHE_TTL = 300; // 5 minutes
const accountStatusCache = new Map();

static async getCachedAccountStatus(stripeAccountId) {
  const cacheKey = `account_status_${stripeAccountId}`;
  const cached = accountStatusCache.get(cacheKey);

  if (cached && Date.now() - cached.timestamp < CACHE_TTL * 1000) {
    return cached.data;
  }

  const status = await this.getVendorAccountStatus(stripeAccountId);
  accountStatusCache.set(cacheKey, {
    data: status,
    timestamp: Date.now()
  });

  return status;
}
```

### Database Optimization

```javascript
// Indexes for better performance
vendorSchema.index({ stripeAccountId: 1 });
vendorSchema.index({ 'verification.status': 1 });
vendorSchema.index({ stripeOnboardingCompleted: 1 });
```

## Security Considerations

### PCI Compliance

- Never store card data on your servers
- Use Stripe's secure tokenization
- Implement proper access controls
- Regular security audits

### Data Protection

```javascript
// Sensitive data handling
const sanitizeVendorData = (vendor) => {
  const { ssnLast4, bankAccount, ...safeData } = vendor;
  return safeData;
};

// Audit logging
const auditLog = {
  action: 'account_created',
  userId: vendor.id,
  timestamp: new Date(),
  ipAddress: req.ip,
  userAgent: req.headers['user-agent']
};
```

### Rate Limiting

```javascript
// API rate limiting
const rateLimiting = {
  strict: rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 requests per window
    message: 'Too many requests, please try again later'
  }),
  moderate: rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 20
  })
};
```

## Deployment Checklist

### Pre-deployment

- [ ] Environment variables configured
- [ ] Webhook endpoints set up in Stripe Dashboard
- [ ] SSL certificates installed
- [ ] Database indexes created
- [ ] Logging configured
- [ ] Error monitoring set up

### Post-deployment

- [ ] Test onboarding flow
- [ ] Verify webhook processing
- [ ] Test payment processing
- [ ] Monitor error rates
- [ ] Check performance metrics

### Monitoring

```javascript
// Health check endpoint
app.get('/health/stripe', async (req, res) => {
  try {
    // Test Stripe connectivity
    await stripe.accounts.list({ limit: 1 });

    res.json({
      status: 'healthy',
      stripe: 'connected',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'unhealthy',
      error: error.message
    });
  }
});
```

This comprehensive documentation provides everything needed to understand, implement, and maintain the custom Stripe Connect solution. The implementation prioritizes security, user experience, and maintainability while providing full control over the payment flow.
