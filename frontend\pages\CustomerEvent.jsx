import React, { useState } from "react";
import CustomSelect from "../components/CustomeSelect";
import EventCard from "../components/event/CustomerEventCard";
import "../styles/CustomerEvent.css";
import CreateEventModal from "../components/event/CustomerCreateEventModal";
import CustomerBookingDetailsModal from "../components/event/CustomerBookingDetailsModal";
import thor from "../src/assets/images/thor.svg";
import balloon from "../src/assets/images/balloon.svg";
import batman from "../src/assets/images/batman.svg";


// Demo data—populate as needed
const EVENTS = [
  {
    id: 1,
    title: "<PERSON><PERSON>'s Birthday",
    date: "2025-06-06",
    editUrl: "#",
    services: [
      {
        title: "Superhero Team",
        dateDetails: "Friday, June 06",
        time: "4:00pm – 6:00pm",
        vendor: "Nero's Heroes",
        price: "$164.00",
        images: [thor],
        highlight: false,
      }
    ]
  },
  {
    id: 2,
    title: "Mountainview Secondary School Festival",
    date: "2025-06-22",
    editUrl: "#",
    services: [
      {
        title: "Star Wars Characters",
        dateDetails: "Saturday, June 22",
        time: "4:00pm – 5:30pm",
        vendor: "Nero's Heroes",
        price: "$375.50",
        images: [balloon, batman],
        highlight: true
      },
      {
        title: "Superhero Team",
        dateDetails: "Friday, June 06",
        time: "4:00pm – 6:00pm",
        vendor: "Nero's Heroes",
        price: "$164.00",
        images: [thor],
        highlight: false,
      },
      {
        title: "Dynamic Duo",
        dateDetails: "Saturday, August 29",
        time: "2:30pm – 3:30pm",
        vendor: "Nero's Heroes",
        price: "$162.50",
        images: [batman, thor],
        highlight: false
      }
    ]
  },
  {
    id: 3,
    title: "Summer Days",
    date: "",
    editUrl: "#",
    services: [],
    noServicesText: "You haven't booked any services for this event yet.",
    bookNowUrl: "#",
  },
  {
    id: 4,
    title: "Sarah's Birthday",
    date: "2025-08-29",
    editUrl: "#",
    services: [
      {
        title: "Comic Book Heroes",
        dateDetails: "Saturday, August 29",
        time: "2:30pm – 3:30pm",
        vendor: "Nero's Heroes",
        price: "$162.50",
        images: [batman, thor, balloon],
        highlight: false
      }
    ],
    showDivider: false
  }
];

const CustomerEvent = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [filter, setFilter] = useState("Upcoming");
  const [selectedService, setSelectedService] = useState(null);
  const [selectedEvent, setSelectedEvent] = useState(null);

  const handleServiceClick = (service, event) => {
    setSelectedService(service);
    setSelectedEvent(event);
  };

  const handleCloseModal = () => {
    setSelectedService(null);
    setSelectedEvent(null);
  };

  return (
    <div className="pp-my-events-root pp-max-container">
      <div className="pp-my-events-header">
        <h2 className="pp-my-events-title">My Events</h2>
        <div className="pp-my-events-actions">
          <CustomSelect
            className="pp-my-events-filter"
            value={filter}
            onChange={setFilter}
            options={[
              { label: "Upcoming", value: "Upcoming" },
              { label: "Past", value: "Past" },
            ]}
          />
          <button
            className="pp-btn pp-btn-primary pp-my-events-create"
            onClick={() => setIsModalOpen(true)}
          >
            Create Event
          </button>
        </div>
      </div>
      <div className="pp-my-events-list">
        {EVENTS.map((event, i) => (
          <React.Fragment key={event.id || i}>
            <EventCard 
              event={event} 
              onServiceClick={handleServiceClick}
            />
            {(event.showDivider !== false && i < EVENTS.length - 1) && <div className="pp-my-events-divider" />}
          </React.Fragment>
        ))}
      </div>

      {/* Modal */}
      <CreateEventModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
      
      {selectedService && (
        <CustomerBookingDetailsModal
          open={true}
          booking={{
            ...selectedEvent,
            services: [{
              ...selectedService,
              dateDetails: selectedService.dateDetails || (selectedEvent.date && new Date(selectedEvent.date).toLocaleDateString("en-US", { 
                weekday: 'long', 
                month: 'long', 
                day: 'numeric' 
              }))
            }]
          }}
          onClose={handleCloseModal}
        />
      )}
    </div>
  );
};
export default CustomerEvent;
