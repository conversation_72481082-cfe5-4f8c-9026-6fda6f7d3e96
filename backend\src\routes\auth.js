const express = require('express');
const AuthController = require('../controllers/authController');
const { validateBody } = require('../middlewares/validation');
const { authRateLimit, passwordResetRateLimit, emailVerificationRateLimit } = require('../middlewares/rateLimiting');
const { authenticateToken } = require('../middlewares/auth');
const {
    registerSchema,
    loginSchema,
    forgotPasswordSchema,
    resetPasswordSchema,
    changePasswordSchema,
    emailVerificationSchema,
    resendVerificationSchema,
    refreshTokenSchema
} = require('../validators/authValidators');

const router = express.Router();

/**
 * @route   POST /api/auth/register
 * @desc    Register a new user
 * @access  Public
 */
router.post('/register',
    authRateLimit,
    validateBody(registerSchema),
    AuthController.register
);

/**
 * @route   POST /api/auth/login
 * @desc    Login user
 * @access  Public
 */
router.post('/login',
    authRateLimit,
    validateB<PERSON>(loginSchema),
    AuthController.login
);

/**
 * @route   POST /api/auth/logout
 * @desc    Logout user
 * @access  Private
 */
router.post('/logout',
    authenticateToken,
    AuthController.logout
);

/**
 * @route   POST /api/auth/refresh
 * @desc    Refresh access token
 * @access  Public (requires refresh token)
 */
router.post('/refresh',
    AuthController.refreshToken
);

/**
 * @route   POST /api/auth/forgot-password
 * @desc    Send password reset email
 * @access  Public
 */
router.post('/forgot-password',
    passwordResetRateLimit,
    validateBody(forgotPasswordSchema),
    AuthController.forgotPassword
);

/**
 * @route   POST /api/auth/reset-password
 * @desc    Reset password with token
 * @access  Public
 */
router.post('/reset-password',
    passwordResetRateLimit,
    validateBody(resetPasswordSchema),
    AuthController.resetPassword
);

/**
 * @route   POST /api/auth/verify-email
 * @desc    Verify email with token
 * @access  Public
 */
router.post('/verify-email',
    emailVerificationRateLimit,
    validateBody(emailVerificationSchema),
    AuthController.verifyEmail
);

/**
 * @route   POST /api/auth/resend-verification
 * @desc    Resend email verification
 * @access  Public
 */
router.post('/resend-verification',
    emailVerificationRateLimit,
    validateBody(resendVerificationSchema),
    AuthController.resendVerification
);

/**
 * @route   POST /api/auth/change-password
 * @desc    Change password for authenticated user
 * @access  Private
 */
router.post('/change-password',
    authenticateToken,
    passwordResetRateLimit,
    validateBody(changePasswordSchema),
    AuthController.changePassword
);

/**
 * @route   GET /api/auth/profile
 * @desc    Get current user profile
 * @access  Private
 */
router.get('/profile',
    authenticateToken,
    AuthController.getProfile
);

module.exports = router;
