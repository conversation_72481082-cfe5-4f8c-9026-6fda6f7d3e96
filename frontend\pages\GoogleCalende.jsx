// src/pages/GoogleCalendar.jsx
import React, { useEffect } from "react";

const CLIENT_ID = "YOUR_CLIENT_ID.apps.googleusercontent.com";
const API_KEY = "YOUR_API_KEY";
const SCOPES = "https://www.googleapis.com/auth/calendar.events";
const DISCOVERY_DOC =
  "https://www.googleapis.com/discovery/v1/apis/calendar/v3/rest";

function GoogleCalendar() {
  useEffect(() => {
    const gapiScript = document.createElement("script");
    gapiScript.src = "https://apis.google.com/js/api.js";
    gapiScript.onload = () => {
      window.gapi.load("client:auth2", initClient);
    };
    document.body.appendChild(gapiScript);
  }, []);

  const initClient = () => {
    window.gapi.client
      .init({
        apiKey: API_KEY,
        clientId: CLIENT_ID,
        discoveryDocs: [DISCOVERY_DOC],
        scope: SCOPES,
      })
      .then(() => {
        console.log("Google API client initialized");
      })
      .catch((err) => console.error("Error initializing Google API", err));
  };

  const handleAuthClick = () => {
    window.gapi.auth2.getAuthInstance().signIn();
  };

  const handleSignOutClick = () => {
    window.gapi.auth2.getAuthInstance().signOut();
  };

  const addEvent = () => {
    const event = {
      summary: "Party Pipeline Test Event",
      location: "Online",
      description: "This is a test event created via Google Calendar API",
      start: {
        dateTime: "2025-08-21T09:00:00-07:00",
        timeZone: "America/Los_Angeles",
      },
      end: {
        dateTime: "2025-08-21T10:00:00-07:00",
        timeZone: "America/Los_Angeles",
      },
    };

    window.gapi.client.calendar.events
      .insert({
        calendarId: "primary",
        resource: event,
      })
      .then((response) => {
        alert("Event created: " + response.result.htmlLink);
      });
  };

  return (
    <div style={{ padding: "20px" }}>
      <h2>Google Calendar Integration</h2>
      <button onClick={handleAuthClick}>Sign In</button>
      <button onClick={handleSignOutClick}>Sign Out</button>
      <button onClick={addEvent}>Add Event</button>
    </div>
  );
}

export default GoogleCalendar;
