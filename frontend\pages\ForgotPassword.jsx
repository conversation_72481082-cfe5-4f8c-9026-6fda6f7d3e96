import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { authApi } from '../redux/apiUtils';
import { toast } from 'react-toastify';
import sideimg from "../src/assets/images/singupsideimg.svg";
import '../styles/ForgotPassword.css';

const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [sent, setSent] = useState(false);

  const onSubmit = async (e) => {
    e.preventDefault();
    if (!email) return;
    setLoading(true);
    try {
      await authApi.forgotPassword(email);
      setSent(true);
      toast.success('If the email exists, a reset link has been sent.');
    } catch (err) {
      // apiUtils already toasts errors; keep UI consistent
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="forgot-password-page">
      {/* Left Section - Image */}
      <div className="forgot-password-page__image-section">
        <img 
          src={sideimg} 
          alt="Party celebration with children and balloons" 
          className="forgot-password-page__image"
          loading="lazy"
          decoding="async"
        />
      </div>

      {/* Right Section - Forgot Password Form */}
      <div className="forgot-password-page__form-section">
        <div className="forgot-password-page__form-wrapper">
          <h1 className="forgot-password-page__title">Forgot your password?</h1>
          
          <p className="pp-form-text" style={{ marginBottom: 'var(--pp-sp-24)', textAlign: 'left' }}>
            Enter the email associated with your account and we'll send you a link to reset your password.
          </p>

          <form onSubmit={onSubmit}>
            <div className="pp-form-group">
              <label htmlFor="email" className="pp-form-label">Email address</label>
              <input
                id="email"
                type="email"
                className="pp-form-input"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            
            <button type="submit" className="pp-form-button" disabled={loading}>
              {loading ? 'Sending...' : 'Send reset link'}
            </button>
          </form>

          {sent && (
            <div className="pp-form-success" style={{ marginTop: 'var(--pp-sp-16)' }}>
              If an account with that email exists, we have sent a password reset link.
            </div>
          )}

          <div className="pp-form-text forgot-password-page__bottom-link">
            Remembered it? <Link to="/login" className="pp-form-link">Back to login</Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
