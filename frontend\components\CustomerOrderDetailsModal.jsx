import React from "react";
import PopupModal from "./common/PopupModal";
import "../styles/CustomerOrderDetailsModal.css";
import { BsFillQuestionSquareFill } from "react-icons/bs";

export default function OrderDetailsModal({ isOpen, order, onClose }) {
  if (!isOpen || !order) return null;

  return (
    <PopupModal isOpen={isOpen} onClose={onClose}>
      <div className="overflow-y-auto">
        <div className="OrderDetailsModal-header">
          <div className="OrderDetailsModal-ordertitle">
            Order <span className="OrderDetailsModal-ordernum">#{order.id}</span>
          </div>
          <div className="OrderDetailsModal-total">${Number(order.total).toFixed(2)}</div>
        </div>
        <div className="OrderDetailsModal-orderdate-row">
          <div className="OrderDetailsModal-orderdate-col">
          <div className="OrderDetailsModal-orderdate-label">Order Date</div>
          <div>{order.date}</div>
          </div>
          <div className="OrderDetailsModal-orderdate-col">
          <div className="OrderDetailsModal-orderdate-label">Event</div>
          <div><span className="OrderDetailsModal-eventLink">{order.event}</span></div>
          </div>
        </div>

        <div className="OrderDetailsModal-infoRow">
          {order.groups.map((group) => (
            <div key={group.vendor}>
              <div className="OrderDetailsModal-groupTitle"><a href="#">{group.vendor}</a></div>
              <div className="OrderDetailsModal-tableWrap">
                <table className="OrderDetailsModal-table">
                  <thead>
                    <tr>
                      <th>Service</th>
                      <th>Date/Time</th>
                      <th className="OrderDetailsModal-colQty">Qty</th>
                      <th className="OrderDetailsModal-colMoney">Unit Price</th>
                      <th className="OrderDetailsModal-colCenter">Duration</th>
                      <th className="OrderDetailsModal-colMoney">Amount</th>
                    </tr>
                  </thead>
                  <tbody>
                    {group.items.map((item, i) => (
                      <tr key={i}>
                        <td data-label="Service"><a href="#" className="OrderDetailsModal-serviceLink">{item.name}</a></td>
                        <td data-label="Date/Time">
                          {item.date}
                          <br />
                          {item.time}
                        </td>
                        <td data-label="Qty" className="OrderDetailsModal-colQty">{item.qty}</td>
                        <td data-label="Unit Price" className="OrderDetailsModal-colMoney">${item.price}</td>
                        <td data-label="Duration" className="OrderDetailsModal-colCenter">{item.duration}</td>
                        <td data-label="Amount" className="OrderDetailsModal-colMoney">${item.amount}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          ))}
        </div>

        <div className="OrderDetailsModal-summary">
          <div className="OrderDetailsModal-summaryRow-total">
            <div>Subtotal</div>
            <div>${Number(order.subtotal).toFixed(2)}</div>
          </div>
          <div className="OrderDetailsModal-summaryRow">
            <div>
              Travel Fees
              <br />
              {order.travelAddress.split("\n").map((line, index) => (
                <div key={index}>{line}</div>
              ))}
            </div>
            <div>${Number(order.fee).toFixed(2)}</div>
          </div>
          <div className="OrderDetailsModal-summaryRow-total-last ">
            <div>Total</div>
            <div>${Number(order.total).toFixed(2)}</div>
          </div>
          <div className="OrderDetailsModal-summaryRow OrderDetailsModal-summaryRow--paid">
            <div>Amount Paid</div>
            <div>${Number(order.total).toFixed(2)}</div>
          </div>
        </div>

        <div className="OrderDetailsModal-footer">
          <div className="OrderDetailsModal-receiptNote">
            <span className="OrderDetailsModal-helpIcon"></span> Please keep a copy of this receipt for your records.
            <div className="OrderDetailsModal-contact">
            <br /> <BsFillQuestionSquareFill /> <b>Questions?</b> Contact us{" "}
            <a href="mailto:<EMAIL>"><EMAIL></a>
            </div>
          </div>
          <button className="pp-btn pp-btn-primary OrderDetailsModal-downloadBtn">
            Download Receipt
          </button>
        </div>
      </div>
    </PopupModal>
  );
}
