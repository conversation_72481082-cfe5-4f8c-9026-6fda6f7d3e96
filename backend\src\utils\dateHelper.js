/**
 * Date utility functions for the Party Pipeline application
 * Handles date formatting, calculations, and timezone conversions
 */

const moment = require('moment-timezone');

/**
 * Format date for display
 * @param {Date|string} date - Date to format
 * @param {string} format - Moment.js format string
 * @returns {string} Formatted date string
 */
const formatDate = (date, format = 'YYYY-MM-DD') => {
    if (!date) return null;
    return moment(date).format(format);
};

/**
 * Format date and time for display
 * @param {Date|string} date - Date to format
 * @param {string} format - Moment.js format string
 * @returns {string} Formatted datetime string
 */
const formatDateTime = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
    if (!date) return null;
    return moment(date).format(format);
};

/**
 * Create a Date interpreted as UTC (for DB storage)
 * @param {Date|string|null} date - Source date or null for now
 * @returns {Date} UTC Date object
 */
const createUTCDate = (date = null) => {
    return date ? moment.utc(date).toDate() : moment.utc().toDate();
};

/**
 * Convert any date to a UTC Date instance
 * @param {Date|string} date - Source date
 * @returns {Date} UTC Date object
 */
const toUTCDate = (date) => {
    if (!date) return null;
    return moment.utc(date).toDate();
};

/**
 * Format a date in UTC (string output)
 * @param {Date|string} date - Date to format
 * @param {string} format - Moment.js format string
 * @returns {string} Formatted UTC string
 */
const formatDateUTC = (date, format = 'YYYY-MM-DD') => {
    if (!date) return null;
    return moment.utc(date).format(format);
};

/**
 * Format a datetime in UTC (string output)
 * @param {Date|string} date - Date to format
 * @param {string} format - Moment.js format string
 * @returns {string} Formatted UTC datetime string
 */
const formatDateTimeUTC = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
    if (!date) return null;
    return moment.utc(date).format(format);
};

/**
 * Get relative time (e.g., "2 hours ago", "in 3 days")
 * @param {Date|string} date - Date to compare
 * @returns {string} Relative time string
 */
const getRelativeTime = (date) => {
    if (!date) return null;
    return moment(date).fromNow();
};

/**
 * Get time until a specific date
 * @param {Date|string} date - Target date
 * @returns {string} Time until string
 */
const getTimeUntil = (date) => {
    if (!date) return null;
    return moment(date).toNow();
};

/**
 * Check if a date is in the past
 * @param {Date|string} date - Date to check
 * @returns {boolean} True if date is in the past
 */
const isInPast = (date) => {
    if (!date) return false;
    return moment(date).isBefore(moment());
};

/**
 * Check if a date is in the future
 * @param {Date|string} date - Date to check
 * @returns {boolean} True if date is in the future
 */
const isInFuture = (date) => {
    if (!date) return false;
    return moment(date).isAfter(moment());
};

/**
 * Check if a date is today
 * @param {Date|string} date - Date to check
 * @returns {boolean} True if date is today
 */
const isToday = (date) => {
    if (!date) return false;
    return moment(date).isSame(moment(), 'day');
};

/**
 * Check if a date is tomorrow
 * @param {Date|string} date - Date to check
 * @returns {boolean} True if date is tomorrow
 */
const isTomorrow = (date) => {
    if (!date) return false;
    return moment(date).isSame(moment().add(1, 'day'), 'day');
};

/**
 * Get the difference between two dates in various units
 * @param {Date|string} date1 - First date
 * @param {Date|string} date2 - Second date
 * @param {string} unit - Unit of measurement (days, hours, minutes, etc.)
 * @returns {number} Difference in specified unit
 */
const getDateDifference = (date1, date2, unit = 'days') => {
    if (!date1 || !date2) return 0;
    return moment(date1).diff(moment(date2), unit);
};

/**
 * Add time to a date
 * @param {Date|string} date - Base date
 * @param {number} amount - Amount to add
 * @param {string} unit - Unit of time (days, hours, minutes, etc.)
 * @returns {Date} New date with added time
 */
const addTime = (date, amount, unit = 'days') => {
    if (!date) return null;
    return moment(date).add(amount, unit).toDate();
};

/**
 * Subtract time from a date
 * @param {Date|string} date - Base date
 * @param {number} amount - Amount to subtract
 * @param {string} unit - Unit of time (days, hours, minutes, etc.)
 * @returns {Date} New date with subtracted time
 */
const subtractTime = (date, amount, unit = 'days') => {
    if (!date) return null;
    return moment(date).subtract(amount, unit).toDate();
};

/**
 * Get start of day for a given date
 * @param {Date|string} date - Date to process
 * @returns {Date} Start of day
 */
const getStartOfDay = (date) => {
    if (!date) return null;
    return moment(date).startOf('day').toDate();
};

/**
 * Get end of day for a given date
 * @param {Date|string} date - Date to process
 * @returns {Date} End of day
 */
const getEndOfDay = (date) => {
    if (!date) return null;
    return moment(date).endOf('day').toDate();
};

/**
 * Get start of week for a given date
 * @param {Date|string} date - Date to process
 * @returns {Date} Start of week (Monday)
 */
const getStartOfWeek = (date) => {
    if (!date) return null;
    return moment(date).startOf('week').toDate();
};

/**
 * Get end of week for a given date
 * @param {Date|string} date - Date to process
 * @returns {Date} End of week (Sunday)
 */
const getEndOfWeek = (date) => {
    if (!date) return null;
    return moment(date).endOf('week').toDate();
};

/**
 * Get start of month for a given date
 * @param {Date|string} date - Date to process
 * @returns {Date} Start of month
 */
const getStartOfMonth = (date) => {
    if (!date) return null;
    return moment(date).startOf('month').toDate();
};

/**
 * Get end of month for a given date
 * @param {Date|string} date - Date to process
 * @returns {Date} End of month
 */
const getEndOfMonth = (date) => {
    if (!date) return null;
    return moment(date).endOf('month').toDate();
};

/**
 * Check if a date falls within business hours
 * @param {Date|string} date - Date to check
 * @param {number} startHour - Business start hour (24-hour format)
 * @param {number} endHour - Business end hour (24-hour format)
 * @returns {boolean} True if within business hours
 */
const isWithinBusinessHours = (date, startHour = 9, endHour = 17) => {
    if (!date) return false;
    const hour = moment(date).hour();
    return hour >= startHour && hour < endHour;
};

/**
 * Check if a date is a weekend
 * @param {Date|string} date - Date to check
 * @returns {boolean} True if weekend (Saturday or Sunday)
 */
const isWeekend = (date) => {
    if (!date) return false;
    const dayOfWeek = moment(date).day();
    return dayOfWeek === 0 || dayOfWeek === 6; // 0 = Sunday, 6 = Saturday
};

/**
 * Check if a date is a weekday
 * @param {Date|string} date - Date to check
 * @returns {boolean} True if weekday (Monday to Friday)
 */
const isWeekday = (date) => {
    if (!date) return false;
    return !isWeekend(date);
};

/**
 * Get next business day
 * @param {Date|string} date - Starting date
 * @returns {Date} Next business day
 */
const getNextBusinessDay = (date) => {
    if (!date) return null;
    let nextDay = moment(date).add(1, 'day');

    while (isWeekend(nextDay)) {
        nextDay = nextDay.add(1, 'day');
    }

    return nextDay.toDate();
};

/**
 * Calculate age from birth date
 * @param {Date|string} birthDate - Birth date
 * @returns {number} Age in years
 */
const calculateAge = (birthDate) => {
    if (!birthDate) return 0;
    return moment().diff(moment(birthDate), 'years');
};

/**
 * Get date range for common periods
 * @param {string} period - Period type (today, yesterday, this_week, last_week, this_month, last_month, etc.)
 * @returns {Object} Object with start and end dates
 */
const getDateRange = (period) => {
    const now = moment();

    switch (period.toLowerCase()) {
        case 'today':
            return {
                start: now.clone().startOf('day').toDate(),
                end: now.clone().endOf('day').toDate()
            };

        case 'yesterday':
            return {
                start: now.clone().subtract(1, 'day').startOf('day').toDate(),
                end: now.clone().subtract(1, 'day').endOf('day').toDate()
            };

        case 'this_week':
            return {
                start: now.clone().startOf('week').toDate(),
                end: now.clone().endOf('week').toDate()
            };

        case 'last_week':
            return {
                start: now.clone().subtract(1, 'week').startOf('week').toDate(),
                end: now.clone().subtract(1, 'week').endOf('week').toDate()
            };

        case 'this_month':
            return {
                start: now.clone().startOf('month').toDate(),
                end: now.clone().endOf('month').toDate()
            };

        case 'last_month':
            return {
                start: now.clone().subtract(1, 'month').startOf('month').toDate(),
                end: now.clone().subtract(1, 'month').endOf('month').toDate()
            };

        case 'this_year':
            return {
                start: now.clone().startOf('year').toDate(),
                end: now.clone().endOf('year').toDate()
            };

        case 'last_year':
            return {
                start: now.clone().subtract(1, 'year').startOf('year').toDate(),
                end: now.clone().subtract(1, 'year').endOf('year').toDate()
            };

        case 'last_30_days':
            return {
                start: now.clone().subtract(30, 'days').startOf('day').toDate(),
                end: now.clone().endOf('day').toDate()
            };

        case 'last_90_days':
            return {
                start: now.clone().subtract(90, 'days').startOf('day').toDate(),
                end: now.clone().endOf('day').toDate()
            };

        default:
            return {
                start: now.clone().startOf('day').toDate(),
                end: now.clone().endOf('day').toDate()
            };
    }
};

/**
 * Validate if a string is a valid date
 * @param {string} dateString - Date string to validate
 * @returns {boolean} True if valid date
 */
const isValidDate = (dateString) => {
    return moment(dateString).isValid();
};

/**
 * Parse date from various formats
 * @param {string} dateString - Date string to parse
 * @param {Array} formats - Array of possible formats
 * @returns {Date|null} Parsed date or null if invalid
 */
const parseDate = (dateString, formats = ['YYYY-MM-DD', 'MM/DD/YYYY', 'DD/MM/YYYY']) => {
    if (!dateString) return null;

    const parsedDate = moment(dateString, formats, true);
    return parsedDate.isValid() ? parsedDate.toDate() : null;
};

/**
 * Convert time string to minutes
 * @param {string} timeString - Time string in HH:mm format
 * @returns {number} Minutes since midnight
 */
const timeToMinutes = (timeString) => {
    if (!timeString || typeof timeString !== 'string') return 0;

    const [hours, minutes] = timeString.split(':').map(Number);
    if (isNaN(hours) || isNaN(minutes)) return 0;

    return hours * 60 + minutes;
};

/**
 * Convert minutes to time string
 * @param {number} minutes - Minutes since midnight
 * @returns {string} Time string in HH:mm format
 */
const minutesToTime = (minutes) => {
    if (typeof minutes !== 'number' || minutes < 0) return '00:00';

    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;

    return `${String(hours).padStart(2, '0')}:${String(mins).padStart(2, '0')}`;
};

/**
 * Get timezone abbreviation
 * @param {string} timezone - Timezone name
 * @returns {string} Timezone abbreviation
 */
const getTimezoneAbbr = (timezone = 'America/New_York') => {
    return moment.tz(timezone).format('z');
};

/**
 * Convert date to specific timezone
 * @param {Date|string} date - Date to convert
 * @param {string} timezone - Target timezone
 * @returns {Date} Date in specified timezone
 */
const convertToTimezone = (date, timezone = 'America/New_York') => {
    if (!date) return null;
    return moment.tz(date, timezone).toDate();
};

/**
 * Get booking reminder dates
 * @param {Date|string} eventDate - Event date
 * @returns {Object} Object with reminder dates
 */
const getBookingReminderDates = (eventDate) => {
    if (!eventDate) return {};

    const event = moment(eventDate);

    return {
        oneWeekBefore: event.clone().subtract(7, 'days').toDate(),
        threeDaysBefore: event.clone().subtract(3, 'days').toDate(),
        oneDayBefore: event.clone().subtract(1, 'day').toDate(),
        fourHoursBefore: event.clone().subtract(4, 'hours').toDate(),
        oneHourBefore: event.clone().subtract(1, 'hour').toDate()
    };
};

/**
 * Check if booking is within cancellation window
 * @param {Date|string} eventDate - Event date
 * @param {number} hoursBeforeEvent - Hours before event for cancellation
 * @returns {boolean} True if within cancellation window
 */
const isWithinCancellationWindow = (eventDate, hoursBeforeEvent = 24) => {
    if (!eventDate) return false;

    const now = moment();
    const event = moment(eventDate);
    const cancellationDeadline = event.clone().subtract(hoursBeforeEvent, 'hours');

    return now.isAfter(cancellationDeadline);
};

module.exports = {
    formatDate,
    formatDateTime,
    getRelativeTime,
    getTimeUntil,
    isInPast,
    isInFuture,
    isToday,
    isTomorrow,
    getDateDifference,
    addTime,
    subtractTime,
    getStartOfDay,
    getEndOfDay,
    getStartOfWeek,
    getEndOfWeek,
    getStartOfMonth,
    getEndOfMonth,
    isWithinBusinessHours,
    isWeekend,
    isWeekday,
    getNextBusinessDay,
    calculateAge,
    getDateRange,
    isValidDate,
    parseDate,
    timeToMinutes,
    minutesToTime,
    getTimezoneAbbr,
    convertToTimezone,
    getBookingReminderDates,
    isWithinCancellationWindow
};
