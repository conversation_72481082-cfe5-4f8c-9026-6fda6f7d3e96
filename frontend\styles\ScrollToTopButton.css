.scroll-to-top-btn {
  position: fixed;
  bottom: var(--pp-sp-24);
  right: var(--pp-sp-24);
  width: 48px;
  height: 48px;
  background: var(--pp-clr-secondary);
  color: var(--pp-clr-primary);
  border: none;
  border-radius: var(--pp-bor-rad-round);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--pp-font-heading6);
  box-shadow: var(--pp-shdw-lg);
  z-index: var(--pp-z-index-sticky);
  transition: all 300ms ease;
  opacity: 0.9;
}

.scroll-to-top-btn:hover {
  opacity: 1;
  transform: translateY(-2px);
  box-shadow: var(--pp-shdw-xl);
}

.scroll-to-top-btn:active {
  transform: translateY(0);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .scroll-to-top-btn {
    bottom: var(--pp-sp-16);
    right: var(--pp-sp-16);
    width: 44px;
    height: 44px;
    font-size: var(--pp-font-base2-font);
  }
}

@media (max-width: 414px) {
  .scroll-to-top-btn {
    bottom: var(--pp-sp-12);
    right: var(--pp-sp-12);
    width: 40px;
    height: 40px;
    font-size: var(--pp-font-base-font);
  }
}
