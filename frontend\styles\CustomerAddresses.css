.CustomerAddresses-root {
    max-width: 1400px;
    margin: 0 auto;
    
    background: var(--pp-clr-bg);
    color: var(--pp-clr-text-main);
    font-family: var(--pp-font-Metro-Sans);
  }
  
  .CustomerAddresses-title {
    font-size: var(--pp-font-heading4);
    font-weight: 700;
    margin-bottom: var(--pp-sp-24);
  }
  .CustomerAddresses-tabs {
    display: flex;
    gap: var(--pp-sp-24);
    border-bottom: 1px solid var(--pp-clr-border-light);
    margin-bottom: var(--pp-sp-24);
  }
  .CustomerAddresses-tab {
    font-size: var(--pp-font-extra-small);
    color: var(--pp-clr-text-gray);
    text-decoration: none;
    padding-bottom: var(--pp-sp-12);
    transition: color 0.18s;
  }
  .CustomerAddresses-tab--active {
    color: var(--pp-clr-secondary);
    font-weight: 600;
    border-bottom: 2px solid var(--pp-clr-secondary);
  }
  
  
  
  .CustomerAddresses-sectionTitle {
    font-size: var(--pp-font-heading5);
    font-weight: 600;
    margin-bottom: var(--pp-sp-24);
  }
  
  .CustomerAddresses-table {
    display: flex;
    flex-direction: column;
    width: 100%;
    border-radius: var(--pp-bor-rad-8);
    overflow: hidden;
    box-shadow: none;
    border: 1px solid var(--pp-clr-border-light);
    background: var(--pp-clr-primary);
    margin-bottom: var(--pp-sp-32);
  }
  .CustomerAddresses-row {
    display: grid;
    grid-template-columns: 2.1fr 1.2fr 0.7fr 1.2fr;
    align-items: center;
    gap: var(--pp-sp-16);
    padding: var(--pp-sp-16) var(--pp-sp-24);
    border-bottom: 1px solid var(--pp-clr-border-light);
  }
  .CustomerAddresses-row:last-child {
    border-bottom: none;
  }
  
  .CustomerAddresses-info {
    display: flex;
    align-items: flex-start;
    gap: var(--pp-sp-32);
  }
  .CustomerAddresses-icon {
    height: 36px;
    width: 36px;
    object-fit: contain;
    border-radius: var(--pp-bor-rad-8);
    
    margin-top: var(--pp-sp-2, 2px);
  }
  .CustomerAddresses-label {
    font-size: var(--pp-font-small-font);
    font-weight: 600;
    color: var(--pp-clr-text-main);
  }
  .CustomerAddresses-address {
    font-size: var(--pp-font-extra-small);
   
  }
  .CustomerAddresses-phone {
    font-size: var(--pp-font-extra-small);
    color: var(--pp-clr-text-main);
  }
  .CustomerAddresses-phonetitle {
   
    font-weight: 600;
  }
  
  .CustomerAddresses-role {
    color: var(--pp-clr-secondary);
    font-size: var(--pp-font-extra-small);
    font-weight: 700;
  }
  
  .CustomerAddresses-actions {
    display: flex;
    gap: var(--pp-sp-12);
    align-items: center;
    justify-content: flex-end;
    flex-wrap: wrap;
  }
  .CustomerAddresses-secondaryBtn {
    border: 1px solid var(--pp-clr-border);
    background: var(--pp-clr-primary);
    color: var(--pp-clr-text-main);
    padding: var(--pp-sp-8) var(--pp-sp-20);
    border-radius: var(--pp-bor-rad-24);
    min-width: 25px;
    font-size: var(--pp-font-extra-small);
    font-weight: 500;
    cursor: pointer;
    transition: background .18s, border .18s;
    margin-right: 0;
  }
  .CustomerAddresses-secondaryBtn:hover {
    border-color: var(--pp-clr-secondary);
  }
  .CustomerAddresses-addBtn {
    margin-top: var(--pp-sp-24);
    min-width: 160px;
  }
  
  /* Responsive */
  @media (max-width: 700px) {
    .CustomerAddresses-root {
      padding: var(--pp-sp-12);
    }
    .CustomerAddresses-table {
      border: none;
    }
    .CustomerAddresses-row {
      grid-template-columns: 1fr;
      grid-row-gap: var(--pp-sp-8);
      padding: var(--pp-sp-16) var(--pp-sp-8);
    }
    .CustomerAddresses-actions {
      justify-content: flex-start;
      flex-wrap: wrap;
      gap: var(--pp-sp-12);
    }
  }
  