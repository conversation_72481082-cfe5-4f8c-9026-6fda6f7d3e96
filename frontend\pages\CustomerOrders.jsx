import React, { useState } from "react";
import "../styles/CustomerOrders.css";
import OrderDetailsModal from "../components/CustomerOrderDetailsModal";
import { Link } from "react-router-dom";

const ORDERS = [
    {
        id: 3587,
        date: 'May 1, 2023',
        event: 'Mountainview Secondary School Festival',
        total: '441.00',
        subtotal: '375.00',
        fee: '66.00',
        travelAddress: '1720 N 272 E\nApt 5E\nSalt Lake City, UT 85515',
        groups: [
          {
            vendor: "Nero’s Heroes",
            items: [
              {
                name: "Storm Trooper",
                date: "Jun 22, 2023",
                time: "4:00pm–5:30pm",
                qty: 1,
                price: "75/hr",
                duration: "1.5 hrs",
                amount: "112.50"
              },
              {
                name: "Darth Vader",
                date: "Jun 22, 2023",
                time: "4:00pm–5:30pm",
                qty: 1,
                price: "75/hr",
                duration: "1.5 hrs",
                amount: "112.50"
              },
              {
                name: "<PERSON>",
                date: "Jun 22, 2023",
                time: "4:30pm–5:30pm",
                qty: 1,
                price: "75/hr",
                duration: "1.5 hrs",
                amount: "75.00"
              }
            ],
          },
          {
            vendor: "Twisted",
            items: [
              {
                name: "Bumble Photo Kiosk",
                date: "Jun 22, 2023",
                time: "4:45pm–5:30pm",
                qty: 2,
                price: "50/hr",
                duration: ".75 hrs",
                amount: "75.00"
              },
              {
                name: "Entertainer Package",
                date: "Jun 22, 2023",
                time: "4:45pm–5:30pm",
                qty: 2,
                price: "50/hr",
                duration: ".75 hrs",
                amount: "75.00"
              }
            ],
          }
        ]
      },
  {
    id: 3692,
    date: "May 4, 2023",
    event: "Mountainview Secondary School Festival",
    total: "289.50",
    subtotal: '250.00',
    fee: '39.50',
    travelAddress: '123 Main St\nApt 1A\nAnytown, USA 12345',
    groups: [
      {
        vendor: "Party Planners",
        items: [
          {
            name: "Event Coordination",
            date: "May 4, 2023",
            time: "9:00am–5:00pm",
            qty: 1,
            price: "250/day",
            duration: "8 hrs",
            amount: "250.00"
          }
        ]
      }
    ],
    eventType: "Event",
    highlight: false,
  },
  {
    id: 3587,
    date: "May 1, 2023",
    total: " 441.00",
    event: "Mountainview Secondary School Festival",
    subtotal: '375.00',
    fee: '66.00',
    travelAddress: '1720 N 272 E\nApt 5E\nSalt Lake City, UT 85515',
    groups: [
      {
        vendor: "Nero’s Heroes",
        items: [
          {
            name: "Storm Trooper",
            date: "Jun 22, 2023",
            time: "4:00pm–5:30pm",
            qty: 1,
            price: "75/hr",
            duration: "1.5 hrs",
            amount: "112.50"
          },
          {
            name: "Darth Vader",
            date: "Jun 22, 2023",
            time: "4:00pm–5:30pm",
            qty: 1,
            price: "75/hr",
            duration: "1.5 hrs",
            amount: "112.50"
          },
          {
            name: "Luke Skywalker",
            date: "Jun 22, 2023",
            time: "4:30pm–5:30pm",
            qty: 1,
            price: "75/hr",
            duration: "1.5 hrs",
            amount: "75.00"
          }
        ],
      },
      {
        vendor: "Twisted",
        items: [
          {
            name: "Bumble Photo Kiosk",
            date: "Jun 22, 2023",
            time: "4:45pm–5:30pm",
            qty: 2,
            price: "50/hr",
            duration: ".75 hrs",
            amount: "75.00"
          },
          {
            name: "Entertainer Package",
            date: "Jun 22, 2023",
            time: "4:45pm–5:30pm",
            qty: 2,
            price: "50/hr",
            duration: ".75 hrs",
            amount: "75.00"
          }
        ],
      }
    ],
    eventType: "Event",
    highlight: true, // row highlight, as in your screenshot
  },
  {
    id: 3241,
    date: "April 23, 2023",
    total: "164.00",
    event: "Kaiden’s Birthday",
    subtotal: '150.00',
    fee: '14.00',
    travelAddress: '789 Pine St\nSuite 300\nOtherville, USA 67890',
    groups: [
      {
        vendor: "Balloon Animals Co.",
        items: [
          {
            name: "Balloon Animal Artist",
            date: "April 23, 2023",
            time: "2:00pm–4:00pm",
            qty: 1,
            price: "80/hr",
            duration: "2 hrs",
            amount: "160.00"
          }
        ]
      }
    ],
    eventType: "Event",
    highlight: false,
  },
];

export default function CustomerOrders() {
    const [openOrder, setOpenOrder] = useState(null);
  return (
    <div className="CustomerOrders-root">
      

      <section className="CustomerOrders-section">
        <h2 className="CustomerOrders-sectionTitle">Orders</h2>
        <div className="CustomerOrders-table">
          {ORDERS.map((order, idx) => (
            <div
              key={order.id}
              className={`CustomerOrders-row${order.highlight ? " CustomerOrders-row--highlight" : ""}`}
            >
              {/* Header: Date on the left, links on the right */}
              <div className="CustomerOrders-rowHeader">
                <div className="CustomerOrders-date">{order.date}</div>
                <div className="CustomerOrders-linkCol">
                  <a href="#" className="CustomerOrders-link">View Invoice</a>
                  <a href="#" className="CustomerOrders-link" onClick={() => setOpenOrder(order)}>View Order Details</a>
                </div>
              </div>

              {/* Body: three labeled columns */}
              <div className="CustomerOrders-rowBody">
                <div className="CustomerOrders-col">
                  <div className="CustomerOrders-colLabel">Order #</div>
                  <div className="CustomerOrders-colValue">{order.id}</div>
                </div>
                <div className="CustomerOrders-col">
                  <div className="CustomerOrders-colLabel">Total</div>
                  <div className="CustomerOrders-colValue">${order.total}</div>
                </div>
                <div className="CustomerOrders-col">
                  <div className="CustomerOrders-colLabel">Event</div>
                  <div className="CustomerOrders-colValue">{order.event}</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>
      <OrderDetailsModal
        isOpen={!!openOrder}
        order={openOrder}
        onClose={() => setOpenOrder(null)}
      />
    </div>
  );
}
