import React, { useState } from "react";
import CustomSelect from "../../components/CustomeSelect";
import { RiDeleteBin6Line } from "react-icons/ri";

export default function AddOns({ addOns, onChange, category }) {
  const [newAddOn, setNewAddOn] = useState({
    title: "",
    pricingMethod: "perItem",
    price: "",
    multiplyCosts: false,
    description: "",
    requireSelection: false,
  });

  const handleAddAddOn = () => {
    if (newAddOn.title && newAddOn.price) {
      onChange([...addOns, { ...newAddOn, id: Date.now() }]);
      setNewAddOn({
        title: "",
        pricingMethod: "perItem",
        price: "",
        multiplyCosts: false,
        description: "",
        requireSelection: false,
      });
    }
  };

  const handleRemoveAddOn = (id) => {
    onChange(addOns.filter((addOn) => addOn.id !== id));
  };

  const handleUpdateAddOn = (id, field, value) => {
    onChange(
      addOns.map((addOn) =>
        addOn.id === id ? { ...addOn, [field]: value } : addOn
      )
    );
  };

  const renderAddOnItem = (addOn, index) => (
    <div key={addOn.id} className="ao-addon-item">
      <div className="ao-header">
        <div className="ao-form-row">
          <div className="ao-form-group">
            <label className="ao-form-label">Add-on Title</label>
            <input
              type="text"
              className="ao-form-input"
              placeholder="Enter add-on title"
              value={addOn.title}
              onChange={(e) =>
                handleUpdateAddOn(addOn.id, "title", e.target.value)
              }
            />
          </div>
       <div className="ao-form-group">
       <div className="ao-form-group">
            <label className="ao-form-label">Pricing Method</label>
            <CustomSelect
              className="ao-form-select"
              value={addOn.pricingMethod}
              onChange={(v) => handleUpdateAddOn(addOn.id, "pricingMethod", v)}
              options={[
                { label: "Per Item", value: "perItem" },
                { label: "Quantity", value: "quantity" },
                { label: "Flat Fee", value: "flatFee" },
              ]}
            />
          </div>
          <div className="ao-form-group">
            <label className="ao-form-label">Price</label>
            <div className="ao-price-input">
              <span className="ao-currency">$</span>
              <input
                type="number"
                className="ao-form-input"
                placeholder="0"
                value={addOn.price}
                onChange={(e) =>
                  handleUpdateAddOn(addOn.id, "price", e.target.value)
                }
              />
            </div>
          </div>
       </div>
          <button
            type="button"
            className="ao-btn-remove"
            onClick={() => handleRemoveAddOn(addOn.id)}
            aria-label="Remove add-on"
          >
            <RiDeleteBin6Line />
          </button>
        </div>
      </div>

      <div className="ao-toggles">
        <div className="ao-toggle-group">
          <label className="ao-toggle-wrapper">
            <input
              type="checkbox"
              className="ao-toggle"
              checked={addOn.multiplyCosts}
              onChange={(e) =>
                handleUpdateAddOn(addOn.id, "multiplyCosts", e.target.checked)
              }
            />
            <span className="ao-toggle-label">Multiply Costs</span>
          </label>
          <p className="ao-toggle-description">
            {category === "food"
              ? "Multiply the cost based on number of guests the customer selects."
              : "Multiply the total by number of workers the customer selects."}
          </p>
        </div>

        <div className="ao-toggle-group">
          <label className="ao-toggle-wrapper">
            <input
              type="checkbox"
              className="ao-toggle"
              checked={addOn.requireSelection}
              onChange={(e) =>
                handleUpdateAddOn(
                  addOn.id,
                  "requireSelection",
                  e.target.checked
                )
              }
            />
            <span className="ao-toggle-label">Require Customer Selection</span>
          </label>
          <p className="ao-toggle-description">
            {category === "food"
              ? "Require the customer to select or deselect this add-on."
              : "If toggled on, customer must select or deselect this add-on."}
          </p>
        </div>
      </div>

      <div className="ao-form-group">
        <label className="ao-form-label">Description</label>
        <textarea
          className="ao-form-textarea"
          placeholder="Describe this add-on..."
          value={addOn.description}
          onChange={(e) =>
            handleUpdateAddOn(addOn.id, "description", e.target.value)
          }
          rows={3}
        />
        <div className="ao-char-count">0/100</div>
      </div>
    </div>
  );

  return (
    <div className="ao-section">
      <h3 className="ao-section-title">Add-ons</h3>
      <p className="ao-section-description">
        {category === "food"
          ? "Add additional options that can be purchased for this service."
          : "Add additional options that are purchasable for this service."}
      </p>

      {addOns.map(renderAddOnItem)}

      <div className="ao-new-addon">
        <div className="ao-header">
          <div className="ao-form-row">
            <div className="ao-form-group">
              <label className="ao-form-label">Add-on Title</label>
              <input
                type="text"
                className="ao-form-input"
                placeholder="Enter add-on title"
                value={newAddOn.title}
                onChange={(e) =>
                  setNewAddOn({ ...newAddOn, title: e.target.value })
                }
              />
            </div>
         <div className="ao-form-row" >
         <div className="ao-form-group">
              <label className="ao-form-label">Pricing Method</label>
              <CustomSelect
                className="ao-form-select"
                value={newAddOn.pricingMethod}
                onChange={(v) => setNewAddOn({ ...newAddOn, pricingMethod: v })}
                options={[
                  { label: "Per Item", value: "perItem" },
                  { label: "Quantity", value: "quantity" },
                  { label: "Flat Fee", value: "flatFee" },
                ]}
              />
            </div>
            <div className="ao-form-group">
              <label className="ao-form-label">Price</label>
              <div className="ao-price-input">
                <span className="ao-currency">$</span>
                <input
                  type="number"
                  className="ao-form-input"
                  placeholder="0"
                  value={newAddOn.price}
                  onChange={(e) =>
                    setNewAddOn({ ...newAddOn, price: e.target.value })
                  }
                />
              </div>
            </div>
         </div>
          </div>
        </div>

        <div className="ao-toggles">
          <div className="ao-toggle-group">
            <label className="ao-toggle-wrapper">
              <input
                type="checkbox"
                className="ao-toggle"
                checked={newAddOn.multiplyCosts}
                onChange={(e) =>
                  setNewAddOn({ ...newAddOn, multiplyCosts: e.target.checked })
                }
              />
              <span className="ao-toggle-label">Multiply Costs</span>
            </label>
            <p className="ao-toggle-description">
              {category === "food"
                ? "Multiply the cost based on number of guests the customer selects."
                : "Multiply the total by number of workers the customer selects."}
            </p>
          </div>

          <div className="ao-toggle-group">
            <label className="ao-toggle-wrapper">
              <input
                type="checkbox"
                className="ao-toggle"
                checked={newAddOn.requireSelection}
                onChange={(e) =>
                  setNewAddOn({
                    ...newAddOn,
                    requireSelection: e.target.checked,
                  })
                }
              />
              <span className="ao-toggle-label">
                Require Customer Selection
              </span>
            </label>
            <p className="ao-toggle-description">
              {category === "food"
                ? "Require the customer to select or deselect this add-on."
                : "If toggled on, customer must select or deselect this add-on."}
            </p>
          </div>
        </div>

        <div className="ao-form-group">
          <label className="ao-form-label">Description</label>
          <textarea
            className="ao-form-textarea"
            placeholder="Describe this add-on..."
            value={newAddOn.description}
            onChange={(e) =>
              setNewAddOn({ ...newAddOn, description: e.target.value })
            }
            rows={3}
          />
          <div className="ao-char-count">0/100</div>
        </div>
      </div>

      <button
        type="button"
        className="ao-btn-add"
        onClick={handleAddAddOn}
        disabled={!newAddOn.title || !newAddOn.price}
      >
        {category === "food" ? "Add Add-on" : "Add Add-on"}
      </button>
    </div>
  );
}
