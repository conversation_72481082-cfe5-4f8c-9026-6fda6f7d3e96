import { useState, use<PERSON><PERSON>back, memo } from "react";
import { Link, useNavigate } from "react-router-dom";
import { IoMdCart } from "react-icons/io";
import { useAuth } from "../redux/useAuth";
import { IoSearch, IoClose } from "react-icons/io5";
import { HiOutlineMenu } from "react-icons/hi";
import {
  MdEvent,
  MdMessage,
  MdPerson,
  MdPowerSettingsNew,
} from "react-icons/md";

import logo from "../src/assets/images/party-pipeline-logo.svg";
import "../styles/Navbar.css";

const IMAGE_URL = import.meta.env.VITE_IMAGE_URL || 'http://localhost:5000';

const NAV_LINKS = [
  { label: "Browse vendors", to: "/browse-services" },
  { label: "Why book with us", to: "/why-book" },
  { label: "Become a vendor", to: "/become-vendor" },
];

const CUSTOMER_MENU = [
  {
    label: "My Events",
    to: "/my-events",
    icon: <MdEvent style={{ color: "#7069f0" }} />,
  },
  {
    label: "Messages",
    to: "/messages",
    icon: <MdMessage style={{ color: "#7968f0" }} />,
    badge: true,
  },
  {
    label: "My Account",
    to: "/customer-account-profile",
    icon: <MdPerson style={{ color: "#253858" }} />,
  },
  {
    label: "Log Out",
    to: "/logout",
    icon: <MdPowerSettingsNew style={{ color: "#dc3545" }} />,
  },
];

const VENDOR_MENU = [
  { label: "Transfer Funds", to: "/vendor/balance", icon: <span style={{ color: "#28a745" }}>💵</span>, balance: true },
  { label: "Vendor Dashboard", to: "/vendor", icon: <span style={{ color: "#11538f" }}>📈</span> },
  { label: "My Events", to: "/customer-event", icon: <span style={{ color: "#7069f0" }}>📅</span> },
  { label: "Messages", to: "/vendor/messages", icon: <span style={{ color: "#7968f0" }}>💬</span>, badge: true },
  { label: "My Account", to: "/customer-account-profile", icon: <span style={{ color: "#253858" }}>👤</span> },
  { label: "Log Out", to: "/logout", icon: <span style={{ color: "#dc3545" }}>⏻</span> },
];

function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [imagePreview, setImagePreview] = useState("");
  const { isAuthenticated, user, logout } = useAuth();
  const navigate = useNavigate();

  const prefetchRoute = useCallback((path) => {}, []);

  const handleLogout = async () => {
    try {
      await logout();
      navigate("/"); // redirect to home page
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  // Choose menu based on role
  const userMenu = user ? (user.role === "vendor" ? VENDOR_MENU : CUSTOMER_MENU) : [];
  

  return (
    <header className="c-navbar">
      <div className="c-navbar__inner ">
        {/* Logo */}
        <Link className="c-navbar__logo" to="/" aria-label="Party Pipeline home">
          <img className="c-navbar__logo-img" src={logo} alt="Party Pipeline" loading="lazy" decoding="async" />
        </Link>

        {/* Main nav */}
        <nav className="c-navbar__nav" aria-label="Primary">
          <ul className="c-navbar__links">
            {NAV_LINKS.map((item) => (
              <li key={item.label} className="c-navbar__link-item">
                <Link
                  className="c-navbar__link"
                  to={item.to}
                  onMouseEnter={() => prefetchRoute(item.to)}
                  onFocus={() => prefetchRoute(item.to)}
                >
                  {item.label}
                </Link>
              </li>
            ))}
          </ul>
        </nav>

        <div className="c-navbar__actions">
          {/* Cart */}
          <button type="button" aria-label="Cart" className="pp-btn c-navbar__icon-btn">
            <IoMdCart />
          </button>

          {/* Authenticated user or login */}
          {!isAuthenticated ? (
            <Link
              to="/login"
              className="pp-btn pp-btn-secondary c-navbar__login"
              onMouseEnter={() => prefetchRoute("/login")}
              onFocus={() => prefetchRoute("/login")}
            >
              Login
            </Link>
          ) : (
            <div
              className="c-navbar__profile-dropdown c-navbar__profile-desktop"
              tabIndex={0}
              onClick={() => setIsDropdownOpen((v) => !v)}
              onBlur={(e) => {
                // Only close if clicking outside the dropdown
                if (!e.currentTarget.contains(e.relatedTarget)) {
                  setIsDropdownOpen(false);
                }
              }}
            >
              <button
                type="button"
                className="pp-btn c-navbar__icon-btn c-navbar__profile-btn"
                aria-label="User Menu"
                aria-expanded={isDropdownOpen}
                aria-controls="user-menu"
              >
                <span>{user?.firstName || user?.name}</span>
                {user?.profileImage ? (
                  <img 
                    src={imagePreview
                      ? (imagePreview.startsWith('data:') || imagePreview.startsWith('http')
                          ? imagePreview
                          : IMAGE_URL + imagePreview)
                      : user.profileImage.startsWith('data:') || user.profileImage.startsWith('http')
                        ? user.profileImage
                        : IMAGE_URL + user.profileImage}  
                    alt="Profile" 
                    className="c-navbar__profile-avatar-img"
                    style={{ width: "24px", height: "24px", borderRadius: "50%", objectFit: "cover" }}
                  />
                ) : (
                  <span className="c-navbar__profile-avatar" aria-hidden="true">😊</span>
                )}
              </button>

              {isDropdownOpen && (
                <ul 
                  className="c-navbar__dropdown-menu" 
                  id="user-menu" 
                  role="menu"
                  onMouseDown={(e) => e.preventDefault()} // Prevent blur when clicking inside
                >
                  <li className="c-navbar__dropdown-header">{user?.firstName || user?.name}</li>
                  {userMenu.map((item) => (
                    <li key={item.label} role="none">
                      {item.label === "Log Out" ? (
                        <button
                          onClick={() => {
                            handleLogout();
                            setIsDropdownOpen(false);
                          }}
                          className="c-navbar__dropdown-link"
                        >
                          {item.icon && <span className="c-navbar__dropdown-icon">{item.icon}</span>}
                          {item.label}
                        </button>
                      ) : (
                        <Link
                          to={item.to}
                          role="menuitem"
                          className="c-navbar__dropdown-link"
                          onClick={() => setIsDropdownOpen(false)}
                        >
                          {item.icon && <span className="c-navbar__dropdown-icon">{item.icon}</span>}
                          {item.label}
                          {item.balance && <span className="c-navbar__vendor-balance">${user?.balance || 0}</span>}
                          {item.badge && user.messages > 0 && (
                            <span className="c-navbar__badge">{user.messages}</span>
                          )}
                        </Link>
                      )}
                    </li>
                  ))}
                </ul>
              )}
            </div>
          )}

          {/* Mobile toggle */}
          <button
            type="button"
            className="pp-btn c-navbar__toggle"
            aria-label="Open menu"
            aria-controls="mobile-menu"
            aria-expanded={isMenuOpen}
            onClick={() => setIsMenuOpen(true)}
          >
            <HiOutlineMenu />
          </button>
        </div>
      </div>

      {/* Mobile Drawer */}
      <div className={`c-navbar__overlay ${isMenuOpen ? "is-open" : ""}`} role="dialog" aria-modal="true">
        <div className="c-navbar__drawer" id="mobile-menu">
          <div className="c-navbar__drawer-header">
            <button
              type="button"
              className="pp-btn c-navbar__icon-btn"
              aria-label="Close menu"
              onClick={() => setIsMenuOpen(false)}
            >
              <IoClose />
            </button>
          </div>

          {/* Mobile profile */}
          {isAuthenticated && (
            <div className="c-navbar__profile-mobile">
              <div className="c-navbar__dropdown-header">{user?.firstName || user?.name}</div>
              <ul className="c-navbar__dropdown-menu">
                {userMenu.map((item) => (
                  <li key={item.label}>
                    {item.label === "Log Out" ? (
                      <button
                        onClick={() => {
                          handleLogout();
                          setIsMenuOpen(false);
                        }}
                        className="c-navbar__dropdown-link"
                      >
                        {item.icon && <span className="c-navbar__dropdown-icon">{item.icon}</span>}
                        {item.label}
                      </button>
                    ) : (
                      <Link
                        to={item.to}
                        className="c-navbar__dropdown-link"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {item.icon && <span className="c-navbar__dropdown-icon">{item.icon}</span>}
                        {item.label}
                        {item.balance && <span className="c-navbar__vendor-balance">${user.balance}</span>}
                        {item.badge && user.messages > 0 && (
                          <span className="c-navbar__badge">{user.messages}</span>
                        )}
                      </Link>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Mobile nav */}
          <ul className="c-navbar__mobile-links">
            {NAV_LINKS.map((item) => (
              <li key={`m-${item.label}`}>
                <Link className="c-navbar__mobile-link" to={item.to} onClick={() => setIsMenuOpen(false)}>
                  {item.label}
                </Link>
              </li>
            ))}
          </ul>

          {/* Mobile search */}
          <div className="navsearch-mobile">
            <input className="pp-navsearch__input" placeholder="Search..." />
            <IoSearch className="pp-navsearch__search-icon" />
          </div>

          {/* Mobile login/logout */}
          <div className="c-navbar__mobile-actions">
            {isAuthenticated ? (
              <button
                onClick={() => {
                  handleLogout();
                  setIsMenuOpen(false);
                }}
                className="pp-btn pp-btn-secondary"
              >
                Logout
              </button>
            ) : (
              <>
                <Link to="/login" className="pp-btn pp-btn-secondary" onClick={() => setIsMenuOpen(false)}>
                  Login
                </Link>
                <Link to="/signup" className="pp-btn pp-btn-primary" onClick={() => setIsMenuOpen(false)}>
                  Sign Up
                </Link>
              </>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}

export default memo(Navbar);
