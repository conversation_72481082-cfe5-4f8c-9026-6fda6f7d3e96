import React from "react";

export default function TravelFees({ value, onChange, category }) {
  const handleChange = (field, val) => {
    onChange({ ...value, [field]: val });
  };

  const handleToggle = (field) => {
    onChange({ ...value, [field]: !value[field] });
  };

  if (category === "food") {
    return (
      <div className="tf-section">
        <h3 className="tf-section-title">Travel Fees</h3>

        <div className="tf-form-group">
          <label className="tf-checkbox-wrapper">
            <input
              type="checkbox"
              className="tf-checkbox"
              checked={value.addTravelFee || false}
              onChange={() => handleToggle("addTravelFee")}
            />
            <span className="tf-checkbox-label">
              Add an additional fee for travel
            </span>
          </label>
        </div>

        {value.addTravelFee && (
          <>
            <div className="tf-form-group">
              <label className="tf-form-label">Cost Per Mile</label>
              <div className="tf-price-input">
                <span className="tf-currency">$</span>
                <input
                  type="number"
                  className="tf-form-input"
                  placeholder="0"
                  value={value.costPerMile || ""}
                  onChange={(e) => handleChange("costPerMile", e.target.value)}
                />
                <span className="tf-unit">/Mile</span>
              </div>
            </div>

            <div className="tf-form-group">
              <label className="tf-form-label">Free Travel Zone</label>
              <div className="tf-input-with-unit">
                <input
                  type="number"
                  className="tf-form-input"
                  placeholder="0"
                  value={value.freeTravelZone || ""}
                  onChange={(e) =>
                    handleChange("freeTravelZone", e.target.value)
                  }
                />
                <span className="tf-unit">miles</span>
              </div>
              <p className="tf-description">
                How many miles do you travel for free?
              </p>
            </div>

            <div className="tf-form-group">
              <label className="tf-form-label">Max Travel Distance</label>
              <div className="tf-input-with-unit">
                <input
                  type="number"
                  className="tf-form-input"
                  placeholder="0"
                  value={value.maxTravelDistance || ""}
                  onChange={(e) =>
                    handleChange("maxTravelDistance", e.target.value)
                  }
                />
                <span className="tf-unit">miles</span>
              </div>
              <p className="tf-description">How far do you travel?</p>
            </div>
          </>
        )}
      </div>
    );
  }

  if (category === "rental") {
    return (
      <div className="tf-section">
        <h3 className="tf-section-title">Travel Fees</h3>

        <div className="tf-form-group">
          <label className="tf-checkbox-wrapper">
            <input
              type="checkbox"
              className="tf-checkbox"
              checked={value.addTravelFee || false}
              onChange={() => handleToggle("addTravelFee")}
            />
            <span className="tf-checkbox-label">
              Add an additional travel fee
            </span>
          </label>
        </div>

        {value.addTravelFee && (
          <>
            <div className="tf-form-group">
              <label className="tf-form-label">Cost Per Mile</label>
              <div className="tf-price-input">
                <span className="tf-currency">$</span>
                <input
                  type="number"
                  className="tf-form-input"
                  placeholder="0"
                  value={value.costPerMile || ""}
                  onChange={(e) => handleChange("costPerMile", e.target.value)}
                />
                <span className="tf-unit">USD</span>
              </div>
            </div>

            <div className="tf-form-group">
              <label className="tf-form-label">Input Travel Zone</label>
              <input
                type="text"
                className="tf-form-input"
                placeholder="Enter travel zone, example: New York"
                value={value.travelZone || ""}
                onChange={(e) => handleChange("travelZone", e.target.value)}
              />
            </div>

            <div className="tf-form-group">
              <label className="tf-form-label">Description</label>
              <textarea
                className="tf-form-textarea"
                placeholder="Describe your travel fees..."
                value={value.description || ""}
                onChange={(e) => handleChange("description", e.target.value)}
                rows={3}
              />
              <div className="tf-char-count">0/200</div>
            </div>
          </>
        )}
      </div>
    );
  }

  // Default entertainment/music travel fees
  return (
    <div className="tf-section">
      <h3 className="tf-section-title">Travel Fees</h3>

      <div className="tf-form-group">
        <label className="tf-checkbox-wrapper">
          <input
            type="checkbox"
            className="tf-checkbox"
            checked={value.addTravelFee || false}
            onChange={() => handleToggle("addTravelFee")}
          />
          <span className="tf-checkbox-label">
            Add an additional fee for travel
          </span>
        </label>
      </div>

      {value.addTravelFee && (
        <>
          <div className="tf-form-group">
            <label className="tf-form-label">Cost Per Mile</label>
            <div className="tf-price-input">
              <span className="tf-currency">$</span>
              <input
                type="number"
                className="tf-form-input"
                placeholder="0"
                value={value.costPerMile || ""}
                onChange={(e) => handleChange("costPerMile", e.target.value)}
              />
              <span className="tf-unit">/Mile</span>
            </div>
          </div>

          <div className="tf-form-container">
            <div className="tf-form-group">
              <label className="tf-form-label">
                Free Travel Zone{" "}
                <p className="tf-description">
                  How many miles will workers travel for free?
                </p>
              </label>
              <div className="tf-input-with-unit">
                <input
                  type="number"
                  className="tf-form-input"
                  placeholder="0"
                  value={value.freeTravelZone || ""}
                  onChange={(e) =>
                    handleChange("freeTravelZone", e.target.value)
                  }
                />
                <span className="tf-unit">Miles</span>
              </div>
            </div>

            <div className="tf-form-group">
              <label className="tf-form-label">
                Max Travel Distance{" "}
                <p className="tf-description">How far is too far?</p>
              </label>

              <div className="tf-input-with-unit">
                <input
                  type="number"
                  className="tf-form-input"
                  placeholder="0"
                  value={value.maxTravelDistance || ""}
                  onChange={(e) =>
                    handleChange("maxTravelDistance", e.target.value)
                  }
                />
                <span className="tf-unit">Miles</span>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
