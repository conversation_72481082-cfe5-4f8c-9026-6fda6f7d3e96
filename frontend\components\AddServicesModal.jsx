import React, { useState } from "react";
import "../styles/AddServicesModal.css";
import EntertainmentImg from "../src/assets/images/AddEntertainment.svg";
import MusicImg from "../src/assets/images/Addmusic.svg";
import FoodImg from "../src/assets/images/AddFood.svg";
import RentalsImg from "../src/assets/images/AddRents.svg";
import { IoClose } from "react-icons/io5";

export default function AddServices({ isOpen, onClose, onSelect }) {
  const [active, setActive] = useState(null);

  if (!isOpen) return null;

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleSelect = (service) => {
    setActive(service);
    onSelect?.(service);
  };

  return (
    <div
      className="srv-modal-overlay"
      role="dialog"
      aria-modal="true"
      onClick={handleOverlayClick}
    >
      <div className="srv-modal">
        <div className="srv-modal-header">
          <span className="srv-modal-title">New Service</span>
          <button
            type="button"
            className="srv-modal-close"
            onClick={onClose}
            aria-label="Close"
          >
            <IoClose />
          </button>
        </div>
        <div className="srv-modal-subtitle">
          What type of service are you listing?
        </div>
        <div className="srv-modal-grid">
          <button
            type="button"
            className={`srv-card entertainment ${
              active === "entertainment" ? "active" : ""
            }`}
            onClick={() => handleSelect("entertainment")}
          >
            <img
              src={EntertainmentImg}
              alt="Entertainment"
              className="srv-card-icon"
            />
            <div className="srv-card-title">Entertainment</div>
            <div className="srv-card-desc">
              Lawn care, balloon art, character shows
            </div>
          </button>
          <button
            type="button"
            className={`srv-card music ${active === "music" ? "active" : ""}`}
            onClick={() => handleSelect("music")}
          >
            <img src={MusicImg} alt="Music" className="srv-card-icon" />
            <div className="srv-card-title">Music</div>
            <div className="srv-card-desc">DJs and live bands for events</div>
          </button>
          <button
            type="button"
            className={`srv-card food ${active === "food" ? "active" : ""}`}
            onClick={() => handleSelect("food")}
          >
            <img src={FoodImg} alt="Food" className="srv-card-icon" />
            <div className="srv-card-title">Food</div>
            <div className="srv-card-desc">
              Caterers, trucks, and dessert tables
            </div>
          </button>
          <button
            type="button"
            className={`srv-card rental ${active === "rental" ? "active" : ""}`}
            onClick={() => handleSelect("rental")}
          >
            <img src={RentalsImg} alt="Rentals" className="srv-card-icon" />
            <div className="srv-card-title">Rentals</div>
            <div className="srv-card-desc">
              Inflatables, tables, chairs, decor
            </div>
          </button>
        </div>
      </div>
    </div>
  );
}
