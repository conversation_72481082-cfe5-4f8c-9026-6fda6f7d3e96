import React, { useEffect, useMemo, useRef, useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { SlCalender } from "react-icons/sl";

import "../styles/CustomeCalender.css";

// Accessible custom date picker
// Props: value (Date|string), onChange(Date|string), placeholder, className, id
const CustomDatePicker = ({
  value,
  onChange,
  placeholder = "Select a date",
  className = "",
  id,
}) => {
  const initialDate = useMemo(() => {
    if (!value) return null;
    if (value instanceof Date) return value;
    const parsed = new Date(value);
    return isNaN(parsed.getTime()) ? null : parsed;
  }, [value]);

  const [selectedDate, setSelectedDate] = useState(initialDate || null);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [open, setOpen] = useState(false);
  const pickerRef = useRef(null);

  const [isEditingMonth, setIsEditingMonth] = useState(false);
  const [isEditingYear, setIsEditingYear] = useState(false);

  const daysOfWeek = ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"];
  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  const startOfMonth = new Date(
    currentMonth.getFullYear(),
    currentMonth.getMonth(),
    1
  );
  const endOfMonth = new Date(
    currentMonth.getFullYear(),
    currentMonth.getMonth() + 1,
    0
  );

  const prevMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1)
    );
  };

  const nextMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1)
    );
  };

  const handleDateClick = (day) => {
    const pickedDate = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth(),
      day
    );
    setSelectedDate(pickedDate);
    if (onChange) onChange(pickedDate);
    setOpen(false);
  };

  const handleMonthChange = (e) => {
    const newMonth = parseInt(e.target.value, 10);
    setCurrentMonth(new Date(currentMonth.getFullYear(), newMonth, 1));
    setIsEditingMonth(false);
  };

  const handleYearChange = (e) => {
    let newYear = parseInt(e.target.value, 10);
    if (!isNaN(newYear)) {
      setCurrentMonth(new Date(newYear, currentMonth.getMonth(), 1));
    }
  };

  const handleYearBlur = () => {
    setIsEditingYear(false);
  };

  const generateDays = () => {
    const days = [];
    const firstDayIndex = startOfMonth.getDay();

    // Empty slots before first day
    for (let i = 0; i < firstDayIndex; i++) {
      days.push(<div key={`empty-${i}`} className="calendar-day empty" />);
    }

    // Days of month
    for (let day = 1; day <= endOfMonth.getDate(); day++) {
      const isSelected =
        selectedDate &&
        selectedDate.getDate() === day &&
        selectedDate.getMonth() === currentMonth.getMonth() &&
        selectedDate.getFullYear() === currentMonth.getFullYear();

      days.push(
        <button
          key={day}
          onClick={() => handleDateClick(day)}
          className={`calendar-day ${isSelected ? "selected" : ""}`}
        >
          {day}
        </button>
      );
    }

    return days;
  };

  // Update selectedDate when value prop changes
  useEffect(() => {
    if (value !== selectedDate) {
      setSelectedDate(initialDate);
    }
  }, [value, initialDate, selectedDate]);

  // Close when clicking outside
  useEffect(() => {
    const handleClickOutside = (e) => {
      if (pickerRef.current && !pickerRef.current.contains(e.target)) {
        setOpen(false);
        setIsEditingMonth(false);
        setIsEditingYear(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const formattedValue = selectedDate
    ? selectedDate.toLocaleDateString(undefined, {
        year: "numeric",
        month: "short",
        day: "2-digit",
      })
    : "";

  const onKeyDownInput = (e) => {
    switch (e.key) {
      case "Enter":
      case " ":
      case "ArrowDown":
        e.preventDefault();
        setOpen(true);
        break;
      case "Escape":
        setOpen(false);
        break;
      default:
        break;
    }
  };

  return (
    <div className={`datepicker-container ${className}`} ref={pickerRef}>
      {/* Input box */}
      <div className="input-wrapper">
        <button
          id={id}
          type="button"
          className="datepicker-input"
          aria-haspopup="dialog"
          aria-expanded={open}
          aria-label="Choose date"
          onClick={() => setOpen((prev) => !prev)}
          onKeyDown={onKeyDownInput}
        >
          {formattedValue || (
            <span className="datepicker-placeholder">{placeholder}</span>
          )}
          <SlCalender className="datepicker-icon" />
        </button>
      </div>

      {/* Calendar */}
      {open && (
        <div
          className="calendar"
          role="dialog"
          aria-modal="true"
          aria-label="Date picker"
        >
          {/* Header */}
          <div className="calendar-header">
            <button className="nav-btn" onClick={prevMonth}>
              <ChevronLeft size={16} />
            </button>

            <div className="calendar-title">
              {isEditingMonth ? (
                <select
                  value={currentMonth.getMonth()}
                  onChange={handleMonthChange}
                  onBlur={() => setIsEditingMonth(false)}
                  autoFocus
                >
                  {months.map((m, i) => (
                    <option key={i} value={i}>
                      {m}
                    </option>
                  ))}
                </select>
              ) : (
                <span onClick={() => setIsEditingMonth(true)}>
                  {months[currentMonth.getMonth()]}
                </span>
              )}
              &nbsp;
              {isEditingYear ? (
                <input
                  type="number"
                  defaultValue={currentMonth.getFullYear()}
                  onBlur={handleYearBlur}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") handleYearBlur();
                  }}
                  onChange={handleYearChange}
                  autoFocus
                  style={{ width: "70px" }}
                />
              ) : (
                <span onClick={() => setIsEditingYear(true)}>
                  {currentMonth.getFullYear()}
                </span>
              )}
            </div>

            <button className="nav-btn" onClick={nextMonth}>
              <ChevronRight size={16} />
            </button>
          </div>

          {/* Weekdays */}
          <div className="calendar-weekdays">
            {daysOfWeek.map((day) => (
              <div key={day} className="weekday">
                {day}
              </div>
            ))}
          </div>

          {/* Days */}
          <div className="calendar-days">{generateDays()}</div>
        </div>
      )}
    </div>
  );
};

export default CustomDatePicker;
