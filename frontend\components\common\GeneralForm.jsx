import React from "react";
import Toggle from "./Toggle";
import "../../styles/GeneralForm.css"; // Import your custom styles

export default function GeneralForm({ formData, onChange }) {
  const titleMaxLength = 35;
  const descriptionMaxLength = 1800;

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    onChange({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  return (
    <div className="gf-general-form">
      <h2 className="gf-section-title">General Info</h2>

      {/* Title Field */}
      <div className="pp-form-group">
        <label htmlFor="title" className="pp-form-label">
          Title
        </label>
        <div className="pp-input-wrapper">
          <input
            id="title"
            name="title"
            type="text"
            className="pp-form-input"
            placeholder="Enter title"
            value={formData.title || ""}
            onChange={handleChange}
            maxLength={titleMaxLength}
            autoComplete="off"
          />
          <span className="pp-count">
            {titleMaxLength - (formData.title?.length || 0)}
          </span>
        </div>
      </div>

      {/* Description Field */}
      <div className="pp-form-group">
        <label htmlFor="description" className="pp-form-label">
          Description
        </label>
        <div className="pp-input-wrapper">
          <textarea
            id="description"
            name="description"
            className="pp-form-input"
            placeholder="Write description..."
            value={formData.description || ""}
            onChange={handleChange}
            maxLength={descriptionMaxLength}
            rows={6}
          />
          <span className="pp-count">
            {descriptionMaxLength - (formData.description?.length || 0)}
          </span>
        </div>
      </div>

      {/* Enable Customer Reviews */}
      <div className="pp-form-group">
        <label className="pp-form-label" htmlFor="enableReviews">
          Enable Customer Reviews
        </label>
        {/* Reusable Toggle Component */}
        <Toggle
          checked={formData.enableReviews}
          onChange={(checked) =>
            onChange({ ...formData, enableReviews: checked })
          }
        />
      </div>

      {/* Order Confirmation Email */}
      <div className="pp-form-group">
        <label htmlFor="orderConfirmation" className="pp-form-label">
          Order Confirmation Email
        </label>
        <span className="pp-helper-text">
          Share a message or instructions with your customer after they reserve
          your bookable.
        </span>
        <textarea
          id="orderConfirmation"
          name="orderConfirmation"
          className="pp-form-input"
          placeholder="Write confirmation email message..."
          value={formData.orderConfirmation || ""}
          onChange={handleChange}
          rows={4}
        />
      </div>
    </div>
  );
}
