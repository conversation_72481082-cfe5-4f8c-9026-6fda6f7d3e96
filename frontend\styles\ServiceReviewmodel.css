.pp-service-review-modal {
  position: fixed;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--pp-z-index-modal);
}

.pp-service-review-modal__backdrop {
  position: absolute;
  inset: 0;
  background: var(--pp-overlay-bg);
  z-index: 1;
}

.pp-service-review-modal__card {
  position: relative;
  background: var(--pp-clr-primary);
  border-radius: var(--pp-bor-rad-24);
  box-shadow: var(--pp-shdw-xl);
  width: 90vw;
  max-width: var(--pp-modal-max-width);
 
  z-index: 2;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  gap: var(--pp-sp-24);
  max-height: 95vh;
  overflow: auto;
}

.pp-service-review-modal__close {
  position: absolute;
  top: var(--pp-sp-16);
  right: var(--pp-sp-16);
  background: none;
  border: none;
  font-size: var(--pp-font-heading6);
  color: var(--pp-clr-secondary);
  cursor: pointer;
  z-index: 2;
}
.pp-service-review-modal__header  {
  border-bottom: 1px solid #D2D2D2;
  padding-bottom:var(--pp-font-base-font) ;

  
  margin-bottom: var(--pp-sp-8);
}
.pp-service-review-modal__header h2 {
  font-size: var(--pp-font-heading5);

  color: var(--pp-clr-secondary);
  
}

.pp-service-review-modal__summary {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-12);
  font-size: var(--pp-font-heading6);
}

.pp-service-review-modal__rating {
  font-size: var(--pp-font-heading5);
  color: black;
  font-weight: 600;
  font-family: Inter, sans-serif;
  margin-right: var(--pp-sp-8);
}

.pp-service-review-modal__list {
  display: flex;
  flex-direction: column;
 
  
  max-height: calc(95vh-250px);
  margin-bottom: var(--pp-sp-20);
}

.pp-service-review-modal__item {
  background: var(--pp-clr-primary);

  padding:20px 0;
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-4);
  border-bottom: 1px solid var(--pp-clr-border-light);
}

.pp-service-review-modal__item-header {
  display: flex;
  
  flex-direction: column;
  justify-content: space-between;
  
  font-size: var(--pp-font-heading5);
  margin-bottom: var(--pp-sp-4);
}

.pp-service-review-modal__item-author {
  color: var(--pp-clr-secondary);
  font-weight: bold;
}

.pp-service-review-modal__item-rating {
  display: flex;
  align-items: center;
  gap: 2px;
  color: var(--pp-clr-warning);
}

.pp-service-review-modal__item-date {
  color: var(--pp-clr-text-gray-light);
  font-size: var(--pp-font-extra-small);
}

.pp-service-review-modal__item-text {
  color: var(--pp-clr-text-main);
  font-size: var(--pp-font-base2-font);
  margin-top: var(--pp-sp-4);
}

.pp-service-review-modal__readmore {
  color: var(--pp-clr-blue-txt);
  text-decoration: underline;
  margin-left: var(--pp-sp-8);
  cursor: pointer;
  font-size: var(--pp-font-extra-small);
}

.pp-service-review-modal__readmorebtn {
  background: var(--pp-clr-secondary);
  color: var(--pp-clr-primary);
  font-size: var(--pp-font-base-font);
  border-radius: var(--pp-bor-rad-24);
  border: none;
  width: fit-content;
  padding: var(--pp-sp-10) var(--pp-sp-20);
  cursor: pointer;
}

.pp-service-review-modal__form {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-12);
  margin-top: var(--pp-sp-32);
  align-items: flex-start;
}

.pp-service-review-modal .pp-label {
  font-size: var(--pp-font-heading5);
  color: var(--pp-clr-secondary);
  
}

.pp-service-review-modal__form-rating {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-4);
  font-size: var(--pp-font-base2-font);

}

.pp-service-review-modal__form-clear {
  color: var(--pp-clr-blue-txt);
  font-size: var(--pp-font-extra-small);
  margin-left: var(--pp-sp-16);
  cursor: pointer;
  text-decoration: underline;
}

.pp-service-review-modal .sendbtn {
  position: absolute;
  top: 50%;
  right: 0%;
  background-color: var(--pp-clr-secondary);
  padding: 5px;
  border-radius: 50%;
  color: var(--pp-clr-primary);
  transform: translate(-50%, -50%);
}
.pp-service-review-modal .pp-input {
  border: 2px solid var(--pp-clr-border);
  border-radius: 6px;
  background: var(--pp-clr-primary);
  padding: var(--pp-sp-10) var(--pp-sp-16);
height: auto;
  color: var(--pp-clr-text-main);
  width: 100%;

}

@media (max-width: 580px) {
  .pp-service-review-modal__card {
    padding: var(--pp-sp-10);
    max-width: 98vw;
    height: 98vh;

    border-radius: 12px;
  }
  .pp-service-review-modal__list {
    max-height: calc(98vh-230px);
  }
  .pp-service-review-modal__item-header {
    display: grid;
    align-items: center;
    gap: 0px;
    font-size: var(--pp-font-small-font);
  }
}
