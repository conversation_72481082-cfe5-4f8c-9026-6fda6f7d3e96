.bookings-main {
  background: var(--pp-clr-bg);
}

.bookings-main__title {
  font-family: var(--pp-font-Playfair-Display);
  font-size: var(--pp-font-heading3);
  color: var(--pp-clr-text-main);
  margin-bottom: var(--pp-sp-20);
}

.bookings-main__tabs {
  display: flex;
  gap: var(--pp-sp-20);
  font-size: var(--pp-font-base2-font);
  color: var(--pp-clr-text-gray);
  border-bottom: 1px solid var(--pp-clr-border-light);
  margin-bottom: var(--pp-sp-16);
  align-items: center;
}

.bookings-main__link {
  margin-left: auto;
  color: var(--pp-clr-blue-txt);
  font-weight: 500;
  text-decoration: none;
}

.bookings-main__controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--pp-sp-24);
}

.bookings-main__controls .custom-select-box {
  min-width: 180px;
}

.bookings-main__viewtoggle {
  display: flex;
  gap: var(--pp-sp-8);
  border: 1px solid var(--pp-clr-border-light); 
  padding: 5px;
  border-radius: 6px;
}

.bookings-main__toggle-btn {
  background: none;
  border: none;
  font-size: var(--pp-font-base2-font);
  padding: 3px var(--pp-sp-8);
  border-radius: var(--pp-bor-rad-8);
  cursor: pointer;
  color: var(--pp-clr-text-gray);
  transition: background-color var(--pp-trans-duration-fast),
    color var(--pp-trans-duration-fast);
    width: 36px;
    height: 36px;
    display: flex
;
    justify-content: center;
    align-items: center;
}

.bookings-main__toggle-btn.active {
  background: var(--pp-clr-secondary);
  color: var(--pp-clr-primary);
}

/* Booking Table */
.bookings-table__wrapper {
  overflow-x: auto;
  border-radius: var(--pp-bor-rad-12);
  box-shadow: var(--pp-shdw-sm);
  border: 1px solid var(--pp-clr-border);
}

.bookings-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-family: var(--pp-font-Metro-Sans);
}

.bookings-table thead tr {
  background: var(--pp-clr-primary);
}

.bookings-table th,
.bookings-table td {
  font-size: var(--pp-font-extra-small);
  padding: var(--pp-sp-16) var(--pp-sp-12);
  border-bottom: 1px solid var(--pp-clr-border-light);
  text-align: left;
}

.bookings-table tr:last-child th,
.bookings-table tr:last-child td {
  border-bottom: none;
}

/* Date block style */
.bookings-table__date-block {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  align-items: center;
  border-radius: var(--pp-bor-rad-8);
  background: var(--pp-clr-primary);
  color: var(--pp-clr-text-main);
  font-weight: 600;
  margin-bottom: var(--pp-sp-8);
}

.bookings-table__date-month {
  font-size: var(--pp-font-small-font);
}

.bookings-table__date-day {
  font-size: var(--pp-font-heading6);
}

.bookings-table__date-time {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  display: flex;
  align-items: center;
}

/* Calendar Styles */
.bookings-calendar__container {
  background: var(--pp-clr-primary);
  border-radius: var(--pp-bor-rad-12);
  padding: var(--pp-sp-24);
  box-shadow: var(--pp-shdw-sm);
  border: 1px solid var(--pp-clr-border);
}

.bookings-calendar__controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--pp-sp-24);
  margin-bottom: var(--pp-sp-24);
  font-size: var(--pp-font-base2-font);
  color: var(--pp-clr-text-main);
}

.bookings-calendar__grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: var(--pp-sp-12);
}

.bookings-calendar__weekday {
  color: var(--pp-clr-text-gray);
  text-align: center;
  font-size: var(--pp-font-extra-small);
  padding-bottom: var(--pp-sp-8);
  font-weight: 500;
}

.bookings-calendar__cell {
  min-height: 80px;
  border-radius: var(--pp-bor-rad-8);
  background: var(--pp-clr-primary);
  color: var(--pp-clr-text-main);
  font-size: var(--pp-font-extra-small);
  padding: var(--pp-sp-8);
  border: 1px solid var(--pp-clr-border-light);
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-4);
}

.bookings-calendar__date {
  font-weight: 600;
}

.bookings-calendar__booking {
  border-radius: var(--pp-bor-rad-4);
  padding: var(--pp-sp-4) var(--pp-sp-8);
  background: var(--pp-clr-guarantee-section-bg);
  color: var(--pp-clr-secondary);
  font-weight: 500;
  margin-top: var(--pp-sp-4);
}
