/* =========================
   🚗 Travel Fees
   ========================= */

.tf-section {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-24);
}

.tf-section-title {
  font-size: var(--pp-font-heading6);
  font-weight: 600;
  color: var(--pp-clr-text-main);
  margin: 0;
  line-height: 1.3;
}

/* Form Groups */
.tf-form-group {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-12);
}
.tf-form-container {
  display: grid;
  grid-template-columns: 375px 1fr;
  gap: 10px;
}
.tf-form-label {
  font-size: var(--pp-font-extra-small);
  font-weight: 500;
  color: var(--pp-clr-text-main);
  margin: 0;
}

.tf-form-input {
  width: 100px;
  padding: var(--pp-sp-12) var(--pp-sp-16);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-8);
  font-size: var(--pp-font-extra-small);
  font-family: var(--pp-font-Metro-Sans);
  background: var(--pp-clr-primary);
  transition: all 0.3s ease;
  color: var(--pp-clr-text-main);
}

.tf-form-input:focus {
  outline: none;
  border-color: var(--pp-clr-secondary);
}

.tf-form-input::placeholder {
  color: var(--pp-clr-text-gray);
}

.tf-form-textarea {
  width: 100%;
  padding: var(--pp-sp-12) var(--pp-sp-16);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-8);
  font-size: var(--pp-font-extra-small);
  font-family: var(--pp-font-Metro-Sans);
  background: var(--pp-clr-primary);
  transition: all 0.3s ease;
  color: var(--pp-clr-text-main);
  resize: vertical;
  min-height: var(--pp-textarea-min-height);
}

.tf-form-textarea:focus {
  outline: none;
  border-color: var(--pp-clr-secondary);
}

.tf-form-textarea::placeholder {
  color: var(--pp-clr-text-gray);
}

/* Price Inputs */
.tf-price-input {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8);
  position: relative;
}

.tf-currency {
  font-size: var(--pp-font-extra-small);
  font-weight: 600;
  color: var(--pp-clr-text-main);
  margin-right: var(--pp-sp-4);
  position: absolute;
  left: var(--pp-sp-16);
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}
.tf-price-input .tf-form-input {
  padding-left: calc(var(--pp-sp-16) + 14px);
}

.tf-unit {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  margin-left: var(--pp-sp-4);
}

/* Input with Unit */
.tf-input-with-unit {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8);
}

.tf-input-with-unit .tf-unit {
  white-space: nowrap;
  min-width: 60px;
}

/* Descriptions */
.tf-description {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  margin: var(--pp-sp-8) 0 0 0;
  line-height: 1.5;
}

/* Checkboxes */
.tf-checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-12);
  cursor: pointer;
  user-select: none;
}

.tf-checkbox {
  position: relative;
  width: var(--pp-sp-20);
  height: var(--pp-sp-20);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-4);
  background: var(--pp-clr-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.tf-checkbox:hover {
  border-color: var(--pp-clr-text-gray);
  transform: scale(1.05);
}

.tf-checkbox:focus {
  outline: none;
  border-color: var(--pp-clr-secondary);
}

.tf-checkbox:checked {
  background-color: var(--pp-clr-secondary) !important;
  border-color: var(--pp-clr-secondary) !important;
}

.tf-checkbox:checked::after {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: var(--pp-sp-12);
  height: var(--pp-sp-12);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.tf-checkbox-label {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-main);
  font-weight: 500;
  cursor: pointer;
}

/* Character Count */
.tf-char-count {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  text-align: right;
  margin-top: var(--pp-sp-4);
}

/* Responsive */
@media (max-width: 768px) {
  .tf-section {
    gap: var(--pp-sp-20);
  }

  .tf-form-group {
    gap: var(--pp-sp-10);
  }

  .tf-input-with-unit {

    gap: var(--pp-sp-8);
  }

  .tf-input-with-unit .tf-unit {
    text-align: center;
    margin: 0;
  }
  .tf-form-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .tf-section {
    gap: var(--pp-sp-16);
  }

  .tf-form-group {
    gap: var(--pp-sp-8);
  }

  .tf-form-input,
  .tf-form-textarea {
    padding: var(--pp-sp-10) var(--pp-sp-12);
  }
}
