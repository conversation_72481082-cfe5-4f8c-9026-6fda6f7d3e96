import React, { useState, useEffect } from "react";
import CustomSelect from "../components/CustomeSelect";
import { NavLink, useLocation, useNavigate, Outlet } from "react-router-dom";
import "../styles/VendorLayout.css";
import { HiOutlineBuildingStorefront } from "react-icons/hi2";

const tabs = [
  { label: "Dashboard", path: "/vendor" },
  { label: "Bookings", path: "/vendor/bookings" },
  { label: "Messages", path: "/vendor/messages" },
  { label: "Services", path: "/vendor/services" },
  { label: "Reports", path: "/vendor/reports" },
  { label: "Reviews", path: "/vendor/reviews" },
  { label: "Travel", path: "/vendor/travel" },
  { label: "Balance", path: "/vendor/balance" },
];

const VendorLayout = () => {
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 1024);
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    const handleResize = () => setIsMobile(window.innerWidth <= 1024);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Determine current tab from location
  const currentTab =
    tabs.find((tab) => location.pathname === tab.path) || tabs[0];
  // Always render shell; CSS will hide it on small screens when chat is active

  return (
    <div className="vendor-layout pp-max-container">
      <div className="mb-8">
        <h2 className="vendor-layout__title">Nero's Heroes</h2>
        <div className="vendor-layout__nav-row">
          {isMobile ? (
            <CustomSelect
              className="vendor-layout__dropdown"
              value={currentTab.path}
              onChange={(val) => navigate(val)}
              options={tabs.map((t) => ({ label: t.label, value: t.path }))}
            />
          ) : (
            <nav className="vendor-layout__tabs">
              {tabs.map((tab) => (
                <NavLink
                  key={tab.path}
                  to={tab.path}
                  end={tab.path === "/vendor"}
                  className={({ isActive }) =>
                    `vendor-layout__tab${
                      isActive ? " vendor-layout__tab--active" : ""
                    }`
                  }
                >
                  {tab.label}
                </NavLink>
              ))}
            </nav>
          )}
          <a className="vendor-layout__link" href="#">
            <HiOutlineBuildingStorefront />{" "}
            <span className="underline">My Storefront</span>
          </a>
        </div>
      </div>
      <Outlet />
    </div>
  );
};

export default VendorLayout;
