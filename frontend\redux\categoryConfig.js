// src/config/categoryConfig.js

export const categoryConfig = {
  entertainment: {
    pricing: {
      fields: [
        { label: "Hourly Rate", name: "hourlyRate", type: "currency" },
        { label: "Multiple Workers", name: "multipleWorkers", type: "toggle" },
        { label: "Travel Fees", name: "travelFees", type: "toggle" },
      ],
      multipleWorkers: true,
      travelFees: true,
      addons: true,
    },
    scheduling: {
      duration: true,
      availability: true,
      blackoutDates: true,
    },
  },

  music: {
    pricing: {
      pricingMethods: ["Hourly Rate", "Packages"],
      fields: [
        { label: "Hourly Rate", name: "hourlyRate", type: "currency" },
        { label: "Travel Fees", name: "travelFees", type: "toggle" },
      ],
      travelFees: true,
      addons: true,
    },
    scheduling: {
      duration: true,
      availability: true,
      blackoutDates: true,
    },
  },

  food: {
    pricing: {
      menuItems: true,
      travelFees: true,
      addons: true,
    },
    scheduling: {
      duration: true,
      availability: true,
      blackoutDates: true,
    },
  },

  rental: {
    pricing: {
      pricingMethods: ["Hourly Rate", "Packages"],
      taxable: true,
      travelFees: true,
      addons: true,
    },
    scheduling: {
      duration: true,
      availability: true,
      blackoutDates: true,
    },
  },
};
