import React from "react";

export default function Toggle({
  checked,
  onChange,
  label,
  description,
  disabled = false,
}) {
  const handleToggle = () => {
    if (!disabled) {
      onChange(!checked);
    }
  };

  return (
    <div className="pp-toggle-container">
      <label className="pp-toggle-wrapper">
        <input
          type="checkbox"
          className="pp-toggle-input"
          checked={checked}
          onChange={handleToggle}
          disabled={disabled}
        />
        <span
          className={`pp-toggle-switch ${
            checked ? "pp-toggle-switch-on" : "pp-toggle-switch-off"
          } ${disabled ? "pp-toggle-switch-disabled" : ""}`}
        >
          <span className="pp-toggle-handle">
            {checked && (
              <svg
                width="12"
                height="12"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="pp-toggle-checkmark"
              >
                <path
                  d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"
                  fill="currentColor"
                />
              </svg>
            )}
          </span>
        </span>
        {label && (
          <span className="pp-toggle-label">
            {label}{" "}
            {description && (
              <p className="pp-toggle-description">{description}</p>
            )}
          </span>
        )}
      </label>
    </div>
  );
}
