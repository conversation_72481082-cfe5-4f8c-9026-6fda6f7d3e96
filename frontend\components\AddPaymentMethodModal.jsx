import React, { useState } from "react";
import PopupModal from "./common/PopupModal";
import "../styles/AddPaymentMethodModal.css";
import paypalicon from "../src/assets/images/paypal.svg";

export default function AddPaymentMethodModal({ isOpen, onClose, onSave }) {
  const [form, setForm] = useState({
    method: "Credit card",
    cardNumber: "",
    expMonth: "",
    expYear: "",
    cvv: "",
  });

  function handleChange(e) {
    const { name, value } = e.target;
    setForm((f) => ({ ...f, [name]: value }));
  }

  function handleSubmit(e) {
    e.preventDefault();
    // Wire up API logic here, then close modal or show result
    if (onSave) onSave(form);
    onClose();
  }

  if (!isOpen) return null;
  return (
    <PopupModal isOpen={isOpen} onClose={onClose}>
      <form className="AddPaymentMethodModal-form" onSubmit={handleSubmit}>
        <h2 className="AddPaymentMethodModal-title">Add Payment Method</h2>
        <div className="AddPaymentMethodModal-divider" />
        <div className="pp-form-group">
          <label className="pp-form-label">Payment Method</label>
          <input
            className="pp-form-input !w-fit"
            name="method"
            value={form.method}
            readOnly
          />
        </div>
        <div className="pp-form-group">
          <label className="pp-form-label">Credit Card Number</label>
          <div className="AddPaymentMethodModal-cardNumberWrap !w-fit h-[44px]">
            <img src={paypalicon} alt="Visa" className="AddPaymentMethodModal-cardIcon" />
            <input
              className="pp-form-input AddPaymentMethodModal-cardNumberInput"
              name="cardNumber"
              value={form.cardNumber}
              onChange={handleChange}
              maxLength={19}
              placeholder="5712-4872-6443-8719"
              inputMode="numeric"
              autoComplete="cc-number"
              required
            />
          </div>
        </div>
        <div className="AddPaymentMethodModal-row !w-fit">
          <div className="pp-form-group AddPaymentMethodModal-expGroup !w-[60%]">
            <label className="pp-form-label">Expiration Date</label>
            <div className="AddPaymentMethodModal-expInputs">
              <input
                className="pp-form-input !w-[60px]"
                type="text"
                name="expMonth"
                value={form.expMonth}
                onChange={handleChange}
                placeholder="06"
                maxLength={2}
                inputMode="numeric"
                required
                autoComplete="cc-exp-month"
              />
              <span className="AddPaymentMethodModal-slash">/</span>
              <input
                className="pp-form-input !w-[90px]"
                type="text"
                name="expYear"
                value={form.expYear}
                onChange={handleChange}
                placeholder="2028"
                maxLength={4}
                inputMode="numeric"
                required
                autoComplete="cc-exp-year"
              />
            </div>
          </div>
          <div className="pp-form-group AddPaymentMethodModal-cvvGroup">
            <label className="pp-form-label">
              CVV <span title="Card Verification Value" className="AddPaymentMethodModal-cvvTip">ⓘ</span>
            </label>
            <input
              className="pp-form-input !w-[100px]"
              type="text"
              name="cvv"
              value={form.cvv}
              onChange={handleChange}
              placeholder="775"
              maxLength={4}
              required
              inputMode="numeric"
              autoComplete="cc-csc"
            />
          </div>
        </div>
        <div className="AddPaymentMethodModal-divider" />
        <button className="pp-btn pp-btn-primary AddPaymentMethodModal-btn" type="submit">
          Save Payment Method
        </button>
      </form>
    </PopupModal>
  );
}
