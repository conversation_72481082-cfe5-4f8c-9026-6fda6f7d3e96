.booking-details-modal__overlay {
  position: fixed;
  inset: 0;
  z-index: var(--pp-z-index-modal);
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
}

.booking-details-modal {
  background: var(--pp-clr-primary);
  border-radius: var(--pp-bor-rad-12);
  box-shadow: var(--pp-shdw-lg);  
  
  max-width: 900px;
  width: 90%;
  max-height: 90vh;
  font-family: var(--pp-font-Metro-Sans);
  color: var(--pp-clr-text-main);
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-32);
  min-width: 340px;
  overflow:hidden ;
}
.overflow-containerbooking{
  overflow: auto;
  padding: var(--pp-sp-40) var(--pp-sp-32) var(--pp-sp-32) var(--pp-sp-32);
   width: 100%;
}
.booking-details-modal__close-btn {
  position: absolute;
  top: 0;
  right: 5px;
  background: transparent;
  border: none;
  color: var(--pp-clr-text-main);
  cursor: pointer;
  z-index: 10;
  padding: var(--pp-sp-4);
  display: flex;
  align-items: center;
  justify-content: center;
}

.booking-details-modal__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--pp-sp-20);
  margin-bottom: var(--pp-sp-8);
}

.booking-details-modal__title {
  font-size: var(--pp-font-heading4);
  color: var(--pp-clr-text-main);
  font-weight: 400;
  margin-bottom: var(--pp-sp-8);
  margin-top: 0;
}

.booking-details-modal__status {
  font-size: var(--pp-font-small-font);

  margin-bottom: var(--pp-sp-2);
}

.booking-details-modal__meta a {
  color: var(--pp-clr-blue-txt);

  font-weight: 500;
  font-size: var(--pp-font-extra-small);
}

.booking-details-modal__action-btn {
  padding: var(--pp-sp-10) var(--pp-sp-24);
  font-size: var(--pp-font-small-font);
  border-radius: var(--pp-bor-rad-24);
  background: #cbe5ff;
  color: #11538f;
  border: none;
  box-shadow: var(--pp-shdw-sm);
  cursor: pointer;
  align-self: flex-start;
  transition: background 200ms;
  white-space: nowrap;
}

.booking-details-modal__action-btn:hover {
  transform: scale(1.008);
  transition: all 0.1s ease;
}

/* Details Section */
.booking-details-modal__details {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-12);
}

.booking-details-modal__section-title {
  font-size: var(--pp-font-heading6);
  font-weight: 500;
  margin-bottom: var(--pp-sp-8);
  color: var(--pp-clr-text-main);
}

.booking-details-modal__details-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--pp-sp-40);
  align-items: flex-start;
}

.booking-details-modal__label {
  font-weight: 600;
  color: var(--pp-clr-text-main);
}

.booking-details-modal__artists {
  display: flex;
  gap: var(--pp-sp-16);
  justify-content: flex-end;
}

.booking-details-modal__artist {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--pp-sp-4);
}

.booking-details-modal__artist img {
  width: var(--pp-sp-40);
  height: var(--pp-sp-40);
  border-radius: var(--pp-bor-rad-round);
  object-fit: cover;
  background: var(--pp-clr-border-light);
  border: 2px solid var(--pp-clr-border);
}

/* Event map dummy block */
.booking-details-modal__location {
  margin-top: var(--pp-sp-16);
}

.booking-details-modal__map {
  width: 100%;
  height: 400px;
  background: var(--pp-clr-border-light);
  border-radius: var(--pp-bor-rad-8);
  overflow: hidden;
  margin-top: var(--pp-sp-8);
  border: 1px solid var(--pp-clr-border-light);
}

/* Payment Info Table */
.booking-details-modal__payment{
  margin-top: 2rem;
}
.booking-details-modal__payment-table {
  max-width: 100%;
  width: fit-content;
  background: var(--pp-clr-primary);
  border-radius: var(--pp-bor-rad-8);
  box-shadow: none;
  display: grid;
  grid-template-columns: 1fr;
  font-size: var(--pp-font-extra-small);

  overflow-x: auto;
}

.booking-details-modal__row {
  display: grid;
  grid-template-columns: 300px 1fr 1fr 1fr;
  padding:4px 0;
  color: var(--pp-clr-secondary);
  gap: var(--pp-sp-20);
}
.booking-details-modal__row span{
  white-space: nowrap;


}
.booking-details-modal__header {
  font-weight: 600;
  border-bottom: 1px solid var(--pp-clr-border-light);
}

.booking-details-modal__footer {
  font-weight: 700;
}

.booking-details-modal__divider {
  height: 1px;
  width: 100%;
  background: var(--pp-clr-border);
  margin: var(--pp-sp-10) 0;
}

.booking-details-modal__refund {
  font-size: var(--pp-font-extra-small);
  margin-top: var(--pp-sp-8);
  text-align: end;
  max-width: 637px;
}

.booking-details-modal__refund a {
  color: var(--pp-clr-blue-txt);
  text-decoration: underline;
}

/* History Section */

.booking-details-modal__history span {
  font-size: var(--pp-font-small-font);
  font-weight: 600;
}
.booking-details-modal__artist-avatar img {
  width: 120px;
  height: 96px;
  border-radius: 6px;
}
@media (max-width: 900px) {
  .booking-details-modal {
    max-width: 98vw;
 
  }
  .booking-details-modal__details-grid {
    grid-template-columns: 1fr;
    gap: var(--pp-sp-24);
  }
  .booking-details-modal__artists {
    justify-self: start;
  }
}

@media (max-width: 550px) {
  .booking-details-modal {
    border-radius: var(--pp-bor-rad-8);
  
    min-width: unset;
  }
  .booking-details-modal__map {
    height: 180px;
  }
  .booking-details-modal__header {
    flex-direction: column;
  }
  .overflow-containerbooking{
    padding: 10px;
  }
  .booking-details-modal__action-btn{
    margin-bottom: 1rem;
  }
  .booking-details-modal__row {
 
    grid-template-columns: 150px 1fr 1fr 1fr;
  
  }
}
