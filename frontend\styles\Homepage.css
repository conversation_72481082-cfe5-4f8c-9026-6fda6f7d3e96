/* .herosection */
.herosection {
  text-align: center;
  margin: var(--pp-section-padding) 0 180px 0;
  padding: 0 var(--pp-sp-24);
  margin: 120px 0;
}

.herosection h1 {
  font-family: var(--pp-font-Playfair-Display);
  font-size: var(--pp-font-heading1);
  font-weight: 700;
  color: var(--pp-clr-text-main);
  margin: 0 0 var(--pp-sp-32) 0;
  line-height: 1.1;
}

.herosection p {
  font-family: var(--pp-font-Metro-Sans);
  font-size: var(--pp-font-heading4);
  font-weight: 400;
  color: var(--pp-clr-text-main);
  margin: 0;
  line-height: 1.3;
}

/* category section */
.gridlayout {
  font-family: var(--pp-font-Playfair-Display);
  font-size: var(--pp-font-heading3);
  font-weight: 700;
  margin: 0 0 var(--pp-section-padding) 0;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  justify-items: center;
  align-items: start;
  padding: 60px var(--pp-sp-24) 0 var(--pp-sp-24);
  margin-bottom: 120px;
}
@media (min-width: 1024px) {
  .gridlayout > *:nth-child(3n + 2) {
    transform: translateY(-60px);
  }
}

.gridcard {
  position: relative;
  width: 100%;
  min-width: 100%;
  height: 400px;
  border-radius: var(--pp-bor-rad-16);
  overflow: hidden;
  padding: var(--pp-sp-24);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  transition: transform 300ms ease;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

/* Category Card Specific Styling */
.category-card.characters-card {
  color: var(--pp-clr-primary);
}
.category-card.entertainment-card {
  color: var(--pp-clr-text-main);
}
.category-card.rentals-card {
  color: var(--pp-clr-text-main);
}
.category-card.food-card {
  color: var(--pp-clr-text-main);
}
.category-card.music-card {
  color: var(--pp-clr-text-main);
}
.category-card.browse-card {
  color: var(--pp-clr-text-main);
  border: 1px solid var(--pp-clr-border);
  background-color: var(--pp-clr-primary);
  height: fit-content;
}

/* Mosaic stagger: up-down pattern across 3 columns */

.gridcard p {
  font-weight: 700;
  display: flex;
  justify-content: space-between;
  gap: 10px;
  width: 100%;
  align-items: flex-start;
}

/* guarantee section styles moved to GuaranteeSection.css */
/* trusted section*/
.trustedby {
  padding: 60px var(--pp-sp-24) 0px var(--pp-sp-24);
  text-align: center;
  margin: 0 0 var(--pp-section-padding) 0;
}

.trustedby p {
  font-family: var(--pp-font-Metro-Sans);
  font-size: var(--pp-font-heading3);
  font-weight: 600;
  color: var(--pp-clr-text-main);
  margin: 0 0 var(--pp-sp-40) 0;
}

.trustedby .logos-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 80px;
  flex-wrap: wrap;
}

.trustedby img {
  height: var(--pp-sp-55);
  width: auto;
  object-fit: contain;
  transition: transform var(--pp-trans-duration-fast) var(--pp-trans-ease);
}

.trustedby img:hover {
  transform: scale(1.05);
}

/* features section */
.features {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  align-items: start;
  justify-items: center;
  margin: 120px 0;
  padding: 0 var(--pp-sp-24);
  gap: 0;
  position: relative;
}

.featureItem {
  position: relative;
  text-align: center;
  width: 100%;
  padding: var(--pp-sp-24);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}

.featureItem:not(:last-child)::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  width: 1px;
  height: 180px;
  background-color: var(--pp-clr-secondary);
}

.featureItem .innerfeatureItem {
  width: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--pp-sp-16);
}

.featureIcon {
  width: 128px;
  height: auto;
  display: block;
  transition: transform 300ms ease;
}

.featureIcon:hover {
  transform: scale(1.05);
}

.featureText {
  margin: 0;
  font-family: var(--pp-font-Metro-Sans);
  font-size: var(--pp-font-heading5);
  color: var(--pp-clr-text-main);
  line-height: 1.35;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .herosection {
    margin: 80px 0 120px 0;
  }

  .gridlayout {
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
    margin-bottom: 120px;
    padding: 0 var(--pp-sp-24) 0 var(--pp-sp-24);
  }

  /* guarantee section styles moved to GuaranteeSection.css */

  .trustedby {
    padding: var(--pp-sp-60) var(--pp-sp-20) 0 var(--pp-sp-20);
  }

  .trustedby .logos-container {
    gap: var(--pp-sp-60);
  }

  .features {
    margin: var(--pp-sp-60) 0 100px 0;
  }
}

@media (max-width: 767px) {
  .herosection {
    margin: var(--pp-sp-60) 0 80px 0;
    padding: 0 var(--pp-sp-16);
  }

  .gridlayout {
    grid-template-columns: 1fr;
    gap: 24px;
    margin-bottom: 80px;
    padding: 0 var(--pp-sp-16);
  }

  .gridcard {
    height: 320px;
  }

  /* guarantee section styles moved to GuaranteeSection.css */

  .trustedby {
    padding: var(--pp-sp-48) var(--pp-sp-16) 0 var(--pp-sp-16);
    margin: 0 0 80px 0;
  }

  .trustedby .logos-container {
    gap: var(--pp-sp-40);
  }

  .trustedby img {
    height: var(--pp-sp-40);
  }

  .features {
    grid-template-columns: 1fr;
    gap: var(--pp-sp-32);
    margin: 0 0 80px 0;
    padding: var(--pp-sp-16);
  }

  .featureItem:not(:last-child)::after {
    content: "";
    position: absolute;
    top: 112%;
    right: 50%;
    transform: translate(50%, 0%);
    width: 100%;
    height: 1px;
    background-color: var(--pp-clr-secondary);
  }

  .featureItem .innerfeatureItem {
    width: 100%;
    max-width: 200px;
  }
  .featureItem {
    padding: 0 var(--pp-sp-24);
  }
}
@media (max-width: 500px) {
  /* guarantee section styles moved to GuaranteeSection.css */
}
@media (max-width: 414px) {
  .herosection {
    margin: var(--pp-sp-40) 0 var(--pp-sp-60) 0;
    padding: 0;
  }
  .herosection p {
    font-size: var(--pp-font-heading6);
  }
  .gridlayout {
    margin-bottom: var(--pp-sp-60);
    padding: 0 var(--pp-sp-12);
  }

  .gridcard {
    height: 280px;
  }

  /* guarantee section styles moved to GuaranteeSection.css */

  .trustedby {
    padding: var(--pp-sp-32) var(--pp-sp-12) 0 var(--pp-sp-12);
    margin-bottom: var(--pp-sp-60);
  }

  .trustedby .logos-container {
    gap: 25px;
    flex-wrap: wrap;
  }
  .trustedby p {
    margin: 0 0 20px 0;
  }
  .trustedby img {
    height: 24px;
  }

  .features {
    margin: 0;
    margin-bottom: var(--pp-sp-60);
    padding: 0 var(--pp-sp-12);
  }
  .featureItem {
    padding: 0 var(--pp-sp-24);
  }
  .featureIcon {
    width: auto;
    height: 55px;
  }
}
