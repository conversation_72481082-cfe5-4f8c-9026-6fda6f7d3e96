import React, { useState } from "react";
import { FaTimes } from "react-icons/fa";
import "../styles/ReviewPopup.css";

function ReviewPopup({ review, onClose, onSubmitComment }) {
  const [comment, setComment] = useState("");

  const handleSubmit = (e) => {
    e.preventDefault();
    if (comment.trim()) {
      onSubmitComment(comment);
      setComment("");
      onClose();
    }
  };

  if (!review) return null;

  return (
    <div className="rev-popup-overlay">
      <div className="rev-popup-modal">
        <button className="rev-popup-close" onClick={onClose}>
          <FaTimes />
        </button>
        <div className="rev-popup-header">
          <span className="rev-popup-user">{review.user}</span>
          <span className="rev-popup-date">{review.date}</span>
        </div>
        <div className="rev-popup-text">{review.text}</div>
        <form className="rev-popup-form" onSubmit={handleSubmit}>
          <label htmlFor="public-comment" className="rev-popup-label">
            Public Comment
          </label>
          <textarea
            id="public-comment"
            className="rev-popup-input"
            placeholder="Add your comment..."
            value={comment}
            onChange={(e) => setComment(e.target.value)}
          />
          <button type="submit" className="rev-popup-submit">
            Submit
          </button>
        </form>
      </div>
    </div>
  );
}

export default ReviewPopup;
