import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { FiCreditCard, FiTrash2, FiStar } from "react-icons/fi";
import AddPaymentMethodModal from "../components/AddPaymentMethodModal";
import { useAuth } from "../redux/useAuth";
import { paymentAPI } from "../redux/apiUtils";
import "../styles/CustomerPaymentMethods.css";
import paypalicon from "../src/assets/images/paypal.svg"
import { Link } from "react-router-dom";
import verified from "../src/assets/images/verified.svg"

const PAYMENT_METHODS = [
  {
    id: 1,
    type: "Checking",
    icon: paypalicon, // Replace with actual bank icon path
    text: "Checking ••••5155",
    role: "Primary",
    isVerified: true,
    isPrimary: true,
  },
  {
    id: 2,
    type: "PayPal",
    icon: paypalicon, // Replace with actual PayPal icon path
    text: "PayPal (c••••••aq•••.com )",
    role: "Secondary",
    isVerified: true,
    isPrimary: false,
  },
];

export default function CustomerPaymentMethods() {
    const dispatch = useDispatch();
    const { user, isAuthenticated } = useAuth();

    // State management
    const [isModalOpen, setModalOpen] = useState(false);
    const [paymentMethods, setPaymentMethods] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [actionLoading, setActionLoading] = useState({});

    // Fetch payment methods from API
    const fetchPaymentMethods = async () => {
      try {
        setError(null);
        const response = await paymentAPI.getPaymentMethods();
        if (response.data?.success) {
          setPaymentMethods(response.data.data.paymentMethods || []);
        }
      } catch (err) {
        console.error('Failed to fetch payment methods:', err);
        setError(err.response?.data?.message || 'Failed to load payment methods');
      } finally {
        setLoading(false);
      }
    };

    // Load payment methods on component mount
    useEffect(() => {
      if (isAuthenticated && user?.role === 'customer') {
        fetchPaymentMethods();
      } else {
        setLoading(false);
      }
    }, [isAuthenticated, user]);

    // Handle adding new payment method
    const handleSave = async (data) => {
      try {
        setError(null);
        const response = await paymentAPI.addPaymentMethod(data);
        if (response.data?.success) {
          await fetchPaymentMethods(); // Refresh the list
          setModalOpen(false);
        }
      } catch (err) {
        console.error('Failed to add payment method:', err);
        setError(err.response?.data?.message || 'Failed to add payment method');
      }
    };

    // Handle removing payment method
    const handleRemove = async (paymentMethodId) => {
      if (!window.confirm('Are you sure you want to remove this payment method?')) {
        return;
      }

      try {
        setActionLoading(prev => ({ ...prev, [paymentMethodId]: true }));
        setError(null);

        const response = await paymentAPI.removePaymentMethod(paymentMethodId);
        if (response.data?.success) {
          await fetchPaymentMethods(); // Refresh the list
        }
      } catch (err) {
        console.error('Failed to remove payment method:', err);
        setError(err.response?.data?.message || 'Failed to remove payment method');
      } finally {
        setActionLoading(prev => ({ ...prev, [paymentMethodId]: false }));
      }
    };

    // Handle making payment method primary
    const handleMakePrimary = async (paymentMethodId) => {
      try {
        setActionLoading(prev => ({ ...prev, [paymentMethodId]: true }));
        setError(null);

        // This would require a backend endpoint to set default payment method
        // For now, just show a message
        window.alert('Make Primary feature will be implemented with Stripe setup intents');

      } catch (err) {
        console.error('Failed to make payment method primary:', err);
        setError(err.response?.data?.message || 'Failed to update payment method');
      } finally {
        setActionLoading(prev => ({ ...prev, [paymentMethodId]: false }));
      }
    };
  // Show loading state
  if (loading) {
    return (
      <div className="CustomerPaymentMethods-root">
        <h1 className="CustomerPaymentMethods-title">My Account</h1>
        <div className="CustomerPaymentMethods-tabs">
          <a className="CustomerPaymentMethods-tab" href="#">Settings</a>
          <a className="CustomerPaymentMethods-tab CustomerPaymentMethods-tab--active" href="#">Payment Methods</a>
          <a className="CustomerPaymentMethods-tab" href="#">Orders</a>
          <a className="CustomerPaymentMethods-tab" href="#">Addresses</a>
        </div>
        <section className="CustomerPaymentMethods-section">
          <h2 className="CustomerPaymentMethods-sectionTitle">Loading Payment Methods...</h2>
        </section>
      </div>
    );
  }

  return (
    <div className="CustomerPaymentMethods-root">
      

      <section className="CustomerPaymentMethods-section">
        <h2 className="CustomerPaymentMethods-sectionTitle">My Payment Methods</h2>

        {error && (
          <div className="pp-form-error" role="alert" style={{ marginBottom: '1rem' }}>
            {error}
          </div>
        )}

        <div className="CustomerPaymentMethods-table">
          {paymentMethods.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '2rem', color: '#666' }}>
              No payment methods found. Add your first payment method to get started.
            </div>
          ) : (
            paymentMethods.map((pm) => (
              <div className="CustomerPaymentMethods-row" key={pm.id}>
                <div className="CustomerPaymentMethods-methodinfo">
                  <FiCreditCard className="CustomerPaymentMethods-methodicon" style={{ fontSize: '24px' }} />
                  <div>
                    <div className="CustomerPaymentMethods-methodtype">
                      {pm.card?.brand?.toUpperCase() || 'Card'} •••• {pm.card?.last4 || '****'}
                    </div>
                    <div className="CustomerPaymentMethods-methodtext">
                      Expires {pm.card?.exp_month}/{pm.card?.exp_year}
                    </div>
                  </div>
                </div>
                <div className="CustomerPaymentMethods-role">
                  {pm.metadata?.is_default === 'true' ? 'Primary' : 'Secondary'}
                </div>
                <div className="CustomerPaymentMethods-actions">
                  <span className="CustomerPaymentMethods-verified">
                    <span className="CustomerPaymentMethods-verifieddot"><img src={verified} alt="verified" /></span> Verified
                  </span>
                  {pm.metadata?.is_default !== 'true' && (
                    <button
                      className="pp-btn CustomerPaymentMethods-secondaryBtn"
                      onClick={() => handleMakePrimary(pm.id)}
                      disabled={actionLoading[pm.id]}
                    >
                      {actionLoading[pm.id] ? 'Processing...' : 'Make Primary'}
                    </button>
                  )}
                  <button
                    className="pp-btn CustomerPaymentMethods-secondaryBtn"
                    onClick={() => handleRemove(pm.id)}
                    disabled={actionLoading[pm.id]}
                    style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
                  >
                    <FiTrash2 size={14} />
                    {actionLoading[pm.id] ? 'Removing...' : 'Remove'}
                  </button>
                </div>
              </div>
            ))
          )}
        </div>

        <button
          className="pp-btn pp-btn-primary CustomerPaymentMethods-addBtn"
          onClick={() => setModalOpen(true)}
        >
          Add Payment Method
        </button>
      </section>
      <AddPaymentMethodModal
        isOpen={isModalOpen}
        onClose={() => setModalOpen(false)}
        onSave={handleSave}
      />
    </div>
  );
}
