import Entertainment from "../src/assets/images/Entertainment-category.png";
import Characters from "../src/assets/images/character-category.png";
import Rentals from "../src/assets/images/Rental-category.png";
import Food from "../src/assets/images/Food-category.png";
import Music from "../src/assets/images/music-category.png";
import AllCategories from "../src/assets/images/Browse-category.svg";

const categories = [
  {
    title: "Characters",
    textColor: "white",
    Image: Entertainment,
    className: "category-card characters-card",
  },
  {
    title: "Entertainment",
    textColor: "black",
    Image: Characters,
    className: "category-card entertainment-card",
  },
  {
    title: "Rentals",
    textColor: "black",
    Image: Rentals,
    className: "category-card rentals-card",
  },
  {
    title: "Food",
    textColor: "black",
    Image: Food,
    className: "category-card food-card",
  },
  {
    title: "Music",
    textColor: "black",
    Image: Music,
    className: "category-card music-card",
  },
  {
    title: (
      <>
        Browse <br /> all vendors <img src={AllCategories} alt=" All" loading="lazy" decoding="async" />
      </>
    ),
    textColor: "black",
  
    border: true,
    className: "category-card browse-card",
  },
];

export default function CategoryGrid() {
  return (
    <section className="gridlayout">
      {categories.map((cat, i) => (
        <div
          key={i}
          className={`gridcard ${cat.className} `}
          style={
            cat.Image
              ? cat.border
                ? {
                    backgroundImage: `url(${cat.Image})`,
                    backgroundRepeat: "no-repeat",
                    backgroundPosition: "90% 12%",
                    backgroundSize: "20px",
                  }
                : {
                    backgroundImage: `url(${cat.Image})`,
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    backgroundRepeat: "no-repeat",
                  }
              : undefined
          }
          aria-label={typeof cat.title === "string" ? cat.title : "Category"}
        >
          <p style={{ color: cat.textColor === "white" ? "#ffffff" : "var(--pp-clr-text-main)" }}>{cat.title}</p>
        </div>
      ))}
    </section>
  );
}
