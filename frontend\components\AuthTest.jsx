import React from 'react';
import { useAuth } from '../redux/useAuth';

/**
 * Simple test component to verify authentication integration
 */
const AuthTest = () => {
  const { 
    user, 
    isAuthenticated, 
    loading, 
    error, 
    login, 
    logout, 
    register 
  } = useAuth();

  const handleTestLogin = async () => {
    try {
      await login({
        email: '<EMAIL>',
        password: 'testpassword',
        rememberMe: false
      });
    } catch (error) {
      console.error('Test login failed:', error);
    }
  };

  const handleTestRegister = async () => {
    try {
      await register({
        email: '<EMAIL>',
        password: 'testpassword',
        confirmPassword: 'testpassword',
        firstName: 'Test',
        lastName: 'User',
        role: 'customer',
        agreeToTerms: true,
        marketingConsent: false
      });
    } catch (error) {
      console.error('Test registration failed:', error);
    }
  };

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', margin: '20px' }}>
      <h3>Authentication Test Component</h3>
      
      <div>
        <strong>Status:</strong> {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
      </div>
      
      {user && (
        <div>
          <strong>User:</strong> {user.firstName} {user.lastName} ({user.role})
        </div>
      )}
      
      {loading && <div><strong>Loading:</strong> Yes</div>}
      
      {error && (
        <div style={{ color: 'red' }}>
          <strong>Error:</strong> {error}
        </div>
      )}
      
      <div style={{ marginTop: '10px' }}>
        <button onClick={handleTestLogin} disabled={loading}>
          Test Login
        </button>
        <button onClick={handleTestRegister} disabled={loading} style={{ marginLeft: '10px' }}>
          Test Register
        </button>
        {isAuthenticated && (
          <button onClick={logout} disabled={loading} style={{ marginLeft: '10px' }}>
            Test Logout
          </button>
        )}
      </div>
    </div>
  );
};

export default AuthTest;
