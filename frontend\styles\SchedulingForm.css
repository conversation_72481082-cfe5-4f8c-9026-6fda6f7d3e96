/* =========================
   📅 Scheduling Form
   ========================= */

.sf-scheduling-form {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-32);
}

.sf-section {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-24);
}

.sf-section-title {
  font-size: var(--pp-font-heading6);
  font-weight: 600;
  color: var(--pp-clr-text-main);
  margin: 0;
  line-height: 1.3;
  padding-bottom: var(--pp-sp-8);
  border-bottom: 1px solid var(--pp-clr-border-light);
}

/* Responsive */
@media (max-width: 768px) {
  .sf-scheduling-form {
    gap: var(--pp-sp-24);
  }

  .sf-section {
    gap: var(--pp-sp-20);
  }
}

@media (max-width: 480px) {
  .sf-scheduling-form {
    gap: var(--pp-sp-20);
  }

  .sf-section {
    gap: var(--pp-sp-16);
  }
}
