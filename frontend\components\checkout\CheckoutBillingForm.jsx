import React, { useState } from "react";
import "../../styles/CheckoutBillingForm.css";
import CustomSelect from "../../components/CustomeSelect";

export default function CheckoutBillingForm() {
  const [form, setForm] = useState({
    firstName: "",
    lastName: "",
    email: "",
    address1: "",
    address2: "",
    city: "",
    state: "UT",
    zip: "",
    create: false,
  });

  const onChange = (e) => {
    const { name, value, type, checked } = e.target;
    setForm((p) => ({ ...p, [name]: type === "checkbox" ? checked : value }));
  };

  return (
    <form className="pp-checkout-billing" onSubmit={(e) => e.preventDefault()}>
      <h2 className="pp-checkout-billing__title">Billing Info</h2>

      <div className="pp-form-name-row">
        <div className="pp-form-group ">
          <label className="pp-form-label" htmlFor="firstName">
            First Name *
          </label>
          <input
            id="firstName"
            name="firstName"
            placeholder="Enter your first name"
            className="pp-form-input"
            value={form.firstName}
            onChange={onChange}
            required
          />
        </div>
        <div className="pp-form-group">
          <label className="pp-form-label" htmlFor="lastName">
            Last Name *
          </label>
          <input
            id="lastName"
            name="lastName"
            placeholder="Enter your last name"
            className="pp-form-input"
            value={form.lastName}
            onChange={onChange}
            required
          />
        </div>
      </div>

      <div className="pp-form-group sm:w-[49%]">
        <label className="pp-form-label " htmlFor="email">
          Email Address *
        </label>
        <input
          id="email"
          name="email"
          placeholder="Enter your email address"
          type="email"
          className="pp-form-input"
          value={form.email}
          onChange={onChange}
          required
        />
      </div>

      <div className="pp-formaddress-row">
        <div className="pp-form-group">
          <label className="pp-form-label" htmlFor="address1">
            Street Address *
          </label>
          <input
            id="address1"
            name="address1"
            placeholder="123 Party Lane"
            className="pp-form-input"
            value={form.address1}
            onChange={onChange}
            required
          />
        </div>
        <div className="pp-form-group">
          <label className="pp-form-label" htmlFor="address2">
            Apt, suite, unit, etc. (optional)
          </label>
          <input
            id="address2"
            name="address2"
            className="pp-form-input"
            placeholder="#2A"
            value={form.address2}
            onChange={onChange}
          />
        </div>
      </div>

      <div className="pp-form-name-row pp-form-name-row--thirds !flex">
        <div className="pp-form-group !w-[49%]">
          <label className="pp-form-label" htmlFor="city">
            Town / City *
          </label>
          <input
            id="city"
            name="city"
            placeholder="Enter your city"
            className="pp-form-input"
            value={form.city}
            onChange={onChange}
            required
          />
        </div>

        <div className="pp-form-group">
          <label className="pp-form-label" htmlFor="state">
            State *
          </label>
          <CustomSelect
            id="state"
          
            value={form.state}
            onChange={(v) => onChange({ target: { name: "state", value: v } })}
            options={[{ label: "UT", value: "UT" }]}
          />
        </div>

        <div className="pp-form-group">
          <label className="pp-form-label" htmlFor="zip">
            Postcode / ZIP *
          </label>
          <input
            id="zip"
            name="zip"
            placeholder="Enter your zip code"
            className="pp-form-input"
            value={form.zip}
            onChange={onChange}
            required
          />
        </div>
      </div>

      <div className="pp-form-group">
        <label className="pp-form-label">Country / Region *</label>
        <div className="">United States (US)</div>
      </div>

      <label className="pp-checkbox-wrapper">
        <input
          className="pp-checkbox"
          type="checkbox"
          name="create"
          checked={form.create}
          onChange={onChange}
        />
        <span className="pp-checkbox-label">Create and account</span>
      </label>
      <div className="pp-form-divider" />
      <div className="pp-checkout-billing__actions">
        <button type="submit" className="pp-btn-primary">
          Next
        </button>
      </div>
    </form>
  );
}
