import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Link } from "react-router-dom"; // Import Link from react-router-dom
import Checkbox from "../components/Checkbox";
import { useAuth } from "../redux/useAuth";
import { getVendorRedirectPath } from "../utils/vendorUtils";
import { toast } from "react-toastify";
import sideimg from "../src/assets/images/singupsideimg.svg"
import "../styles/SignupPage.css";

const SignupPage = () => {
  const navigate = useNavigate();
  const {
    register,
    loading,
    error,
    registrationSuccess,
    clearAuthError,
    clearRegSuccess,
    isAuthenticated,
    user,
    vendorOnboardingStatus
  } = useAuth();

  const [formData, setFormData] = useState({
    role: "customer",
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    agreeToTerms: false,
    marketingEmails: false,
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordError, setPasswordError] = useState("");

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      // Redirect based on user role
      if (user.role === 'vendor') {
        // Use onboarding status to determine redirect path
        const redirectPath = getVendorRedirectPath(vendorOnboardingStatus);
        navigate(redirectPath);
      } else {
        navigate('/'); // Redirect customers to home page
      }
    }
  }, [isAuthenticated, user, vendorOnboardingStatus, navigate]);

  // Clear errors when component mounts
  useEffect(() => {
    clearAuthError();
    clearRegSuccess();
  }, [clearAuthError, clearRegSuccess]);

  // Handle successful registration
  useEffect(() => {
    if (registrationSuccess) {
      // Show success message and redirect to login
      toast.success('Registration successful! Please check your email for verification. Redirecting to login...');
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    }
  }, [registrationSuccess, navigate]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));

    // Clear password error when user types
    if (name === 'password' || name === 'confirmPassword') {
      setPasswordError("");
    }
  };

  const validateForm = () => {
    // Check password match
    if (formData.password !== formData.confirmPassword) {
      setPasswordError("Passwords do not match");
      return false;
    }

    // Check terms agreement
    if (!formData.agreeToTerms) {
      return false;
    }

    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    clearAuthError();
    setPasswordError("");

    if (!validateForm()) {
      return;
    }

    try {
      await register({
        email: formData.email,
        password: formData.password,
        confirmPassword: formData.confirmPassword,
        firstName: formData.firstName,
        lastName: formData.lastName,
        role: formData.role,
        agreeToTerms: formData.agreeToTerms,
        marketingConsent: formData.marketingEmails,
      });
    } catch (error) {
      // Error is handled by Redux state
      console.error('Registration failed:', error);
    }
  };

  const togglePasswordVisibility = (field) => {
    if (field === "password") {
      setShowPassword(!showPassword);
    } else {
      setShowConfirmPassword(!showConfirmPassword);
    }
  };

  return (
    <div className="signup-page">
      {/* Left Section - Image */}
      <div className="signup-page__image-section">
        <img
          src={sideimg}
          alt="Party celebration with children and balloons"
          className="signup-page__image"
          loading="lazy"
          decoding="async"
        />
      </div>

      {/* Right Section - Signup Form */}
      <div className="signup-page__form-section">
        <div className="signup-page__form-wrapper">
          <h1 className="signup-page__title">Sign up for Party Pipeline</h1>

          {registrationSuccess && (
            <div className="pp-form-success" role="alert">
              Registration successful! Please check your email for verification. Redirecting to login...
            </div>
          )}

          {error && (
            <div className="pp-form-error" role="alert">
              {error}
            </div>
          )}

          {passwordError && (
            <div className="pp-form-error" role="alert">
              {passwordError}
            </div>
          )}

          <form onSubmit={handleSubmit}>
            {/* Role Selection */}
            <div className="pp-form-group">
              <label className="pp-form-label" htmlFor="role">
                I am a
              </label>
              <div
                className="pp-role-options"
                role="radiogroup"
                aria-label="Select your role"
              >
                <label className="pp-role-option">
                  <input
                    type="radio"
                    name="role"
                    value="customer"
                    checked={formData.role === "customer"}
                    onChange={handleInputChange}
                    className="pp-visually-hidden"
                  />
                  <div
                    className="pp-role-card"
                    aria-pressed={formData.role === "customer"}
                  >
                    <div className="pp-role-card__title">Customer</div>
                    <div className="pp-role-card__desc">
                      Book services for your events
                    </div>
                  </div>
                </label>

                <label className="pp-role-option">
                  <input
                    type="radio"
                    name="role"
                    value="vendor"
                    checked={formData.role === "vendor"}
                    onChange={handleInputChange}
                    className="pp-visually-hidden"
                  />
                  <div
                    className="pp-role-card"
                    aria-pressed={formData.role === "vendor"}
                  >
                    <div className="pp-role-card__title">Vendor</div>
                    <div className="pp-role-card__desc">
                      List and manage your services
                    </div>
                  </div>
                </label>
              </div>
            </div>
            {/* First Name and Last Name */}
            <div className="signup-page__form-row">
              <div className="pp-form-group">
                <label htmlFor="firstName" className="pp-form-label">
                  First Name
                </label>
                <input
                  type="text"
                  id="firstName"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  className="pp-form-input"
                  placeholder="Enter your first name"
                  required
                />
              </div>

              <div className="pp-form-group">
                <label htmlFor="lastName" className="pp-form-label">
                  Last Name
                </label>
                <input
                  type="text"
                  id="lastName"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleInputChange}
                  className="pp-form-input"
                  placeholder="Enter your last name"
                  required
                />
              </div>
            </div>

            {/* Email */}
            <div className="pp-form-group">
              <label htmlFor="email" className="pp-form-label">
                Email
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className="pp-form-input"
                placeholder="Enter your email address"
                required
              />
            </div>

            <div className="signup-page__form-row">
              {/* Password */}
              <div className="pp-form-group">
                <label htmlFor="password" className="pp-form-label">
                  Password
                </label>
                <div className="signup-page__password-container">
                  <input
                    type={showPassword ? "text" : "password"}
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    className="pp-form-input"
                    placeholder="Create a password"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility("password")}
                    className="signup-page__password-toggle"
                    aria-label={
                      showPassword ? "Hide password" : "Show password"
                    }
                  >
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                    >
                      {showPassword ? (
                        <>
                          <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" />
                          <line x1="1" y1="1" x2="23" y2="23" />
                        </>
                      ) : (
                        <>
                          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
                          <circle cx="12" cy="12" r="3" />
                        </>
                      )}
                    </svg>
                  </button>
                </div>
              </div>

              {/* Confirm Password */}
              <div className="pp-form-group">
                <label htmlFor="confirmPassword" className="pp-form-label">
                  Confirm Password
                </label>
                <div className="signup-page__password-container">
                  <input
                    type={showConfirmPassword ? "text" : "password"}
                    id="confirmPassword"
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className="pp-form-input"
                    placeholder="Confirm your password"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility("confirmPassword")}
                    className="signup-page__password-toggle"
                    aria-label={
                      showConfirmPassword ? "Hide password" : "Show password"
                    }
                  >
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                    >
                      {showConfirmPassword ? (
                        <>
                          <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" />
                          <line x1="1" y1="1" x2="23" y2="23" />
                        </>
                      ) : (
                        <>
                          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
                          <circle cx="12" cy="12" r="3" />
                        </>
                      )}
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            {/* Checkboxes */}
            <div className="pp-form-group">
              <Checkbox
                id="agreeToTerms"
                name="agreeToTerms"
                checked={formData.agreeToTerms}
                onChange={handleInputChange}
                label="I agree to the Terms of Service and Privacy Policy"
                required
              />
            </div>

            <div className="pp-form-group">
              <Checkbox
                id="marketingEmails"
                name="marketingEmails"
                checked={formData.marketingEmails}
                onChange={handleInputChange}
                label="I want to receive marketing emails from Party Pipeline"
              />
            </div>

            {/* Submit Button */}
            <button type="submit" className="pp-form-button" disabled={loading || !formData.agreeToTerms}>
              {loading ? 'Creating Account...' : 'Create Account'}
            </button>
          </form>

          {/* Sign In Link */}
          <div className="pp-form-text signup-page__bottom-link">
            Already have an account? <Link to="/login">Sign in</Link> {/* Updated to use Link */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignupPage;
