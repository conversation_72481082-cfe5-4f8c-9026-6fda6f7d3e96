import React from "react";
import "../styles/CustomerAddresses.css";
import { Link } from "react-router-dom";
import { AiOutlineHome } from "react-icons/ai";


const ADDRESSES = [
  {
    id: 1,
    label: "Home",
    icon: <AiOutlineHome />, // Replace with your actual path
    address: "879 South Ridge Dr\nRiverton, UT 87421",
    phone: "(*************",
    role: "Primary",
    isPrimary: true,
  },
  {
    id: 2,
    label: "Cakeko, Inc.",
    icon: <AiOutlineHome />, // Replace with your actual path
    address: "12878 S 500E\nSuite 210\nSalt Lake City, UT 87902",
    phone: "(*************",
    role: "Secondary",
    isPrimary: false,
  },
];

export default function CustomerAddresses() {
  return (
    <div className="CustomerAddresses-root">
    

      <section className="CustomerAddresses-section">
        <h2 className="CustomerAddresses-sectionTitle">My Addresses</h2>
        <div className="CustomerAddresses-table">
          {ADDRESSES.map((a) => (
            <div className="CustomerAddresses-row" key={a.id}>
              <div className="CustomerAddresses-info">
                <AiOutlineHome className="CustomerAddresses-icon"/>
                <div>
                  <div className="CustomerAddresses-label">{a.label}</div>
                  <div className="CustomerAddresses-address">
                    {a.address.split("\n").map((line, i) => (
                      <div key={i}>{line}</div>
                    ))}
                  </div>
                </div>
              </div>
              <div className="CustomerAddresses-phone">
                <div className="CustomerAddresses-phonetitle">Phone Number</div>
                <div>{a.phone}</div>
              </div>
              <div className="CustomerAddresses-role">
                {a.role}
              </div>
              <div className="CustomerAddresses-actions">
                {!a.isPrimary && (
                  <button className="pp-btn CustomerAddresses-secondaryBtn">Make Primary</button>
                )}
                <button className="pp-btn CustomerAddresses-secondaryBtn">Remove</button>
              </div>
            </div>
          ))}
        </div>

        <button className="pp-btn pp-btn-primary CustomerAddresses-addBtn">
          Add Address
        </button>
      </section>
    </div>
  );
}
