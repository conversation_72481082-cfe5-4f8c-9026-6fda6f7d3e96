import React, { useState } from "react";
import { FiCheck } from "react-icons/fi";
import Checkbox from "../components/Checkbox";
import "../styles/VendorTravelPage.css";

export default function DefaultTravelFeesPage() {
  const [enabled, setEnabled] = useState(true);
  const [fee, setFee] = useState("");
  const [freeZone, setFreeZone] = useState("");
  const [maxDistance, setMaxDistance] = useState("");

  return (
    <div className="tf-root ">
      <div className="tf-block">
        <div className="tf-title">Default Travel Fees</div>
        <div className="tf-subtitle">
          Set a default travel fee structure for when new services are added
        </div>

        <Checkbox
          className="tf-checkbox-wrapper"
          checked={enabled}
          onChange={() => setEnabled((v) => !v)}
          label="Add a default additional fee based on distance"
          iconChecked={<FiCheck className="tf-check-icon" />}
          inputClassName="tf-checkbox"
          customCheckboxClassName="tf-custom-checkbox"
          labelClassName="tf-checkbox-text"
        />

        <div className="tf-form">
          <div className="tf-form-group">
            <label className="tf-label" htmlFor="costPerMile">
              Cost Per Mile
            </label>
            <div className="tf-input-row">
              <div className="tf-input-adorn">
                <input
                  id="costPerMile"
                  type="number"
                  className="tf-input "
                  placeholder=" "
                  min="0"
                  value={fee}
                  onChange={(e) => setFee(e.target.value)}
                  disabled={!enabled}
                />
                <span className="tf-dollar-text">$</span>
                <span className="tf-input-suffix">/Mile</span>
              </div>
            </div>
          </div>

          <div className="tf-form-group">
            <label className="tf-label" htmlFor="freeZone">
              Free Travel Zone
            </label>
            <div className="tf-label-desc">
              <p className="whitespace-nowrap">
                How many miles will workers travel for free?
              </p>
            </div>
            <div className="tf-input-row">
              <input
                id="freeZone"
                type="number"
                className="tf-input"
                placeholder="0"
                min="0"
                value={freeZone}
                onChange={(e) => setFreeZone(e.target.value)}
                disabled={!enabled}
              />
              <span className="tf-input-suffix">Miles</span>
            </div>
          </div>

          <div className="tf-form-group">
            <label className="tf-label" htmlFor="maxDistance">
              Max Travel Distance
            </label>
            <div className="tf-label-desc">How far is too far?</div>
            <div className="tf-input-row">
              <input
                id="maxDistance"
                type="number"
                className="tf-input"
                placeholder="0"
                min="0"
                value={maxDistance}
                onChange={(e) => setMaxDistance(e.target.value)}
                disabled={!enabled}
              />
              <span className="tf-input-suffix">Miles</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
