import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Link } from 'react-router-dom'; // Import Link from react-router-dom
import Checkbox from '../components/Checkbox';
import { useAuth } from '../redux/useAuth';
import { getVendorRedirectPath } from '../utils/vendorUtils';
import { toast } from 'react-toastify';
import '../styles/LoginPage.css';
import sideimg from "../src/assets/images/singupsideimg.svg"
const LoginPage = () => {
  const navigate = useNavigate();
  const { login, loading, error, clearAuthError, isAuthenticated, user, vendorOnboardingStatus } = useAuth();

  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });

  const [showPassword, setShowPassword] = useState(false);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      // Redirect based on user role
      if (user.role === 'vendor') {
        // Use onboarding status to determine redirect path
        const redirectPath = getVendorRedirectPath(vendorOnboardingStatus);
        navigate(redirectPath);
      } else {
        navigate('/'); // Redirect customers to home page
      }
    }
  }, [isAuthenticated, user, vendorOnboardingStatus, navigate]);

  // Clear error when component mounts
  useEffect(() => {
    clearAuthError();
  }, [clearAuthError]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    clearAuthError();

    try {
      await login({
        email: formData.email,
        password: formData.password,
        rememberMe: formData.rememberMe
      });
      // Navigation will be handled by useEffect above
    } catch (error) {
      // Error is handled by Redux state
      console.error('Login failed:', error);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="login-page">
      {/* Left Section - Image */}
      <div className="login-page__image-section">
        <img 
          src={sideimg} 
          alt="Party celebration with children and balloons" 
          className="login-page__image"
          loading="lazy"
          decoding="async"
        />
      </div>

      {/* Right Section - Login Form */}
      <div className="login-page__form-section">
        <div className="login-page__form-wrapper">
          <h1 className="login-page__title">Log in to Party Pipeline</h1>

          {error && (
            <div className="pp-form-error" role="alert">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit}>
            {/* Email */}
            <div className="pp-form-group">
              <label htmlFor="email" className="pp-form-label">Email</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className="pp-form-input"
                placeholder="Enter your email address"
                required
              />
            </div>

            {/* Password */}
            <div className="pp-form-group">
              <label htmlFor="password" className="pp-form-label">Password</label>
              <div className="login-page__password-container">
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className="pp-form-input"
                  placeholder="Enter your password"
                  required
                />
                <button
                  type="button"
                  onClick={togglePasswordVisibility}
                  className="login-page__password-toggle"
                  aria-label={showPassword ? 'Hide password' : 'Show password'}
                >
                  <svg 
                    width="20" 
                    height="20" 
                    viewBox="0 0 24 24" 
                    fill="none" 
                    stroke="currentColor" 
                    strokeWidth="2"
                  >
                    {showPassword ? (
                      <>
                        <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" />
                        <line x1="1" y1="1" x2="23" y2="23" />
                      </>
                    ) : (
                      <>
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
                        <circle cx="12" cy="12" r="3" />
                      </>
                    )}
                  </svg>
                </button>
              </div>
            </div>

            {/* Remember Me Checkbox */}
            <div className="pp-form-group">
              <Checkbox
                id="rememberMe"
                name="rememberMe"
                checked={formData.rememberMe}
                onChange={handleInputChange}
                label="Remember me"
              />
            </div>

            {/* Forgot Links */}
            <div className="login-page__forgot-links">
              <Link to="/forgot-password" className="pp-form-link">Forgot your password?</Link>
              <Link to="/forgot-email" className="pp-form-link">Forgot your email address?</Link>
            </div>

            {/* Submit Button */}
            <button type="submit" className="pp-form-button" disabled={loading}>
              {loading ? 'Logging in...' : 'Log in'}
            </button>
          </form>

          {/* Sign Up Link */}
          <div className="pp-form-text login-page__bottom-link">
            New to Party Pipeline? <Link to="/signup">Create an account</Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
