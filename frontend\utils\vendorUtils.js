/**
 * Vendor utility functions for handling onboarding status and redirects
 */
import { vendorApi } from '../redux/apiUtils';
import { toast } from 'react-toastify';

/**
 * Check vendor onboarding status and determine redirect path
 * @param {Object} user - User object from auth state
 * @returns {Promise<string>} - Redirect path for the vendor
 */
export const checkVendorOnboardingStatus = async (user) => {
  try {
    if (!user || user.role !== 'vendor') {
      return '/';
    }

    // Call the onboarding status API
    const response = await vendorApi.getOnboardingStatus();
    
    if (response && response.data) {
      const { onboardingCompleted } = response.data;
      
      if (onboardingCompleted) {
        return '/vendor'; // Redirect to vendor dashboard
      } else {
        return '/vendor-onboarding'; // Redirect to onboarding
      }
    } else {
      // If no response data, assume onboarding not completed
      return '/vendor-onboarding';
    }
  } catch (error) {
    console.error('Error checking vendor onboarding status:', error);
    
    // Show error toast but don't block the flow
    toast.error('Unable to check onboarding status. Please try again.');
    
    // Default to onboarding page if there's an error
    return '/vendor-onboarding';
  }
};

/**
 * Get vendor redirect path based on onboarding status
 * This is a synchronous version that doesn't make API calls
 * Used when we already have the onboarding status in Redux state
 * @param {Object} onboardingStatus - Onboarding status from Redux state
 * @returns {string} - Redirect path for the vendor
 */
export const getVendorRedirectPath = (onboardingStatus) => {
  if (onboardingStatus && onboardingStatus.onboardingCompleted) {
    return '/vendor';
  }
  return '/vendor-onboarding';
};

/**
 * Handle vendor authentication redirect with loading state
 * @param {Function} navigate - React Router navigate function
 * @param {Object} user - User object from auth state
 * @param {Function} setLoading - Optional loading state setter
 */
export const handleVendorAuthRedirect = async (navigate, user, setLoading = null) => {
  try {
    if (setLoading) setLoading(true);
    
    const redirectPath = await checkVendorOnboardingStatus(user);
    navigate(redirectPath);
  } catch (error) {
    console.error('Error handling vendor auth redirect:', error);
    // Fallback to onboarding page
    navigate('/vendor-onboarding');
  } finally {
    if (setLoading) setLoading(false);
  }
};

/**
 * Validate if vendor can access a specific route
 * @param {Object} user - User object from auth state
 * @param {Object} onboardingStatus - Onboarding status from Redux state
 * @param {string} requestedPath - The path user is trying to access
 * @returns {Object} - { canAccess: boolean, redirectTo: string|null }
 */
export const validateVendorRouteAccess = (user, onboardingStatus, requestedPath) => {
  if (!user || user.role !== 'vendor') {
    return { canAccess: false, redirectTo: '/login' };
  }

  // If trying to access vendor dashboard but onboarding not completed
  if (requestedPath.startsWith('/vendor') && requestedPath !== '/vendor-onboarding') {
    if (!onboardingStatus || !onboardingStatus.onboardingCompleted) {
      return { canAccess: false, redirectTo: '/vendor-onboarding' };
    }
  }

  // If trying to access onboarding but already completed
  if (requestedPath === '/vendor-onboarding') {
    if (onboardingStatus && onboardingStatus.onboardingCompleted) {
      return { canAccess: false, redirectTo: '/vendor' };
    }
  }

  return { canAccess: true, redirectTo: null };
};
