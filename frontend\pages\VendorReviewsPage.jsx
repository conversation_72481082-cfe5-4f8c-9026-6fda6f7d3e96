import React, { useState } from "react";
import CustomSelect from "../components/CustomeSelect";
import { FaStar, FaRegCommentDots, FaTimes, FaSmile } from "react-icons/fa";
import { IoMdHeartEmpty, IoMdHeart } from "react-icons/io";
import { FaSmileWink } from "react-icons/fa";
import { FaArrowRight } from "react-icons/fa6";


import "../styles/VendorReviewsPage.css";
import "../styles/ReviewPopup.css";

const reviews = [
  {
    user: "<PERSON>",
    date: "May 07, 2023",
    rating: 5,
    totalSpent: "$240",
    bookables: ["Batman", "Spiderman, +1 more"],
    text: "Duis sollicitudin lectus eu risus tincidunt varius. Etiam lobortis convallis. Ut volutpat tellus et velit congue tempus magna. Duis convallis ex ante sodales tortor magna...",
  },
  {
    user: "<PERSON><PERSON>",
    date: "May 01, 2023",
    rating: 5,
    totalSpent: "$160",
    bookables: ["Captain Marvel"],
    text: "Donec metus erat id lobortis porta. Vestibulum varius diam. Nam ullamcorper massa nec luctus aliquet elit semper tempus magna. Vivamus eu erat mattis ligula scelerisque diam.",
  },
  {
    user: "Clarice Hampton",
    date: "Apr 14, 2023",
    rating: 5,
    totalSpent: "$100",
    bookables: ["Batman"],
    text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Mauris faucibus, velit eu feugiat euismod, risus urna dapibus imperdiet.",
  },
  {
    user: "Nico Hansen",
    date: "Apr 14, 2023",
    rating: 4,
    totalSpent: "$85",
    bookables: ["Luke Skywalker", "Storm Trooper"],
    text: "Duis tristique maximus lacus et sagittis. Pellentesque in massa. Nam dictum felis molestie orci blandit, et euismod elit porttitor enim placerat urna maximus ornare.",
  },
  {
    user: "Devan Stark",
    date: "Apr 13, 2023",
    rating: 5,
    totalSpent: "$237",
    bookables: ["Ironman"],
    text: "Donec metus erat id lobortis porta. Vestibulum varius diam. Nam ullamcorper massa nec luctus aliquet elit semper tempus magna. Vivamus eu erat mattis ligula scelerisque diam.",
  },
];

// Moved ReviewPopup to its own file in a real project, but keeping here for this rewrite
function ReviewPopup({ review, onClose, onSubmitComment }) {
  const [comment, setComment] = useState("");
  if (!review) return null;

  const handleSubmit = (e) => {
    e.preventDefault();
    if (comment.trim()) {
      onSubmitComment(comment);
      setComment("");
      onClose();
    }
  };

  return (
    <div className="rev-popup-overlay">
      <div className="rev-popup-modal">
        <button className="rev-popup-close" onClick={onClose}>
          <FaTimes />
        </button>
        
        <div className="rev-popup-header">
          <div className="rev-popup-user-info">
            <span className="rev-popup-user">{review.user}</span>
         <div className="flex gap-2 items-center">
         <div className="rev-popup-rating">
              {[...Array(5)].map((_, i) => (
                <FaStar
                  key={i}
                  className={i < review.rating ? "star filled" : "star"}
                />
              ))}
            </div>
            <span className="rev-popup-date">{review.date}</span>
         </div>
          </div>
        </div>
        
        <div className="rev-popup-text">{review.text}</div>
        
        <div className="rev-popup-comment-section">
          <div className="rev-popup-label">Public Comment</div>
       <div className="flex items-center gap-2 w-full" >
       <button className="rev-popup-emoji-btn">
              <FaSmile />
            </button>
          <div className="rev-popup-input-container">
         
            <input
              type="text"
              className="rev-popup-input"
              placeholder=""
              value={comment}
              onChange={(e) => setComment(e.target.value)}
            />
            <button 
              type="button" 
              className="rev-popup-send-btn"
              onClick={handleSubmit}
            >
             <FaArrowRight />

            </button>
          </div>
       </div>
        </div>
      </div>
    </div>
  );
}

function ReviewCardComment({ name = "Username", date = "May 28", comment }) {
  return (
    <div className="rev-card-comment-gray">
      <FaSmileWink className="rev-card-comment-icon" />
      <div>
      <div className="rev-card-comment-header">
        
        <span className="rev-card-comment-name">{name}</span>
        <span className="rev-card-comment-date">{date}</span>
      </div>
      <div className="rev-card-comment-body">{comment}</div>
      </div>
    </div>
  );
}

export default function VendorReviewsPage() {
  const [selectedReview, setSelectedReview] = useState(null);
  const [comments, setComments] = useState({});
  const [hearted, setHearted] = useState(Array(reviews.length).fill(false));
  const [bookable, setBookable] = useState("All");
  // Track which reviews are expanded (show full text)
  const [expanded, setExpanded] = useState({});

  const submitComment = (comment) => {
    const user = selectedReview.user;
    // Using today's date for new comments (can be customized)
    const date = new Date().toLocaleDateString(undefined, {
      month: "short",
      day: "numeric",
    });
    setComments((prev) => ({
      ...prev,
      [user]: [
        ...(prev[user] || []),
        {
          text: comment,
          date,
          name: user,
        },
      ],
    }));
  };

  // Toggle hearted state for a review by index
  const handleHeartClick = (idx) => {
    setHearted((prev) => {
      const updated = [...prev];
      updated[idx] = !updated[idx];
      return updated;
    });
  };

  // Optionally filter reviews by bookable
  const filteredReviews =
    bookable === "All"
      ? reviews
      : reviews.filter((r) =>
          r.bookables.some((b) =>
            b
              .toLowerCase()
              .includes(bookable.toLowerCase())
          )
        );

  return (
    <div className="rev-root">
      <div className="rev-toolbar">
        <div className="rev-summary">
          <div className="rev-summary-item">
            <div className="rev-summary-label">Average Rating</div>
            <div className="rev-summary-line">
              <div className="rev-summary-value">4.7</div>
              <div className="rev-stars">
                {[...Array(5)].map((_, i) => (
                  <FaStar key={i} className={i < 4 ? "star filled" : "star"} />
                ))}
              </div>
            </div>
          </div>
          <div className="rev-summary-item">
            <div className="rev-summary-label">Total Reviews</div>
            <div className="rev-summary-value">16</div>
          </div>
        </div>
        <div className="rev-bookable-filter">
          <label htmlFor="bookable">Bookables</label>
          <CustomSelect
            id="bookable"
            
            value={bookable}
            onChange={setBookable}
            options={["All", "Batman", "Spiderman", "Ironman"]}
          />
        </div>
      </div>
      <div className="rev-list">
        {filteredReviews.map((r, idx) => (
          <div className="rev-card" key={idx}>
            <div className="rev-card-inner">
              <div className="rev-card-left">
              <div className="rev-user">{r.user}</div>
                <div className="rev-left-item">
               
                  <div className="rev-left-label">Total Spend :</div>
                  <div className="rev-left-value">{r.totalSpent}</div>
                </div>
                <div className="rev-left-item">
                  <div className="rev-left-label">Bookable :</div>
                  <div className="rev-left-value">{r.bookables.join(", ")}</div>
                </div>
              </div>
              <div className="rev-card-right">
                <div className="rev-row">
                 
                  <div className="rev-rating">
                    {[...Array(5)].map((_, i) => (
                      <FaStar
                        key={i}
                        className={i < r.rating ? "star filled" : "star"}
                      />
                    ))}
                  </div>
                  <div className="rev-date">{r.date}</div>
                </div>
                <div className="rev-row-readmore">
                  <div className={`rev-text ${!expanded[idx] ? "rev-text--clamp" : ""}`}>
                    {r.text}
                  </div>
                  {(!expanded[idx] && (r.text?.length || 0) > 120) && (
                    <button
                      type="button"
                      className="rev-read-more"
                      onClick={() => setExpanded((prev) => ({ ...prev, [idx]: true }))}
                    >
                      Read more
                    </button>
                  )}
                  {expanded[idx] && (
                    <button
                      type="button"
                      className="rev-read-more"
                      onClick={() => setExpanded((prev) => ({ ...prev, [idx]: false }))}
                    >
                      Read less
                    </button>
                  )}
                </div>

                {/* Comments */}
                {comments[r.user]?.map((cmt, i) => (
                  <ReviewCardComment
                    key={i}
                    name={cmt.name}
                    date={cmt.date}
                    comment={cmt.text}
                  />
                ))}

                <div className="rev-row rev-row-actions">
                  <button
                    type="button"
                    className="pp-btn-secondary"
                    onClick={() => setSelectedReview(r)}
                  >
                     Public Comment
                  </button>
                  <button type="button" className="pp-btn-secondary">
                    Direct Message
                  </button>
                  <button
                    type="button"
                    className="rev-btn-heart"
                    onClick={() => handleHeartClick(idx)}
                    aria-label={hearted[idx] ? "Unheart" : "Heart"}
                  >
                    {hearted[idx] ? (
                      <IoMdHeart className="rev-heart-filled" />
                    ) : (
                      <IoMdHeartEmpty className="rev-heart-empty" />
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
      {selectedReview && (
        <ReviewPopup
          review={selectedReview}
          onClose={() => setSelectedReview(null)}
          onSubmitComment={submitComment}
        />
      )}
    </div>
  );
}
