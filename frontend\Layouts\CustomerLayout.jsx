import React, { useState, useEffect } from "react";
import CustomSelect from "../components/CustomeSelect";
import {
  Link,
  NavLink,
  Outlet,
  useLocation,
  useNavigate,
} from "react-router-dom";
import "../styles/CustomerLayout.css";
import { useAuth } from "../redux/useAuth";



const customerTabs = [
  { label: "Profile", path: "/customer-account-profile" },
  { label: "Payment Methods", path: "/customer-payment-methods" },
  { label: "Orders", path: "/customer-orders" },
  { label: "Addresses", path: "/customer-addresses" },
  { label: "Settings", path: "/customer-settings" },
];

const vendorTabs = [
  { label: "Profile", path: "/customer-account-profile" },
  { label: "Settings", path: "/customer-settings" },
];



const CustomerLayout = () => {
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 600);
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const tabs = user && user.role === "vendor" ? vendorTabs : customerTabs;

  useEffect(() => {
    const handleResize = () => setIsMobile(window.innerWidth <= 600);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const currentTab =
    tabs.find((tab) => location.pathname === tab.path) || tabs[0];

  return (
    <div className="CustomerLayout-root CustomerAccountSettings-root pp-max-container">
      <main className="CustomerLayout-content CustomerAccountSettings-content">
        <h1 className="CustomerLayout-title CustomerAccountSettings-title">
          My Account
        </h1>

        <div className="CustomerLayout-tabs CustomerAccountSettings-tabs">
          {isMobile ? (
            <CustomSelect
              className="CustomerLayout-dropdown"
              value={currentTab.path}
              onChange={(val) => navigate(val)}
              options={tabs.map((t) => ({ label: t.label, value: t.path }))}
            />
          ) : (
            <nav className="CustomerLayout-tablist">
              {tabs.map((tab) => (
                <NavLink
                  key={tab.path}
                  to={tab.path}
                  className={({ isActive }) =>
                    `CustomerLayout-tab CustomerAccountSettings-tab${
                      isActive
                        ? " CustomerLayout-tab--active CustomerAccountSettings-tab--active"
                        : ""
                    }`
                  }
                >
                  {tab.label}
                </NavLink>
              ))}
            </nav>
          )}
        </div>

        <Outlet />
      </main>
    </div>
  );
};

export default CustomerLayout;
