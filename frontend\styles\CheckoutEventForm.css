.pp-event-checkout-form {
    display: flex;
    flex-direction: column;
    
    width: 100%;
    background: var(--pp-clr-primary);
    border-radius: var(--pp-bor-rad-16);
    font-family: var(--pp-font-Metro-Sans);
    

    gap: var(--pp-sp-16);
  }
  
  @media (max-width: 768px) {
    .pp-event-checkout-form {
      
      max-width: 100%;
    }
  }
  
  .pp-event-checkout-form__heading {
    font-family: var(--pp-font-Metro-Sans);
    font-size: var(--pp-font-heading5);
    font-weight: 600;
    
  }
  
  .pp-event-checkout-form__group-row {
    display: flex;
    flex-direction: column;
    gap: var(--pp-sp-4);
    width: 50%;
    margin-bottom: var(--pp-sp-10);
  }
  .pp-event-checkout-form__label {
    font-size: var(--pp-font-extra-small);
    color: var(--pp-clr-text-gray);
    font-weight: 500;
    margin-bottom: var(--pp-sp-4);
  }
  
  .pp-event-checkout-form__select-wrap {
    display: flex;
  }

  
  .pp-event-checkout-form__divider {
    height: 1px;
    background: var(--pp-clr-border-light);
    margin: var(--pp-sp-16) 0 var(--pp-sp-16) 0;
  }
  
  .pp-event-checkout-form__section-title {
    font-size: var(--pp-font-base2-font);
    font-family: var(--pp-font-Metro-Sans);
    font-weight: 600;
    margin-bottom: var(--pp-sp-16);
    margin-top: var(--pp-sp-8);
  }
  
  .pp-event-checkout-form__form {
    display: flex;
    flex-direction: column;
    gap: var(--pp-sp-16);
  }
  
  /* 2 column layout */
  .pp-event-checkout-form__2col {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--pp-sp-16);
    margin-bottom: var(--pp-sp-10);
  }
  @media (max-width: 640px) {
    .pp-event-checkout-form__2col {
      grid-template-columns: 1fr;
      gap: var(--pp-sp-16);
    }
  }
  
  /* 3 columns for address (city/state/zip) */
  .pp-event-checkout-form__3col {
    display: grid;
    grid-template-columns: 2fr 1fr 1.2fr;
    gap: var(--pp-sp-16);
    margin-bottom: var(--pp-sp-8);
  }
  @media (max-width: 640px) {
    .pp-event-checkout-form__3col {
      grid-template-columns: 1fr;
    }
  }
  
  .pp-event-checkout-form__group {
    display: flex;
    flex-direction: column;
    gap: var(--pp-sp-4);
  }
  
  .pp-event-checkout-form__input,
  
.custom-select-wrapper.pp-event-checkout-form__select,
  .pp-event-checkout-form__textarea {
    width: 100%;
    padding: var(--pp-sp-16);
    border: 1px solid var(--pp-clr-border);
    border-radius: var(--pp-bor-rad-8);
    background: var(--pp-clr-primary);
    color: var(--pp-clr-text-main);
    font-size: var(--pp-font-extra-small);
    font-family: var(--pp-font-Metro-Sans);
    transition: border-color 0.2s;
  }
  .pp-event-checkout-form__input:focus,
  .pp-event-checkout-form__textarea:focus {
    border-color: var(--pp-clr-secondary);
    outline: none;
  }
  
  .pp-event-checkout-form__required {
    color: #de2727;
    margin-left: 1px;
  }
  
  /* Country static */
  .pp-event-checkout-form__country-static {
    font-size: var(--pp-font-extra-small);
    color: var(--pp-clr-text-main);
    padding: var(--pp-sp-8) 0 0 var(--pp-sp-4);
  }
  
  .pp-event-checkout-form__small-group {
    margin-bottom: 0;
  }
  
  .pp-event-checkout-form__2col-no-space {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--pp-sp-32);
    margin-bottom: var(--pp-sp-8);
  }
  @media (max-width: 640px) {
    .pp-event-checkout-form__2col-no-space {
      grid-template-columns: 1fr;
      gap: var(--pp-sp-8);
    }
  }
  
  /* Radio-like two-button group (yes/no) */
  
  .pp-event-checkout-form__radio-wrap {
    display: flex;
   padding: 5px;
    margin-top: var(--pp-sp-4);
    border: 1px solid var(--pp-clr-border-light);
    border-radius: var(--pp-bor-rad-8);
    width: fit-content;
    background-color: var(--pp-clr-secondary);
  }
  .pp-event-checkout-form__radio-btn {
    width: 36px;
    height: 28px;
    background: var(--pp-clr-primary);
    border: 1px solid var(--pp-clr-border);
    color: var(--pp-clr-text-main);
    font-family: var(--pp-font-Metro-Sans);
    border-radius: 4px;
    padding: 4px 12px;
    font-size: 12px;
    cursor: pointer;
    text-align: center;
    transition: all 0.14s;
    box-shadow: none;
    display: flex;
    align-items: center;
    justify-content: center;
    
  }
  .pp-event-checkout-form__radio-btn.selected,
  .pp-event-checkout-form__radio-btn:active {
    background: var(--pp-clr-secondary);
    color: transparent;
    border-color: var(--pp-clr-secondary);
  }
  .pp-event-checkout-form__radio-btn:not(.selected):hover {
    border-color: var(--pp-clr-text-gray);
  }
  
  .pp-event-checkout-form__textarea {
    min-height: 44px;
    font-size: var(--pp-font-extra-small);
    resize: vertical;
  }
  
  .pp-event-checkout-form__footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--pp-sp-16);
    border-top: 1px solid var(--pp-clr-border-light);
    padding-top: var(--pp-sp-20);
  }

@media (max-width: 800px) {
  .pp-event-checkout-form__divider{
    margin: 0;
  }
}

  
  @media (max-width: 640px) {
    .pp-event-checkout-form__footer {
      
      gap: var(--pp-sp-8);
      padding-top: var(--pp-sp-16);
    }


    .pp-event-checkout-form__2col{
      margin-bottom: 0;
    }
  }
  @media (max-width: 480px) {
    .pp-event-checkout-form__group-row {
      width: 100%;
    }
    .pp-event-checkout-form{
      gap: var(--pp-sp-8);
    }
  }
  @media (max-width: 370px) {
    .pp-event-checkout-form__3col{
      flex-direction: column;
    } 
  }