import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { vendorApi } from './apiUtils';

/**
 * Async thunk for starting vendor onboarding
 */
export const startVendorOnboarding = createAsyncThunk(
  'vendor/startOnboarding',
  async (businessInfo, { rejectWithValue }) => {
    try {
      const response = await vendorApi.startOnboarding(businessInfo);
      return response;
    } catch (error) {
      return rejectWithValue({
        message: error.message,
        status: error.status,
        data: error.data
      });
    }
  }
);

/**
 * Async thunk for forcing refresh of onboarding status
 */
export const forceRefreshStatus = createAsyncThunk(
  'vendor/forceRefreshStatus',
  async (_, { rejectWithValue }) => {
    try {
      const response = await vendorApi.forceRefreshStatus();
      return response;
    } catch (error) {
      return rejectWithValue({
        message: error.message,
        status: error.status,
        data: error.data
      });
    }
  }
);

/**
 * Async thunk for getting vendor onboarding status
 */
export const getOnboardingStatus = createAsyncThunk(
  'vendor/getOnboardingStatus',
  async (_, { rejectWithValue }) => {
    try {
      const response = await vendorApi.getOnboardingStatus();
      return response;
    } catch (error) {
      return rejectWithValue({
        message: error.message,
        status: error.status,
        data: error.data
      });
    }
  }
);

/**
 * Async thunk for getting vendor profile
 */
export const getVendorProfile = createAsyncThunk(
  'vendor/getProfile',
  async (_, { rejectWithValue }) => {
    try {
      const response = await vendorApi.getProfile();
      return response;
    } catch (error) {
      return rejectWithValue({
        message: error.message,
        status: error.status,
        data: error.data
      });
    }
  }
);

/**
 * Async thunk for updating vendor profile
 */
export const updateVendorProfile = createAsyncThunk(
  'vendor/updateProfile',
  async (profileData, { rejectWithValue }) => {
    try {
      const response = await vendorApi.updateProfile(profileData);
      return response;
    } catch (error) {
      return rejectWithValue({
        message: error.message,
        status: error.status,
        data: error.data
      });
    }
  }
);

/**
 * Initial state for vendor
 */
const initialState = {
  profile: null,
  onboardingStatus: {
    onboardingCompleted: false,
    chargesEnabled: false,
    payoutsEnabled: false,
    requiresAction: true,
  },
  onboardingCompleted: false,
  loading: false,
  error: null,
  onboardingLoading: false,
  onboardingError: null,
};

/**
 * Vendor slice
 */
const vendorSlice = createSlice({
  name: 'vendor',
  initialState,
  reducers: {
    // Clear error state
    clearError: (state) => {
      state.error = null;
    },

    // Clear onboarding error
    clearOnboardingError: (state) => {
      state.onboardingError = null;
    },

    // Reset vendor state (for logout)
    resetVendorState: (state) => {
      return initialState;
    },
  },
  extraReducers: (builder) => {
    // Start onboarding
    builder
      .addCase(startVendorOnboarding.pending, (state) => {
        state.onboardingLoading = true;
        state.onboardingError = null;
      })
      .addCase(startVendorOnboarding.fulfilled, (state, action) => {
        state.onboardingLoading = false;
        state.onboardingCompleted = action.payload.data?.onboardingCompleted || false;
        state.onboardingStatus = action.payload.data?.vendorProfile || null;
      })
      .addCase(startVendorOnboarding.rejected, (state, action) => {
        state.onboardingLoading = false;
        state.onboardingError = action.payload?.message || 'Failed to start onboarding';
      });

    // Force refresh status
    builder
      .addCase(forceRefreshStatus.pending, (state) => {
        state.loading = true;
      })
      .addCase(forceRefreshStatus.fulfilled, (state, action) => {
        state.loading = false;
        state.onboardingStatus = action.payload.data || state.onboardingStatus;
      })
      .addCase(forceRefreshStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || 'Failed to refresh status';
      });

    // Get onboarding status
    builder
      .addCase(getOnboardingStatus.pending, (state) => {
        state.loading = true;
      })
      .addCase(getOnboardingStatus.fulfilled, (state, action) => {
        state.loading = false;
        state.onboardingStatus = action.payload.data || state.onboardingStatus;
      })
      .addCase(getOnboardingStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || 'Failed to get onboarding status';
      });

    // Get vendor profile
    builder
      .addCase(getVendorProfile.pending, (state) => {
        state.loading = true;
      })
      .addCase(getVendorProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.profile = action.payload.data?.vendor || action.payload.data;
      })
      .addCase(getVendorProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || 'Failed to get vendor profile';
      });

    // Update vendor profile
    builder
      .addCase(updateVendorProfile.pending, (state) => {
        state.loading = true;
      })
      .addCase(updateVendorProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.profile = action.payload.data?.vendor || action.payload.data;
      })
      .addCase(updateVendorProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || 'Failed to update vendor profile';
      });
  },
});

export const {
  clearError,
  clearOnboardingError,
  resetVendorState
} = vendorSlice.actions;

export default vendorSlice.reducer;

// Selectors
export const selectVendor = (state) => state.vendor;
export const selectVendorProfile = (state) => state.vendor.profile;
export const selectOnboardingStatus = (state) => state.vendor.onboardingStatus;
export const selectOnboardingCompleted = (state) => state.vendor.onboardingCompleted;
export const selectVendorLoading = (state) => state.vendor.loading;
export const selectVendorError = (state) => state.vendor.error;
export const selectOnboardingLoading = (state) => state.vendor.onboardingLoading;
export const selectOnboardingError = (state) => state.vendor.onboardingError;
