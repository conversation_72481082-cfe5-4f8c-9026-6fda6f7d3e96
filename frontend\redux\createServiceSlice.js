import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  isCreateModalOpen: false,
  selectedCategory: "event", // "event" | "service" | "product" etc.
  activeTab: "General", // General | Photos | Pricing | Scheduling | Reviews
};

const createServiceSlice = createSlice({
  name: "createService",
  initialState,
  reducers: {
    openCreateModal: (state) => {
      state.isCreateModalOpen = true;
    },
    closeCreateModal: (state) => {
      state.isCreateModalOpen = false;
    },
    setSelectedCategory: (state, action) => {
      state.selectedCategory = action.payload;
    },
    setActiveTab: (state, action) => {
      state.activeTab = action.payload;
    },
  },
});

export const {
  openCreateModal,
  closeCreateModal,
  setSelectedCategory,
  setActiveTab,
} = createServiceSlice.actions;

export default createServiceSlice.reducer;


