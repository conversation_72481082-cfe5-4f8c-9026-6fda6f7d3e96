const Joi = require('joi');
const { commonSchemas } = require('../middlewares/validation');

/**
 * Authentication validation schemas
 */

// User registration validation
const registerSchema = Joi.object({
    email: commonSchemas.email.required(),
    password: commonSchemas.strongPassword.required(),
    confirmPassword: Joi.string()
        .valid(Joi.ref('password'))
        .required()
        .messages({
            'any.only': 'Passwords do not match'
        }),
    firstName: Joi.string()
        .trim()
        .min(2)
        .max(50)
        .required()
        .messages({
            'string.min': 'First name must be at least 2 characters',
            'string.max': 'First name cannot exceed 50 characters'
        }),
    lastName: Joi.string()
        .trim()
        .min(2)
        .max(50)
        .required()
        .messages({
            'string.min': 'Last name must be at least 2 characters',
            'string.max': 'Last name cannot exceed 50 characters'
        }),
    role: Joi.string()
        .valid('customer', 'vendor')
        .default('customer'),
    phone: commonSchemas.phone.optional(),
    agreeToTerms: Joi.boolean()
        .valid(true)
        .required()
        .messages({
            'any.only': 'You must agree to the terms and conditions'
        }),
    marketingConsent: Joi.boolean().default(false),
    // Vendor-specific optional fields allowed on registration when role is 'vendor'
    businessName: Joi.when('role', {
        is: 'vendor',
        then: Joi.string().min(2).max(100).optional(),
        otherwise: Joi.forbidden()
    }),
    businessType: Joi.when('role', {
        is: 'vendor',
        then: Joi.string().valid('individual', 'llc', 'corporation', 'partnership', 'other').optional(),
        otherwise: Joi.forbidden()
    }),
    description: Joi.when('role', {
        is: 'vendor',
        then: Joi.string().max(2000).optional(),
        otherwise: Joi.forbidden()
    })
});

// User login validation
const loginSchema = Joi.object({
    email: commonSchemas.email.required(),
    password: Joi.string()
        .required()
        .messages({
            'any.required': 'Password is required'
        }),
    rememberMe: Joi.boolean().default(false)
});

// Forgot password validation
const forgotPasswordSchema = Joi.object({
    email: commonSchemas.email.required()
});

// Reset password validation
const resetPasswordSchema = Joi.object({
    token: Joi.string()
        .required()
        .messages({
            'any.required': 'Reset token is required'
        }),
    password: commonSchemas.strongPassword.required(),
    confirmPassword: Joi.string()
        .valid(Joi.ref('password'))
        .required()
        .messages({
            'any.only': 'Passwords do not match'
        })
});

// Change password validation (for authenticated users)
const changePasswordSchema = Joi.object({
    currentPassword: Joi.string()
        .required()
        .messages({
            'any.required': 'Current password is required'
        }),
    newPassword: commonSchemas.strongPassword.required(),
    confirmPassword: Joi.string()
        .valid(Joi.ref('newPassword'))
        .required()
        .messages({
            'any.only': 'Passwords do not match'
        })
});

// Email verification validation
const emailVerificationSchema = Joi.object({
    token: Joi.string()
        .required()
        .messages({
            'any.required': 'Verification token is required'
        })
});

// Resend verification email validation
const resendVerificationSchema = Joi.object({
    email: commonSchemas.email.required()
});

// Refresh token validation
const refreshTokenSchema = Joi.object({
    refreshToken: Joi.string()
        .required()
        .messages({
            'any.required': 'Refresh token is required'
        })
});

// Update profile validation
const updateProfileSchema = Joi.object({
    firstName: Joi.string()
        .trim()
        .min(2)
        .max(50)
        .optional(),
    lastName: Joi.string()
        .trim()
        .min(2)
        .max(50)
        .optional(),
    phone: commonSchemas.phone.optional().allow(''),
    profileImage: Joi.string().uri().optional().allow(''),
    addresses: Joi.array().items(
        Joi.object({
            type: Joi.string()
                .valid('billing', 'event')
                .required(),
            street: Joi.string()
                .trim()
                .max(200)
                .required(),
            city: Joi.string()
                .trim()
                .max(100)
                .required(),
            state: Joi.string()
                .trim()
                .max(100)
                .required(),
            zipCode: Joi.string()
                .trim()
                .pattern(/^[0-9]{5}(-[0-9]{4})?$/)
                .required()
                .messages({
                    'string.pattern.base': 'Invalid ZIP code format'
                }),
            country: Joi.string()
                .default('US')
        })
    ).max(5).optional(),
    marketingConsent: Joi.boolean().optional()
});

// Two-factor authentication setup validation
const setupTwoFactorSchema = Joi.object({
    secret: Joi.string()
        .required()
        .messages({
            'any.required': 'Two-factor secret is required'
        }),
    token: Joi.string()
        .length(6)
        .pattern(/^[0-9]+$/)
        .required()
        .messages({
            'string.length': 'Token must be 6 digits',
            'string.pattern.base': 'Token must contain only numbers'
        })
});

// Two-factor authentication verification validation
const verifyTwoFactorSchema = Joi.object({
    token: Joi.string()
        .length(6)
        .pattern(/^[0-9]+$/)
        .required()
        .messages({
            'string.length': 'Token must be 6 digits',
            'string.pattern.base': 'Token must contain only numbers'
        })
});

// Social login validation
const socialLoginSchema = Joi.object({
    provider: Joi.string()
        .valid('google', 'facebook', 'apple')
        .required(),
    accessToken: Joi.string()
        .required()
        .messages({
            'any.required': 'Access token is required'
        }),
    userData: Joi.object({
        email: commonSchemas.email.required(),
        firstName: Joi.string().trim().min(1).max(50).required(),
        lastName: Joi.string().trim().min(1).max(50).required(),
        profileImage: Joi.string().uri().optional(),
        providerId: Joi.string().required()
    }).required()
});

// Device/session management validation
const registerDeviceSchema = Joi.object({
    deviceId: Joi.string()
        .required()
        .messages({
            'any.required': 'Device ID is required'
        }),
    deviceType: Joi.string()
        .valid('web', 'mobile', 'tablet', 'desktop')
        .required(),
    deviceName: Joi.string()
        .trim()
        .max(100)
        .optional(),
    pushToken: Joi.string().optional(),
    metadata: Joi.object({
        userAgent: Joi.string().optional(),
        platform: Joi.string().optional(),
        version: Joi.string().optional()
    }).optional()
});

// Logout validation
const logoutSchema = Joi.object({
    deviceId: Joi.string().optional(),
    allDevices: Joi.boolean().default(false)
});

// Account deactivation validation
const deactivateAccountSchema = Joi.object({
    reason: Joi.string()
        .valid(
            'privacy_concerns',
            'too_many_emails',
            'not_useful',
            'found_alternative',
            'temporary_break',
            'other'
        )
        .required(),
    feedback: Joi.string()
        .trim()
        .max(500)
        .optional()
        .allow(''),
    password: Joi.string()
        .required()
        .messages({
            'any.required': 'Password confirmation is required'
        })
});

// Account deletion validation
const deleteAccountSchema = Joi.object({
    password: Joi.string()
        .required()
        .messages({
            'any.required': 'Password confirmation is required'
        }),
    confirmation: Joi.string()
        .valid('DELETE')
        .required()
        .messages({
            'any.only': 'You must type "DELETE" to confirm account deletion'
        }),
    reason: Joi.string()
        .valid(
            'privacy_concerns',
            'not_satisfied',
            'found_alternative',
            'no_longer_needed',
            'other'
        )
        .optional(),
    feedback: Joi.string()
        .trim()
        .max(1000)
        .optional()
        .allow('')
});

// Export all validation schemas
module.exports = {
    registerSchema,
    loginSchema,
    forgotPasswordSchema,
    resetPasswordSchema,
    changePasswordSchema,
    emailVerificationSchema,
    resendVerificationSchema,
    refreshTokenSchema,
    updateProfileSchema,
    setupTwoFactorSchema,
    verifyTwoFactorSchema,
    socialLoginSchema,
    registerDeviceSchema,
    logoutSchema,
    deactivateAccountSchema,
    deleteAccountSchema
};
