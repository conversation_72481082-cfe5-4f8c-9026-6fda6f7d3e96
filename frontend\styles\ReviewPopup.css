.rev-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1200;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}

.rev-popup-modal {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 32px;
  width: 600px;
  max-width: 90vw;
  position: relative;
  animation: fadeIn 0.2s;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.rev-popup-close {
  position: absolute;
  top: 16px;
  right: 16px;
  background: transparent;
  




  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.rev-popup-close:hover {
  background: #f5f5f5;
}

.rev-popup-header {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.rev-popup-user-info {
  display: flex;
  
  flex-direction: column;
  gap: 5px;
  flex-wrap: wrap;
}

.rev-popup-user {
  font-weight: 600;
  font-size: 16px;
  color: #000;
}

.rev-popup-rating {
  display: flex;
  gap: 2px;
}

.rev-popup-rating .star {
  color: #e5e5e5;
  font-size: 14px;
}

.rev-popup-rating .filled {
  color: #ffc107;
}

.rev-popup-date {
  font-size: 12px;
  color: #727272;
  font-weight: 500;
  margin-left: auto;
}

.rev-popup-text {
  color: #333;
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
  font-weight: 400;
}

.rev-popup-comment-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.rev-popup-label {
  color: #000;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.rev-popup-input-container {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 0px 12px;
  gap: 8px;
  width: 100%;
  background: #fff;
}

.rev-popup-emoji-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 18px;
  padding: 6px;
  border-radius: 4px;
  transition: background 0.2s;
  color: #666;
}

.rev-popup-emoji-btn:hover {
  background: #f5f5f5;
}

.rev-popup-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 14px;
  padding: 8px 0;
  background: transparent;
  color: #333;
}

.rev-popup-input::placeholder {
  color: #999;
}

.rev-popup-send-btn {
  background: #000;
  border: none;
  border-radius: 50%;
  width: 26px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.2s;

}

.rev-popup-send-btn:hover {
  background: #333;
}

.rev-popup-send-btn svg {
  color: #fff;
  font-size: 14px;
}

@media (max-width: 600px) {
  .rev-popup-modal {
    width: 95vw;
    padding: 24px 16px;
  }
  
  .rev-popup-user-info {
   
    gap: 8px;
  }
  
  .rev-popup-date {
    margin-left: 0;
  }
}
