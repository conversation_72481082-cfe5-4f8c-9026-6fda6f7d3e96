import React, { useEffect, useCallback } from "react";
import "../../styles/PopupModal.css";
/**
 * PopupModal
 * - Reusable modal with overlay
 * - Top-right close button
 * - Scrollable content area (entire body scrolls, no header/footer)
 * - Customizable width via prop while respecting max width and viewport
 *
 * Props:
 * - isOpen: boolean
 * - onClose: () => void
 * - (Head<PERSON>/Footer intentionally omitted for a simple modal)
 * - width?: string | number (e.g., 720, "720px", "60%")
 * - maxWidth?: string (defaults to CSS var --pp-modal-max-width)
 * - showClose?: boolean (default true)
 * - closeOnBackdrop?: boolean (default true)
 * - className?: string (applied to container)
 * - contentClassName?: string (applied to content)
 * - children: ReactNode (modal body)
 */
export default function PopupModal({
  isOpen,
  onClose,
  width,
  maxWidth = "var(--pp-modal-max-width)",
  showClose = true,
  closeOnBackdrop = true,
  className = "",
  contentClassName = "",
  children,
}) {
  // Close on ESC key
  const handleKeyDown = useCallback(
    (e) => {
      if (e.key === "Escape" && isOpen) {
        e.stopPropagation();
        onClose?.();
      }
    },
    [isOpen, onClose]
  );

  useEffect(() => {
    if (!isOpen) return;
    // lock body scroll
    const originalOverflow = document.body.style.overflow;
    document.body.style.overflow = "hidden";
    window.addEventListener("keydown", handleKeyDown);
    return () => {
      document.body.style.overflow = originalOverflow;
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [isOpen, handleKeyDown]);

  if (!isOpen) return null;

  const containerStyle = {};
  if (width !== undefined) {
    containerStyle.width = typeof width === "number" ? `${width}px` : width;
  }
  if (maxWidth) {
    containerStyle.maxWidth = maxWidth;
  }

  return (
    <div
      className="PopupModal-overlay"
      role="dialog"
      aria-modal="true"
      onMouseDown={(e) => {
        // close on backdrop click
        if (!closeOnBackdrop) return;
        if (e.target === e.currentTarget) {
          onClose?.();
        }
      }}
    >
      <div className={`PopupModal-container ${className}`} style={containerStyle}>
        {/* Close Button */}
        {showClose && (
            <div className="PopupModal-closeContainer">
          <button
            type="button"
            aria-label="Close"
            className="PopupModal-close"
            onClick={onClose}
          >
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M18 6L6 18M6 6l12 12"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </button>
          </div>
        )}

        {/* Content (fills the remaining height and scrolls) */}
        <div className={`PopupModal-content ${contentClassName}`}>{children}</div>
      </div>
    </div>
  );
}
