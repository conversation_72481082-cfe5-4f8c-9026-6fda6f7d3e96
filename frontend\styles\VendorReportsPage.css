@import "./ReportsShared.css";
.report-root {
  background: var(--pp-clr-bg);
  font-family: var(--pp-font-Metro-Sans), system-ui, -apple-system, "Segoe UI";

  min-height: calc(100vh - 640px);
}

.report-form {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: var(--pp-sp-20);
  align-items: start;
}

.report-field {
  min-width: 180px;
}

.report-date-range {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8);
}



@media (max-width: 900px) {
  .report-form {
    gap: var(--pp-sp-24);
    justify-items: flex-start;
  }
}

@media (max-width: 768px) {
  .report-form {
    grid-template-columns: 1fr;
  }
}
@media (max-width: 600px) {
  .report-form {
    gap: var(--pp-sp-16);
  }

  .report-date-range {
    flex-direction: column;
    align-items: start;
  }
}
