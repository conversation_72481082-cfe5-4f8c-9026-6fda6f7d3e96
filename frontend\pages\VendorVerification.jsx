import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { selectUser } from '../redux/slices/authSlice';
import { vendorAPI } from '../redux/apiUtils';
import VerificationStatusBanner from '../components/VerificationStatusBanner';
import '../styles/VendorVerification.css';

const VendorVerification = () => {
  const user = useSelector(selectUser);
  const [verificationStatus, setVerificationStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [submittingDoc, setSubmittingDoc] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({});

  useEffect(() => {
    fetchVerificationStatus();
  }, []);

  const fetchVerificationStatus = async () => {
    try {
      setLoading(true);
      const response = await vendorAPI.getVerificationStatus();
      setVerificationStatus(response.data);
      setError(null);
    } catch (err) {
      console.error('Failed to fetch verification status:', err);
      setError(err.response?.data?.message || 'Failed to load verification status');
    } finally {
      setLoading(false);
    }
  };

  const handleDocumentUpload = async (documentType, file) => {
    if (!file) return;

    try {
      setSubmittingDoc(true);
      setUploadProgress({ [documentType]: 0 });

      const formData = new FormData();
      formData.append('document', file);
      formData.append('documentType', documentType);

      const response = await vendorAPI.submitVerificationDocument(formData);
      
      if (response.success) {
        setUploadProgress({ [documentType]: 100 });
        // Refresh verification status
        await fetchVerificationStatus();
        
        setTimeout(() => {
          setUploadProgress({});
        }, 2000);
      }
    } catch (err) {
      console.error('Failed to upload document:', err);
      setError(err.response?.data?.message || 'Failed to upload document');
      setUploadProgress({});
    } finally {
      setSubmittingDoc(false);
    }
  };

  const DocumentUploadCard = ({ documentType, title, description, required = false }) => {
    const isUploading = uploadProgress[documentType] !== undefined;
    const progress = uploadProgress[documentType] || 0;

    return (
      <div className="document-upload-card">
        <div className="document-upload-card__header">
          <h3 className="document-upload-card__title">
            {title}
            {required && <span className="document-upload-card__required">*</span>}
          </h3>
          <p className="document-upload-card__description">{description}</p>
        </div>
        
        <div className="document-upload-card__upload">
          <input
            type="file"
            id={`upload-${documentType}`}
            accept=".jpg,.jpeg,.png,.pdf"
            onChange={(e) => handleDocumentUpload(documentType, e.target.files[0])}
            disabled={submittingDoc}
            className="document-upload-card__input"
          />
          <label 
            htmlFor={`upload-${documentType}`} 
            className={`document-upload-card__label ${submittingDoc ? 'document-upload-card__label--disabled' : ''}`}
          >
            {isUploading ? (
              <span>
                Uploading... {Math.round(progress)}%
                <div className="document-upload-card__progress">
                  <div 
                    className="document-upload-card__progress-bar"
                    style={{ width: `${progress}%` }}
                  />
                </div>
              </span>
            ) : progress === 100 ? (
              '✅ Uploaded Successfully'
            ) : (
              '📎 Choose File'
            )}
          </label>
        </div>
        
        <div className="document-upload-card__info">
          <p>Accepted formats: JPG, PNG, PDF</p>
          <p>Maximum size: 10MB</p>
        </div>
      </div>
    );
  };

  const RequirementItem = ({ requirement, isOverdue = false }) => (
    <div className={`requirement-item ${isOverdue ? 'requirement-item--overdue' : ''}`}>
      <span className="requirement-item__icon">
        {isOverdue ? '🚨' : '📋'}
      </span>
      <span className="requirement-item__text">
        {requirement.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
      </span>
      {isOverdue && (
        <span className="requirement-item__badge">Overdue</span>
      )}
    </div>
  );

  if (loading) {
    return (
      <div className="vendor-verification">
        <div className="vendor-verification__loading">
          <div className="spinner"></div>
          <p>Loading verification status...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="vendor-verification">
        <div className="vendor-verification__error">
          <h2>Error Loading Verification Status</h2>
          <p>{error}</p>
          <button 
            onClick={fetchVerificationStatus}
            className="btn btn-primary"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  const { 
    verificationStatus: status, 
    requiresAdditionalVerification, 
    additionalVerificationDetails,
    verificationRequirements,
    stripeAccount 
  } = verificationStatus;

  const isFullyVerified = status === 'approved' && 
    stripeAccount?.chargesEnabled && 
    stripeAccount?.payoutsEnabled;

  return (
    <div className="vendor-verification">
      <VerificationStatusBanner />
      
      <div className="vendor-verification__header">
        <h1>Account Verification</h1>
        <div className={`verification-status verification-status--${status}`}>
          <span className="verification-status__icon">
            {status === 'approved' ? '✅' : 
             status === 'pending' ? '⏳' : 
             status === 'suspended' ? '❌' : '📋'}
          </span>
          <span className="verification-status__text">
            {status === 'approved' ? 'Verified' :
             status === 'pending' ? 'Pending Review' :
             status === 'suspended' ? 'Suspended' : 'In Progress'}
          </span>
        </div>
      </div>

      {isFullyVerified ? (
        <div className="verification-complete">
          <div className="verification-complete__icon">🎉</div>
          <h2>Verification Complete!</h2>
          <p>Your vendor account is fully verified and operational. You can now accept bookings and receive payments.</p>
          
          <div className="verification-complete__capabilities">
            <div className="capability">
              <span className="capability__icon">💳</span>
              <span>Payment Processing: Enabled</span>
            </div>
            <div className="capability">
              <span className="capability__icon">💰</span>
              <span>Payouts: Enabled</span>
            </div>
            <div className="capability">
              <span className="capability__icon">📅</span>
              <span>Booking Acceptance: Enabled</span>
            </div>
          </div>
        </div>
      ) : (
        <div className="verification-requirements">
          {/* Current Requirements */}
          {verificationRequirements?.currentlyDue?.length > 0 && (
            <div className="requirement-section">
              <h2>Current Requirements</h2>
              <p>Please complete the following to continue verification:</p>
              <div className="requirement-list">
                {verificationRequirements.currentlyDue.map((req, index) => (
                  <RequirementItem key={index} requirement={req} />
                ))}
              </div>
            </div>
          )}

          {/* Overdue Requirements */}
          {verificationRequirements?.pastDue?.length > 0 && (
            <div className="requirement-section requirement-section--urgent">
              <h2>Overdue Requirements</h2>
              <p>These requirements are past due and need immediate attention:</p>
              <div className="requirement-list">
                {verificationRequirements.pastDue.map((req, index) => (
                  <RequirementItem key={index} requirement={req} isOverdue={true} />
                ))}
              </div>
            </div>
          )}

          {/* Additional Verification Documents */}
          {requiresAdditionalVerification && additionalVerificationDetails?.requiredDocuments?.length > 0 && (
            <div className="requirement-section">
              <h2>Additional Documents Required</h2>
              <p>Stripe requires additional verification documents:</p>
              
              {additionalVerificationDetails.dueDate && (
                <div className="due-date-notice">
                  <span>⏰ Due: {new Date(additionalVerificationDetails.dueDate).toLocaleDateString()}</span>
                </div>
              )}

              <div className="document-upload-grid">
                {additionalVerificationDetails.requiredDocuments.includes('identity_document') && (
                  <DocumentUploadCard
                    documentType="identity_document"
                    title="Government ID"
                    description="Upload a clear photo of your government-issued ID (passport, driver's license, or state ID)"
                    required={true}
                  />
                )}
                
                {additionalVerificationDetails.requiredDocuments.includes('additional_document') && (
                  <DocumentUploadCard
                    documentType="additional_document"
                    title="Additional Document"
                    description="Upload any additional document requested by Stripe for verification"
                    required={true}
                  />
                )}
              </div>
            </div>
          )}

          {/* Account Capabilities Status */}
          <div className="capability-status">
            <h2>Account Capabilities</h2>
            <div className="capability-grid">
              <div className={`capability-item ${stripeAccount?.chargesEnabled ? 'capability-item--enabled' : 'capability-item--disabled'}`}>
                <span className="capability-item__icon">💳</span>
                <div className="capability-item__details">
                  <h3>Payment Processing</h3>
                  <p>{stripeAccount?.chargesEnabled ? 'Enabled' : 'Disabled'}</p>
                </div>
              </div>
              
              <div className={`capability-item ${stripeAccount?.payoutsEnabled ? 'capability-item--enabled' : 'capability-item--disabled'}`}>
                <span className="capability-item__icon">💰</span>
                <div className="capability-item__details">
                  <h3>Payouts</h3>
                  <p>{stripeAccount?.payoutsEnabled ? 'Enabled' : 'Disabled'}</p>
                </div>
              </div>
              
              <div className={`capability-item ${status === 'approved' ? 'capability-item--enabled' : 'capability-item--disabled'}`}>
                <span className="capability-item__icon">📅</span>
                <div className="capability-item__details">
                  <h3>Booking Acceptance</h3>
                  <p>{status === 'approved' ? 'Enabled' : 'Disabled'}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Help Section */}
          <div className="verification-help">
            <h2>Need Help?</h2>
            <p>If you're having trouble with verification or have questions about the requirements:</p>
            <div className="help-actions">
              <button 
                onClick={fetchVerificationStatus}
                className="btn btn-secondary"
                disabled={loading}
              >
                Refresh Status
              </button>
              <a href="/support" className="btn btn-primary">
                Contact Support
              </a>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VendorVerification;
