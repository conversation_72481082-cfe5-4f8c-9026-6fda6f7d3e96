.pp-product-card {
  background: var(--pp-clr-primary);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-12);
  box-shadow: var(--pp-shdw-sm);
  overflow: hidden;
  display: grid;

  transition: transform 300ms ease, box-shadow 300ms ease;
}

.pp-product-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--pp-shdw-md);
}

.pp-product-card__image-wrap {
  height: 230px;
}
.pp-product-card__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.pp-product-card__content {
  padding: var(--pp-sp-24) var(--pp-sp-16);
  height: 170px;
  display: grid;
  align-content: space-between;
  gap: 10px;
}

.pp-product-card__title {
  font-size: var(--pp-font-heading6);
  font-weight: 600;
  color: var(--pp-clr-text-main);

  display: -webkit-box;
  -webkit-line-clamp: 1; /* 👈 limit to 2 lines */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pp-product-card__subtitle {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  display: -webkit-box;
  -webkit-line-clamp: 1; /* 👈 limit to 2 lines */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pp-product-card__footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.pp-product-card_category-title {
  color: var(--pp-clr-secondary);
  font-weight: 600;
  font-size: var(--pp-font-base-font);
}
.pp-product-card__price span {
  font-size: var(--pp-font-heading6);
  font-weight: 600;
  margin-left: 4px;
  font-family: "Inter", sans-serif;
  color: var(--pp-clr-secondary);
}
