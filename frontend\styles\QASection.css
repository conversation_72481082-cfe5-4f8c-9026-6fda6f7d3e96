.pp-qa-section {
    padding-bottom: var(--pp-sp-60);
    margin-bottom: var(--pp-sp-60);
  }
.pp-qa-section h2{
  font-size: var(--pp-font-heading4);
  
  padding: var(--pp-sp-16) 0;
  border-bottom: 1px solid var(--pp-clr-border-light);
}
  
  .pp-qa-list {
    list-style: none;
    display: flex;
    flex-direction: column;
  
    
  }
  .pp-qa-list span{

    font-weight: 500;
  }
  .pp-qa-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--pp-sp-16) 0;
    border-bottom: 1px solid var(--pp-clr-border-light);
    cursor: pointer;
    transition: background var(--pp-trans-duration-fast) var(--pp-trans-ease);
  }
  
  .pp-qa-item:hover {
    background: var(--pp-clr-card-rentals-bg);
  }
  
  .pp-qa-arrow {
    font-size: var(--pp-font-heading6);
  }
  