import React, { useState, useEffect } from "react";
import "../styles/CustomerProfile.css";
import { Link } from "react-router-dom";
import { useAuth } from "../redux/useAuth";
import { toast } from 'react-toastify';
import { userApi } from '../redux/apiUtils';
import { VITE_IMAGE_URL } from '../redux/apiUtils';
import defaultprofile from "../src/assets/images/defaultprofile.jpg";
export default function CustomerAccountSettings() {
  const { user, getProfile } = useAuth();
  const [form, setForm] = useState({
    firstName: "",
    lastName: "",
    email: "",
  });
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState("");
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);

  // Load user data on component mount
  useEffect(() => {
    if (user) {
      setForm({
        firstName: user.firstName || "",
        lastName: user.lastName || "",
        email: user.email || "",
      });
      setImagePreview(user.profileImage || "");
    }
  }, [user]);

  function handleChange(e) {
    const { name, value } = e.target;
    setForm((f) => ({ ...f, [name]: value }));
  }

  function handleImageChange(e) {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        toast.error('Please select a valid image file (JPG, PNG, WEBP)');
        return;
      }

      // Validate file size (5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('Image size should be less than 5MB');
        return;
      }

      setSelectedImage(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  }

  async function uploadImage() {
    if (!selectedImage) return null;

    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('image', selectedImage);

      const response = await userApi.uploadProfileImage(formData);
      
      toast.success('Profile image uploaded successfully!');
      // Optimistically update preview with the stored URL if provided
      if (response?.data?.profileImage) {
        setImagePreview(response.data.profileImage);
      }
      return response.data.profileImage;
    } catch (error) {
      console.error('Image upload error:', error);
      toast.error(error.message || 'Failed to upload image');
      throw error;
    } finally {
      setUploading(false);
    }
  }

  async function handleSubmit(e) {
    e.preventDefault();
    setLoading(true);

    try {
      // First upload image if selected
      if (selectedImage) {
        await uploadImage();
      }

      // Then update profile data
      const response = await userApi.updateProfile(form);

      toast.success('Profile updated successfully!');
      
      // Clear selected image after successful upload
      setSelectedImage(null);
      
      // Refresh Redux auth user so UI reflects latest profileImage and fields
      try {
        await getProfile();
      } catch (_) {
        // non-fatal: UI will still have local preview
      }
      
    } catch (error) {
      console.error('Profile update error:', error);
      toast.error(error.message || 'Failed to update profile');
    } finally {
      setLoading(false);
    }
  }

  // Resolve preview src:
  // - If a freshly selected image (data URL), use it directly
  // - If server path (no scheme), prefix with VITE_IMAGE_URL
  // - Otherwise fallback to placeholder
  const previewSrc = imagePreview
    ? (imagePreview.startsWith('data:') || imagePreview.startsWith('http')
        ? imagePreview
        : VITE_IMAGE_URL + imagePreview)
    : "https://via.placeholder.com/120x120.png?text=+";

  return (
    <div className="CustomerAccountSettings-root">
      <main className="CustomerAccountSettings-content">
        <form
          className="CustomerAccountSettings-form"
          onSubmit={handleSubmit}
          autoComplete="off"
        >
          {/* My Info Section */}
          <section className="CustomerAccountSettings-section">
            <h2 className="CustomerAccountSettings-sectionTitle">My Info</h2>

            <div className="Customerphotocontainer">
              <div className="CustomerAccountSettings-grid2">
                <div className="pp-form-group">
                  <label className="pp-form-label" htmlFor="firstName">
                    First Name
                  </label>
                  <input
                    className="pp-form-input"
                    id="firstName"
                    name="firstName"
                    placeholder="First Name"
                    value={form.firstName}
                    onChange={handleChange}
                  />
                </div>

                <div className="pp-form-group">
                  <label className="pp-form-label" htmlFor="lastName">
                    Last Name
                  </label>
                  <input
                    className="pp-form-input"
                    id="lastName"
                    name="lastName"
                    placeholder="Last Name"
                    value={form.lastName}
                    onChange={handleChange}
                  />
                </div>

                <div className="pp-form-group CustomerAccountSettings-grid2-colspan">
                  <label className="pp-form-label" htmlFor="email">
                    Email Address
                  </label>
                  <input
                    className="pp-form-input"
                    id="email"
                    name="email"
                    placeholder="Email Address"
                    value={form.email}
                    onChange={handleChange}
                    type="email"
                    readOnly
                  />
                </div>
              </div>

              {/* Profile Photo Upload */}
              <div className="customerprofilephoto">
                <h3 className="profilephoto-title">Profile Image</h3>
                <div className="profilephoto-preview">
                  <img
                    src={previewSrc || defaultprofile}
                    alt="Profile Preview"
                    className="profilephoto-img"
                  />
                </div>
                <label className="upload-btn pp-btn-secondary">
                  {uploading ? 'Uploading...' : 'Upload Photo'}
                  <input
                    type="file"
                    accept="image/jpeg,image/jpg,image/png,image/webp"
                    onChange={handleImageChange}
                    disabled={uploading}
                    hidden
                  />
                </label>
                {selectedImage && (
                  <p className="selected-file-info">
                    Selected: {selectedImage.name}
                  </p>
                )}
              </div>
            </div>
          </section>

          <div className="CustomerAccountSettings-divider" />

          {/* Save Button */}
          <div className="CustomerAccountSettings-formActions">
            <button
              type="submit"
              className="pp-btn pp-btn-primary CustomerAccountSettings-saveBtn"
              disabled={loading || uploading}
            >
              {loading ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      </main>
    </div>
  );
}
