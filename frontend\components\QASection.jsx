import React from "react";
import { FaArrowRightLong } from "react-icons/fa6";



const questions = [
  "Nulla placerat quam a sem molestie euismod?",
  "Fusce ultrices tellus vitae augue imperdiet, convallis pellentesque?",
  "Aenean vitae pellentesque nisl?",
  "Vestibulum id risus vel massa fermentum facilisis?",
  "ed pharetra risus ac purus?",
];

const QASection = () => {
  return (
    <section className="pp-qa-section ">
      <h2>Q&A</h2>
      <ul className="pp-qa-list">
        {questions.map((q, i) => (
          <li key={i} className="pp-qa-item">
            <span>{q}</span>
            <span className="pp-qa-arrow">
            <FaArrowRightLong />

            </span>
          </li>
        ))}
      </ul>
    </section>
  );
};

export default QASection;
