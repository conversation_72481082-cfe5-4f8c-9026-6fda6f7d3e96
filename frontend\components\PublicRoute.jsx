import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../redux/useAuth';
import { getVendorRedirectPath } from '../utils/vendorUtils';

/**
 * Public Route component for login/signup pages
 * Redirects authenticated users to appropriate dashboard
 */
const PublicRoute = ({ children }) => {
  const navigate = useNavigate();
  const { isAuthenticated, user, loading, vendorOnboardingStatus } = useAuth();
  const [redirecting, setRedirecting] = useState(false);

  useEffect(() => {
    if (!loading && isAuthenticated && user) {
      setRedirecting(true);

      // Redirect based on user role
      if (user.role === 'vendor') {
        // Use onboarding status to determine redirect path
        const redirectPath = getVendorRedirectPath(vendorOnboardingStatus);
        navigate(redirectPath);
      } else if (user.role === 'customer') {
        navigate('/');
      } else {
        navigate('/');
      }
    }
  }, [isAuthenticated, user, loading, vendorOnboardingStatus, navigate]);

  // Show loading while checking authentication or redirecting
  if (loading || redirecting) {
    return (
      <div className="auth-loading">
        <div>Loading...</div>
      </div>
    );
  }

  // Don't render children if authenticated (will redirect)
  if (isAuthenticated) {
    return (
      <div className="auth-loading">
        <div>Redirecting...</div>
      </div>
    );
  }

  return children;
};

export default PublicRoute;
