import React, { useEffect, useRef } from "react";
import "../../styles/CustomerMessages.css";
import { BsArrowRightCircleFill } from "react-icons/bs";
import { MdArrowBackIos } from "react-icons/md";

/**
 * Reusable Messages UI that mirrors CustomerMessages design and CSS.
 * Props:
 * - title: string (e.g., "Messages")
 * - contacts: [{ id, name, avatar, lastMessage, lastDate, unread }]
 * - selectedChatId: number|string
 * - isSidebarOpen: boolean
 * - onOpenConversation: (id) => void
 * - onBackToList: () => void
 * - messages: [{ id, sender, avatar, date, text, isUser }]
 * - composeAvatar: image src for current user's avatar (optional)
 * - inputValue: string
 * - onInputChange: (e) => void
 * - onSend: (e) => void (preventDefault should be handled by parent or here)
 */
export default function MessagesUI({
  title = "Messages",
  contacts = [],
  selectedChatId,
  isSidebarOpen,
  onOpenConversation,
  onBackToList,
  messages = [],
  composeAvatar,
  inputValue,
  onInputChange,
  onSend,
}) {
  const chatThreadRef = useRef(null);

  useEffect(() => {
    if (chatThreadRef.current) {
      chatThreadRef.current.scrollTop = chatThreadRef.current.scrollHeight;
    }
  }, [messages.length, selectedChatId]);

  const selected = contacts.find((c) => c.id === selectedChatId);

  return (
    <section className="CustomerMessages-section">
      <div
        className={`CustomerMessages-root ${
          isSidebarOpen ? "CustomerMessages--sidebar-open" : ""
        } ${!isSidebarOpen && selectedChatId ? "CustomerMessages--chat-active" : ""}`}
      >
        
        <div className="CustomerMessages-mainOnly">
          <aside className="CustomerMessages-chatlist">
            <ul className="CustomerMessages-contacts">
              {contacts.map((c) => (
                <li
                  key={c.id}
                  className={`CustomerMessages-contactItem$${selectedChatId === c.id ? " CustomerMessages-contactItem--active" : ""}`.replace("$", "")}
                  onClick={() => onOpenConversation && onOpenConversation(c.id)}
                  tabIndex={0}
                >
                  {c.avatar ? (
                    <img src={c.avatar} alt={c.name} className="CustomerMessages-avatar" />
                  ) : (
                    <div className="CustomerMessages-avatar" aria-hidden="true" />
                  )}
                  <div className="CustomerMessages-contactMain">
                    <div className="CustomerMessages-contactTop">
                      <div className="grid">
                      <span className="CustomerMessages-name">{c.name}</span>
                      <div className="CustomerMessages-preview">{c.lastMessage}</div>
                      </div>
                      <div className="grid">
                      <span className="CustomerMessages-date">{c.lastDate}</span>
                      {c.unread > 0 && (
                    <span className="CustomerMessages-badge">{c.unread}</span>
                  )}
                      </div>
                    </div>
                    
                  </div>
                  
                </li>
              ))}
            </ul>
          </aside>

          <section className="CustomerMessages-chatThread">
            <div className="CustomerMessages-threadHeader">
              <div>
                <button
                  type="button"
                  className="CustomerMessages-mobileBtn"
                  aria-label="Back to conversations"
                  onClick={onBackToList}
                >
                  <MdArrowBackIos />
                </button>
                <span className="CustomerMessages-threadTitle">{selected?.name}</span>
              </div>

              <button className="CustomerMessages-bookedBtn">View Booked Events</button>
            </div>

            <div className="CustomerMessages-messagesArea" ref={chatThreadRef}>
              {messages.map((msg) => (
                <div
                  key={msg.id}
                  className={`CustomerMessages-messageRow ${msg.isUser ? "CustomerMessages-messageRow--user" : ""}`}
                >
                  {msg.avatar ? (
                    <img src={msg.avatar} alt={msg.sender} className="CustomerMessages-msgAvatar" />
                  ) : (
                    <div className="CustomerMessages-msgAvatar" aria-hidden="true" />
                  )}

                  <div style={{ flex: 1, display: "flex", flexDirection: "column" }}>
                    <div className="CustomerMessages-msgMeta">
                      <span className="CustomerMessages-msgSender">{msg.sender}</span>
                      <span className="CustomerMessages-msgDate">{msg.date}</span>
                    </div>
                    <div className="CustomerMessages-msgText">{msg.text}</div>
                  </div>
                </div>
              ))}
            </div>
         <div className="flex items-center gap-2 w-full">
         {composeAvatar ? (
                <img src={composeAvatar} alt="user" className="CustomerMessages-msgAvatar" />
              ) : (
                <div className="CustomerMessages-msgAvatar" aria-hidden="true" />
              )}
            <form className="CustomerMessages-compose" onSubmit={onSend} autoComplete="off">
          
              <input
                className="CustomerMessages-input"
                type="text"
                placeholder="Type your message…"
                value={inputValue}
                onChange={onInputChange}
                autoFocus
              />
              <button className="CustomerMessages-sendBtn" type="submit" aria-label="Send">
                <span>
                  <BsArrowRightCircleFill />
                </span>
              </button>
            </form>
         </div>
          </section>
        </div>
      </div>
    </section>
  );
}
