const Service = require('../models/Service');
const Category = require('../models/Category');
const VendorProfile = require('../models/VendorProfile');
const { paginate, paginateAggregate } = require('../utils/pagination');
const { apiLogger } = require('../utils/logger');

/**
 * Search Service
 * Handles all search and filtering operations for services, vendors, and content
 */
class SearchService {
    /**
     * Search services with advanced filtering
     */
    static async searchServices(searchParams, options = {}) {
        try {
            const {
                q, // text query
                category,
                subcategory,
                location,
                radius = 25,
                minPrice,
                maxPrice,
                minRating,
                vendorId,
                availability,
                sortBy = 'relevance',
                page = 1,
                limit = 20
            } = searchParams;

            let aggregationPipeline = [];
            let matchStage = { status: 'active' };

            // Text search
            if (q) {
                matchStage.$text = { $search: q };
                aggregationPipeline.push({
                    $addFields: {
                        searchScore: { $meta: 'textScore' }
                    }
                });
            }

            // Category filters
            if (category) {
                const categoryDoc = await Category.findOne({
                    $or: [{ _id: category }, { slug: category }]
                });
                if (categoryDoc) {
                    matchStage.categoryId = categoryDoc._id;
                }
            }

            if (subcategory) {
                const subcategoryDoc = await Category.findOne({
                    $or: [{ _id: subcategory }, { slug: subcategory }]
                });
                if (subcategoryDoc) {
                    matchStage.subcategoryId = subcategoryDoc._id;
                }
            }

            // Vendor filter
            if (vendorId) {
                matchStage.vendorId = vendorId;
            }

            // Rating filter
            if (minRating) {
                matchStage['ratings.average'] = { $gte: parseFloat(minRating) };
            }

            // Price range filter (complex due to different pricing methods)
            if (minPrice || maxPrice) {
                const priceConditions = [];

                if (minPrice && maxPrice) {
                    priceConditions.push({
                        $and: [
                            { 'pricing.hourlyRate': { $gte: parseFloat(minPrice), $lte: parseFloat(maxPrice) } },
                            { 'pricing.method': 'hourly' }
                        ]
                    });
                    priceConditions.push({
                        $and: [
                            { 'pricing.packages.price': { $gte: parseFloat(minPrice), $lte: parseFloat(maxPrice) } },
                            { 'pricing.method': 'package' }
                        ]
                    });
                    priceConditions.push({
                        $and: [
                            { 'pricing.perPersonRate': { $gte: parseFloat(minPrice), $lte: parseFloat(maxPrice) } },
                            { 'pricing.method': 'per_person' }
                        ]
                    });
                    priceConditions.push({
                        $and: [
                            { 'pricing.flatRate': { $gte: parseFloat(minPrice), $lte: parseFloat(maxPrice) } },
                            { 'pricing.method': 'flat_rate' }
                        ]
                    });
                }

                if (priceConditions.length > 0) {
                    matchStage.$or = priceConditions;
                }
            }

            // Location-based search
            if (location && location.lat && location.lng) {
                aggregationPipeline.unshift({
                    $geoNear: {
                        near: {
                            type: 'Point',
                            coordinates: [parseFloat(location.lng), parseFloat(location.lat)]
                        },
                        distanceField: 'distance',
                        maxDistance: radius * 1609.34, // Convert miles to meters
                        spherical: true
                    }
                });
            }

            // Apply match stage
            aggregationPipeline.push({ $match: matchStage });

            // Join with vendor information
            aggregationPipeline.push({
                $lookup: {
                    from: 'users',
                    localField: 'vendorId',
                    foreignField: '_id',
                    as: 'vendor'
                }
            });

            aggregationPipeline.push({
                $lookup: {
                    from: 'vendorprofiles',
                    localField: 'vendorId',
                    foreignField: 'userId',
                    as: 'vendorProfile'
                }
            });

            // Join with category information
            aggregationPipeline.push({
                $lookup: {
                    from: 'categories',
                    localField: 'categoryId',
                    foreignField: '_id',
                    as: 'category'
                }
            });

            aggregationPipeline.push({
                $lookup: {
                    from: 'categories',
                    localField: 'subcategoryId',
                    foreignField: '_id',
                    as: 'subcategory'
                }
            });

            // Unwind arrays
            aggregationPipeline.push(
                { $unwind: { path: '$vendor', preserveNullAndEmptyArrays: true } },
                { $unwind: { path: '$vendorProfile', preserveNullAndEmptyArrays: true } },
                { $unwind: { path: '$category', preserveNullAndEmptyArrays: true } },
                { $unwind: { path: '$subcategory', preserveNullAndEmptyArrays: true } }
            );

            // Filter by vendor verification status
            aggregationPipeline.push({
                $match: {
                    'vendorProfile.verification.status': 'approved',
                    'vendorProfile.isActive': true
                }
            });

            // Calculate minimum price for sorting
            aggregationPipeline.push({
                $addFields: {
                    minPrice: {
                        $switch: {
                            branches: [
                                {
                                    case: { $eq: ['$pricing.method', 'hourly'] },
                                    then: '$pricing.hourlyRate'
                                },
                                {
                                    case: { $eq: ['$pricing.method', 'package'] },
                                    then: { $min: '$pricing.packages.price' }
                                },
                                {
                                    case: { $eq: ['$pricing.method', 'per_person'] },
                                    then: { $multiply: ['$pricing.perPersonRate', '$pricing.minPeople'] }
                                },
                                {
                                    case: { $eq: ['$pricing.method', 'flat_rate'] },
                                    then: '$pricing.flatRate'
                                }
                            ],
                            default: 0
                        }
                    }
                }
            });

            // Sorting
            let sortStage = {};
            switch (sortBy) {
                case 'price_asc':
                    sortStage = { minPrice: 1, 'ratings.average': -1 };
                    break;
                case 'price_desc':
                    sortStage = { minPrice: -1, 'ratings.average': -1 };
                    break;
                case 'rating':
                    sortStage = { 'ratings.average': -1, 'ratings.count': -1 };
                    break;
                case 'newest':
                    sortStage = { createdAt: -1 };
                    break;
                case 'popular':
                    sortStage = { bookingCount: -1, viewCount: -1 };
                    break;
                case 'distance':
                    if (location && location.lat && location.lng) {
                        sortStage = { distance: 1 };
                    } else {
                        sortStage = { 'ratings.average': -1 };
                    }
                    break;
                case 'relevance':
                default:
                    if (q) {
                        sortStage = { searchScore: { $meta: 'textScore' } };
                    } else {
                        sortStage = { isFeatured: -1, 'ratings.average': -1 };
                    }
            }

            aggregationPipeline.push({ $sort: sortStage });

            // Project final fields
            aggregationPipeline.push({
                $project: {
                    title: 1,
                    description: 1,
                    shortDescription: 1,
                    images: 1,
                    pricing: 1,
                    minPrice: 1,
                    ratings: 1,
                    bookingCount: 1,
                    viewCount: 1,
                    isFeatured: 1,
                    location: 1,
                    distance: 1,
                    searchScore: 1,
                    vendor: {
                        _id: 1,
                        firstName: 1,
                        lastName: 1,
                        profileImage: 1
                    },
                    vendorProfile: {
                        businessName: 1,
                        ratings: 1,
                        verification: 1
                    },
                    category: {
                        _id: 1,
                        name: 1,
                        slug: 1
                    },
                    subcategory: {
                        _id: 1,
                        name: 1,
                        slug: 1
                    },
                    createdAt: 1,
                    updatedAt: 1
                }
            });

            // Paginate results
            const result = await paginateAggregate(Service, aggregationPipeline, {
                page,
                limit
            });

            apiLogger.info('Service search completed', {
                query: q,
                filters: { category, subcategory, location, minPrice, maxPrice, minRating },
                resultCount: result.data.length,
                totalResults: result.pagination.totalItems,
                sortBy
            });

            return {
                success: true,
                services: result.data,
                pagination: result.pagination,
                searchParams: {
                    query: q,
                    filters: searchParams,
                    sortBy
                }
            };

        } catch (error) {
            apiLogger.error('Service search failed', {
                searchParams,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Get search suggestions/autocomplete
     */
    static async getSearchSuggestions(query, limit = 10) {
        try {
            const suggestions = [];

            // Service title suggestions
            const services = await Service.find({
                title: { $regex: query, $options: 'i' },
                status: 'active'
            })
                .select('title')
                .limit(limit / 2)
                .lean();

            suggestions.push(...services.map(s => ({
                type: 'service',
                text: s.title,
                id: s._id
            })));

            // Category suggestions
            const categories = await Category.find({
                name: { $regex: query, $options: 'i' },
                status: 'active'
            })
                .select('name slug')
                .limit(limit / 2)
                .lean();

            suggestions.push(...categories.map(c => ({
                type: 'category',
                text: c.name,
                slug: c.slug,
                id: c._id
            })));

            return {
                success: true,
                suggestions: suggestions.slice(0, limit)
            };

        } catch (error) {
            apiLogger.error('Search suggestions failed', {
                query,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Search vendors
     */
    static async searchVendors(searchParams, options = {}) {
        try {
            const {
                q,
                location,
                radius = 25,
                category,
                minRating,
                serviceArea,
                sortBy = 'rating',
                page = 1,
                limit = 20
            } = searchParams;

            let matchStage = {
                'verification.status': 'approved',
                isActive: true
            };

            // Text search on business name and description
            if (q) {
                matchStage.$text = { $search: q };
            }

            // Rating filter
            if (minRating) {
                matchStage['ratings.average'] = { $gte: parseFloat(minRating) };
            }

            // Service area filter
            if (serviceArea) {
                matchStage.serviceAreas = { $regex: serviceArea, $options: 'i' };
            }

            let aggregationPipeline = [
                { $match: matchStage },
                {
                    $lookup: {
                        from: 'users',
                        localField: 'userId',
                        foreignField: '_id',
                        as: 'user'
                    }
                },
                { $unwind: '$user' },
                {
                    $lookup: {
                        from: 'services',
                        localField: 'userId',
                        foreignField: 'vendorId',
                        as: 'services'
                    }
                }
            ];

            // Category filter (based on services)
            if (category) {
                const categoryDoc = await Category.findOne({
                    $or: [{ _id: category }, { slug: category }]
                });
                if (categoryDoc) {
                    aggregationPipeline.push({
                        $match: {
                            'services.categoryId': categoryDoc._id
                        }
                    });
                }
            }

            // Sorting
            let sortStage = {};
            switch (sortBy) {
                case 'rating':
                    sortStage = { 'ratings.average': -1, 'ratings.count': -1 };
                    break;
                case 'newest':
                    sortStage = { createdAt: -1 };
                    break;
                case 'popular':
                    sortStage = { 'metrics.totalBookings': -1 };
                    break;
                case 'relevance':
                default:
                    if (q) {
                        sortStage = { score: { $meta: 'textScore' } };
                    } else {
                        sortStage = { isFeatured: -1, 'ratings.average': -1 };
                    }
            }

            aggregationPipeline.push({ $sort: sortStage });

            // Project final fields
            aggregationPipeline.push({
                $project: {
                    businessName: 1,
                    description: 1,
                    serviceAreas: 1,
                    ratings: 1,
                    verification: 1,
                    featuredImages: 1,
                    isFeatured: 1,
                    metrics: 1,
                    user: {
                        _id: 1,
                        firstName: 1,
                        lastName: 1,
                        profileImage: 1
                    },
                    serviceCount: { $size: '$services' },
                    categories: { $setUnion: ['$services.categoryId'] },
                    createdAt: 1
                }
            });

            const result = await paginateAggregate(VendorProfile, aggregationPipeline, {
                page,
                limit
            });

            return {
                success: true,
                vendors: result.data,
                pagination: result.pagination
            };

        } catch (error) {
            apiLogger.error('Vendor search failed', {
                searchParams,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Get popular search terms
     */
    static async getPopularSearches(limit = 10) {
        try {
            // In production, this would track actual search queries
            // For now, return static popular terms
            const popularSearches = [
                'DJ',
                'Photographer',
                'Catering',
                'Wedding band',
                'Party rentals',
                'Photo booth',
                'Bartender',
                'Decorator',
                'Live music',
                'Food truck'
            ];

            return {
                success: true,
                searches: popularSearches.slice(0, limit)
            };

        } catch (error) {
            apiLogger.error('Popular searches failed', {
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Get featured services
     */
    static async getFeaturedServices(limit = 6) {
        try {
            const services = await Service.find({
                status: 'active',
                isFeatured: true,
                $or: [
                    { featuredUntil: { $gte: new Date() } },
                    { featuredUntil: { $exists: false } }
                ]
            })
                .populate('vendorId', 'firstName lastName profileImage')
                .populate('vendorProfile', 'businessName ratings verification')
                .populate('categoryId', 'name slug')
                .sort({ 'ratings.average': -1, bookingCount: -1 })
                .limit(limit)
                .lean();

            return {
                success: true,
                services
            };

        } catch (error) {
            apiLogger.error('Featured services fetch failed', {
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Get services by location
     */
    static async getServicesByLocation(location, radius = 25, options = {}) {
        try {
            const { category, limit = 20 } = options;

            let pipeline = [];

            // Geo search
            if (location.lat && location.lng) {
                pipeline.push({
                    $geoNear: {
                        near: {
                            type: 'Point',
                            coordinates: [parseFloat(location.lng), parseFloat(location.lat)]
                        },
                        distanceField: 'distance',
                        maxDistance: radius * 1609.34,
                        spherical: true,
                        query: { status: 'active' }
                    }
                });
            }

            // Category filter
            if (category) {
                const categoryDoc = await Category.findOne({
                    $or: [{ _id: category }, { slug: category }]
                });
                if (categoryDoc) {
                    pipeline.push({
                        $match: { categoryId: categoryDoc._id }
                    });
                }
            }

            // Join vendor info
            pipeline.push(
                {
                    $lookup: {
                        from: 'vendorprofiles',
                        localField: 'vendorId',
                        foreignField: 'userId',
                        as: 'vendorProfile'
                    }
                },
                { $unwind: '$vendorProfile' },
                {
                    $match: {
                        'vendorProfile.verification.status': 'approved',
                        'vendorProfile.isActive': true
                    }
                }
            );

            pipeline.push(
                { $sort: { distance: 1, 'ratings.average': -1 } },
                { $limit: limit }
            );

            const services = await Service.aggregate(pipeline);

            return {
                success: true,
                services,
                location,
                radius
            };

        } catch (error) {
            apiLogger.error('Location-based services search failed', {
                location,
                radius,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Advanced search with filters
     */
    static async advancedSearch(filters, options = {}) {
        try {
            const {
                query,
                categories,
                priceRange,
                dateRange,
                location,
                radius,
                ratings,
                features,
                availability,
                sortBy,
                page = 1,
                limit = 20
            } = filters;

            let aggregationPipeline = [];
            let matchStage = { status: 'active' };

            // Build complex query based on filters
            // This is a simplified version - in production, implement more sophisticated filtering

            const result = await this.searchServices(filters, options);

            return result;

        } catch (error) {
            apiLogger.error('Advanced search failed', {
                filters,
                error: error.message
            });
            throw error;
        }
    }
}

module.exports = SearchService;
