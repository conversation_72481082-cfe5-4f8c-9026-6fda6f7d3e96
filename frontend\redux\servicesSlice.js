import { createSlice, createSelector } from "@reduxjs/toolkit";
import servicesData from "../src/servicesData.json";
import CharactersImg from "../src/assets/images/categories/character.svg";
import EntertainmentImg from "../src/assets/images/categories/Entertainments.svg";
import RentalsImg from "../src/assets/images/categories/Rental.svg";
import FoodImg from "../src/assets/images/categories/food.jpg";
import MusicImg from "../src/assets/images/categories/Music.svg";

const categoryToImageMap = {
  characters: CharactersImg,
  entertainment: EntertainmentImg,
  rental: RentalsImg,
  food: FoodImg,
  music: MusicImg,
};

// Each category gets a gallery of exactly 5 local images: first from its own category,
// followed by four other category images (re-used across categories)
const categoryToGalleryMap = {
  characters: [CharactersImg, EntertainmentImg, FoodImg, MusicImg, RentalsImg],
  entertainment: [
    EntertainmentImg,
    MusicImg,
    FoodImg,
    RentalsImg,
    CharactersImg,
  ],
  rental: [RentalsImg, EntertainmentImg, FoodImg, MusicImg, CharactersImg],
  food: [FoodImg, EntertainmentImg, MusicImg, RentalsImg, CharactersImg],
  music: [MusicImg, EntertainmentImg, FoodImg, RentalsImg, CharactersImg],
};

const normalizedItems = (servicesData?.services || []).map((service) => ({
  // keep all original fields for details page
  ...service,
  // enrich for list/card UI parity
  id: service.id,
  title: service.title,
  subtitle: service.vendor,
  price: service.basePrice,
  category: service.category,
  image:
    categoryToImageMap[service.category] || categoryToImageMap.entertainment,
  gallery: categoryToGalleryMap[service.category] || [
    categoryToImageMap[service.category] || categoryToImageMap.entertainment,
    EntertainmentImg,
    FoodImg,
    MusicImg,
    RentalsImg,
  ],
}));

const initialState = {
  items: normalizedItems, // Initialized from JSON for immediate availability
  filters: {
    query: "",
    location: "",
    time: "",
    category: "",
    subCategory: "",
    vendor: "",
    date: "",
    sort: "", // price-asc | price-desc | distance-asc | distance-desc
  },
  // Optionally: selectedServiceId or details can be added if you want to track the currently viewed service
  // selectedServiceId: null,
  // details: {},
};

const servicesSlice = createSlice({
  name: "services",
  initialState,
  reducers: {
    setServices: (state, action) => {
      state.items = action.payload; // payload = unified service array
    },
    addService: (state, action) => {
      state.items.push(action.payload);
    },
    setFilter: (state, action) => {
      const { name, value } = action.payload;
      if (Object.prototype.hasOwnProperty.call(state.filters, name)) {
        state.filters[name] = value;
      }
    },
    clearFilters: (state) => {
      state.filters = {
        query: "",
        location: "",
        time: "",
        category: "",
        subCategory: "",
        vendor: "",
        date: "",
        sort: "",
      };
    },
    // Optional: setDetails for fetching/loading a single service detail by ID
    // setServiceDetails: (state, action) => {
    //   const { id, details } = action.payload;
    //   state.details[id] = details;
    // },
    // setSelectedService: (state, action) => {
    //   state.selectedServiceId = action.payload;
    // },
  },
});

export const {
  setServices,
  addService,
  setFilter,
  clearFilters,
  // setServiceDetails,
  // setSelectedService,
} = servicesSlice.actions;
export default servicesSlice.reducer;

// Selectors for scalable, reusable access
export const selectServices = (state) => state.services.items || [];
export const selectFilters = (state) => state.services.filters;
export const selectServiceById = (state, id) =>
  (state.services.items || []).find((s) => String(s.id) === String(id));

export const selectFilteredServices = createSelector(
  [selectServices, selectFilters],
  (items, filters) => {
    const query = (filters.query || "").toLowerCase();
    const location = filters.location || "";
    const time = filters.time || "";
    const category = filters.category || "";
    const subCategory = filters.subCategory || "";
    const vendor = (filters.vendor || "").toLowerCase();
    const date = filters.date || "";
    const sort = filters.sort || "";

    const filtered = items.filter((s) => {
      const matchesQuery = query
        ? (s.title || "").toLowerCase().includes(query) ||
          (s.subtitle || "").toLowerCase().includes(query)
        : true;
      const matchesLocation = location ? s.location === location : true;
      const matchesTime = time ? s.time === time : true;
      const matchesCategory = category ? s.category === category : true;
      const matchesSubCategory = subCategory
        ? (s.subCategory || "") === subCategory
        : true;
      const matchesVendor = vendor
        ? (s.vendor || s.subtitle || "").toLowerCase() === vendor
        : true;
      const matchesDate = date
        ? Array.isArray(s.availableDates)
          ? s.availableDates.includes(date)
          : true // if no data, don't exclude
        : true;
      return (
        matchesQuery &&
        matchesLocation &&
        matchesTime &&
        matchesCategory &&
        matchesSubCategory &&
        matchesVendor &&
        matchesDate
      );
    });

    const withSort = [...filtered];
    if (sort === "price-asc") {
      withSort.sort((a, b) => (a.price ?? 0) - (b.price ?? 0));
    } else if (sort === "price-desc") {
      withSort.sort((a, b) => (b.price ?? 0) - (a.price ?? 0));
    } else if (sort === "distance-asc") {
      withSort.sort((a, b) => (a.distance ?? 0) - (b.distance ?? 0));
    } else if (sort === "distance-desc") {
      withSort.sort((a, b) => (b.distance ?? 0) - (a.distance ?? 0));
    }
    return withSort;
  }
);
