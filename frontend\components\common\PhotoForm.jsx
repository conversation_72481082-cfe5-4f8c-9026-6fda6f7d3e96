import React, { useState } from "react";
import { MdDelete } from "react-icons/md";

export default function PhotoForm({ photos = [], onChange }) {
  const [dragActive, setDragActive] = useState(false);

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const handleChange = (e) => {
    e.preventDefault();
    if (e.target.files && e.target.files[0]) {
      handleFiles(e.target.files);
    }
  };

  const handleFiles = (files) => {
    const newPhotos = Array.from(files).map((file, index) => ({
      id: Date.now() + index,
      file,
      url: URL.createObjectURL(file),
      isCover: photos.length === 0 && index === 0,
    }));
    onChange([...photos, ...newPhotos]);
  };

  const handleRemovePhoto = (id) => {
    const updatedPhotos = photos.filter((photo) => photo.id !== id);
    // If we're removing the cover photo, make the first remaining photo the cover
    if (photos.find((p) => p.id === id)?.isCover && updatedPhotos.length > 0) {
      updatedPhotos[0].isCover = true;
    }
    onChange(updatedPhotos);
  };

  const handleSetCover = (id) => {
    const updatedPhotos = photos.map((photo) => ({
      ...photo,
      isCover: photo.id === id,
    }));
    onChange(updatedPhotos);
  };

  const renderPhotoItem = (photo, index) => (
    <div key={photo.id} className="pf-photo-item">
      <div className="pf-photo-container">
        <img
          src={photo.url}
          alt={`Service photo ${index + 1}`}
          className="pf-photo"
          loading="lazy"
          decoding="async"
        />
        {photo.isCover && <span className="pf-cover-badge">Cover</span>}
        <div className="pf-photo-overlay">
          <button
            type="button"
            className="pf-btn-remove"
            onClick={() => handleRemovePhoto(photo.id)}
            aria-label="Remove photo"
          >
            <MdDelete />
          </button>
          {!photo.isCover && (
            <button
              type="button"
              className="pf-btn-set-cover"
              onClick={() => handleSetCover(photo.id)}
              aria-label="Set as cover photo"
            >
              Set as Cover
            </button>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="pf-photo-form">
      <h3 className="pf-section-title">Bookable Photos</h3>

      <div className="pf-photos-grid">
        {photos.map(renderPhotoItem)}

        <div className="pf-upload-area">
          <input
            ref={(input) => {
              if (input) {
                input.addEventListener("dragenter", handleDrag);
                input.addEventListener("dragleave", handleDrag);
                input.addEventListener("dragover", handleDrag);
                input.addEventListener("drop", handleDrop);
              }
            }}
            type="file"
            multiple
            accept="image/*,video/*"
            onChange={handleChange}
            className="pf-file-input"
            id="photo-upload"
          />
          <label
            htmlFor="photo-upload"
            className={`pf-upload-label ${dragActive ? "pf-drag-active" : ""}`}
          >
            <div className="pf-upload-content">
              <span className="pf-upload-icon">+</span>
              <p className="pf-upload-text">
                Upload or drag & drop photos, videos
              </p>
            </div>
          </label>
        </div>
      </div>

      <div className="pf-upload-info">
        <p className="pf-info-text">
          Upload high-quality photos and videos to showcase your service. The
          first photo will be used as the cover image.
        </p>
        <p className="pf-info-text">
          Supported formats: JPG, PNG, GIF, MP4, MOV (Max 10MB per file)
        </p>
      </div>
    </div>
  );
}
