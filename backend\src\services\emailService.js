const { sendEmail, sendBulkEmails } = require('../config/email');
const { emailLogger } = require('../utils/logger');
const { formatDate, formatDateTime, addTime } = require('../utils/dateHelper');

/**
 * Email Service
 * Handles all email-related operations including user notifications,
 * booking updates, payment confirmations, and marketing emails
 */
class EmailService {
    /**
     * Send welcome email to new users
     */
    static async sendWelcomeEmail(user) {
        try {
            const data = {
                firstName: user.firstName,
                lastName: user.lastName,
                role: user.role,
                dashboardUrl: `${process.env.FRONTEND_URL}/dashboard`,
                supportEmail: process.env.EMAIL_FROM
            };

            await sendEmail(user.email, 'welcome', data);

            emailLogger.info('Welcome email sent', {
                userId: user._id,
                email: user.email,
                role: user.role
            });

            return { success: true };
        } catch (error) {
            emailLogger.error('Failed to send welcome email', {
                userId: user._id,
                email: user.email,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Send email verification
     */
    static async sendEmailVerification(user, verificationToken) {
        try {
            const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${verificationToken}`;

            const data = {
                firstName: user.firstName,
                verificationUrl,
                expiryTime: '24 hours'
            };

            await sendEmail(user.email, 'emailVerification', data);

            emailLogger.info('Email verification sent', {
                userId: user._id,
                email: user.email
            });

            return { success: true };
        } catch (error) {
            emailLogger.error('Failed to send email verification', {
                userId: user._id,
                email: user.email,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Send password reset email
     */
    static async sendPasswordReset(user, resetToken) {
        try {
            const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;

            const data = {
                firstName: user.firstName,
                resetUrl,
                expiryTime: '30 minutes'
            };

            await sendEmail(user.email, 'passwordReset', data);

            emailLogger.info('Password reset email sent', {
                userId: user._id,
                email: user.email
            });

            return { success: true };
        } catch (error) {
            emailLogger.error('Failed to send password reset email', {
                userId: user._id,
                email: user.email,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Send booking confirmation to customer
     */
    static async sendBookingConfirmation(booking, customer, vendor, service) {
        try {
            const data = {
                customerName: `${customer.firstName} ${customer.lastName}`,
                bookingId: booking.bookingId,
                serviceName: service.title,
                vendorName: `${vendor.firstName} ${vendor.lastName}`,
                eventDate: formatDate(booking.eventDetails.date, 'MMMM DD, YYYY'),
                eventTime: booking.eventDetails.startTime,
                eventLocation: `${booking.eventDetails.location.address}, ${booking.eventDetails.location.city}, ${booking.eventDetails.location.state}`,
                totalAmount: booking.serviceDetails.totalCost,
                bookingUrl: `${process.env.FRONTEND_URL}/bookings/${booking._id}`,
                supportEmail: process.env.EMAIL_FROM
            };

            await sendEmail(customer.email, 'bookingConfirmation', data);

            emailLogger.info('Booking confirmation sent to customer', {
                bookingId: booking._id,
                customerId: customer._id,
                customerEmail: customer.email
            });

            return { success: true };
        } catch (error) {
            emailLogger.error('Failed to send booking confirmation', {
                bookingId: booking._id,
                customerId: customer._id,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Send booking request to vendor
     */
    static async sendVendorBookingRequest(booking, customer, vendor, service) {
        try {
            const dashboardUrl = `${process.env.FRONTEND_URL}/vendor/bookings/${booking._id}`;

            const data = {
                vendorName: `${vendor.firstName} ${vendor.lastName}`,
                bookingId: booking.bookingId,
                serviceName: service.title,
                customerName: `${customer.firstName} ${customer.lastName}`,
                eventDate: formatDate(booking.eventDetails.date, 'MMMM DD, YYYY'),
                eventTime: booking.eventDetails.startTime,
                eventLocation: `${booking.eventDetails.location.address}, ${booking.eventDetails.location.city}, ${booking.eventDetails.location.state}`,
                totalAmount: booking.serviceDetails.totalCost,
                attendees: booking.eventDetails.attendees,
                specialRequests: booking.eventDetails.specialRequests || 'None',
                dashboardUrl,
                responseDeadline: formatDateTime(addTime(new Date(), 24, 'hours'), 'MMMM DD, YYYY [at] hh:mm A')
            };

            await sendEmail(vendor.email, 'vendorBookingRequest', data);

            emailLogger.info('Booking request sent to vendor', {
                bookingId: booking._id,
                vendorId: vendor._id,
                vendorEmail: vendor.email
            });

            return { success: true };
        } catch (error) {
            emailLogger.error('Failed to send vendor booking request', {
                bookingId: booking._id,
                vendorId: vendor._id,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Send booking approval notification
     */
    static async sendBookingApproved(booking, customer, vendor, service) {
        try {
            const data = {
                customerName: customer.fullName,
                bookingId: booking.bookingId,
                serviceName: service.title,
                vendorName: vendor.fullName,
                eventDate: formatDate(booking.eventDetails.date, 'MMMM DD, YYYY'),
                eventTime: booking.eventDetails.startTime,
                vendorPhone: vendor.phone,
                vendorEmail: vendor.email,
                bookingUrl: `${process.env.FRONTEND_URL}/bookings/${booking._id}`
            };

            await sendEmail(customer.email, 'bookingApproved', data);

            emailLogger.info('Booking approved notification sent', {
                bookingId: booking._id,
                customerId: customer._id,
                customerEmail: customer.email
            });

            return { success: true };
        } catch (error) {
            emailLogger.error('Failed to send booking approval notification', {
                bookingId: booking._id,
                customerId: customer._id,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Send booking rejection notification
     */
    static async sendBookingRejected(booking, customer, vendor, reason) {
        try {
            const data = {
                customerName: customer.fullName,
                bookingId: booking.bookingId,
                serviceName: booking.serviceName || 'Service',
                reason: reason || 'Vendor unavailable for the requested date',
                refundAmount: booking.serviceDetails.totalCost,
                supportEmail: process.env.EMAIL_FROM
            };

            await sendEmail(customer.email, 'bookingRejected', data);

            emailLogger.info('Booking rejection notification sent', {
                bookingId: booking._id,
                customerId: customer._id,
                customerEmail: customer.email,
                reason
            });

            return { success: true };
        } catch (error) {
            emailLogger.error('Failed to send booking rejection notification', {
                bookingId: booking._id,
                customerId: customer._id,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Send payment confirmation
     */
    static async sendPaymentConfirmation(booking, customer, paymentDetails) {
        try {
            const details = paymentDetails || booking.paymentDetails || {};
            const data = {
                customerName: customer.fullName,
                bookingId: booking.bookingId,
                amount: details.amount,
                paymentMethod: details.paymentMethod || 'Credit Card',
                transactionId: details.transactionId || details.stripeChargeId,
                receiptUrl: details.receiptUrl || `${process.env.FRONTEND_URL}/receipts/${booking._id}`
            };

            await sendEmail(customer.email, 'paymentConfirmation', data);

            emailLogger.info('Payment confirmation sent', {
                bookingId: booking._id,
                customerId: customer._id,
                amount: data.amount
            });

            return { success: true };
        } catch (error) {
            emailLogger.error('Failed to send payment confirmation', {
                bookingId: booking._id,
                customerId: customer._id,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Notify vendor that booking is confirmed
     */
    static async sendBookingConfirmedToVendor(booking, vendor) {
        try {
            const data = {
                vendorName: `${vendor.firstName || vendor.fullName || ''}`.trim(),
                bookingId: booking.bookingId,
                serviceName: booking.serviceId?.title || booking.serviceName || 'Service',
                eventDate: formatDate(booking.eventDetails.date, 'MMMM DD, YYYY'),
                eventTime: booking.eventDetails.startTime,
                eventLocation: `${booking.eventDetails.location.address}, ${booking.eventDetails.location.city}, ${booking.eventDetails.location.state}`,
                amount: booking.paymentDetails?.amount,
                dashboardUrl: `${process.env.FRONTEND_URL}/vendor/bookings/${booking._id}`
            };

            await sendEmail(vendor.email, 'vendorBookingConfirmed', data);

            emailLogger.info('Booking confirmation sent to vendor', {
                bookingId: booking._id,
                vendorId: vendor._id
            });

            return { success: true };
        } catch (error) {
            emailLogger.error('Failed to send booking confirmation to vendor', {
                bookingId: booking._id,
                vendorId: vendor._id,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Notify vendor about customer cancellation
     */
    static async sendBookingCancellationToVendor(booking, vendor, reason) {
        try {
            const data = {
                vendorName: `${vendor.firstName || vendor.fullName || ''}`.trim(),
                bookingId: booking.bookingId,
                serviceName: booking.serviceId?.title || booking.serviceName || 'Service',
                eventDate: formatDate(booking.eventDetails.date, 'MMMM DD, YYYY'),
                reason: reason || booking.cancellationPolicy?.reason || 'No reason provided',
                refundAmount: booking.cancellationPolicy?.refundAmount || 0
            };

            await sendEmail(vendor.email, 'bookingCancellationVendor', data);

            emailLogger.info('Booking cancellation notice sent to vendor', {
                bookingId: booking._id,
                vendorId: vendor._id
            });

            return { success: true };
        } catch (error) {
            emailLogger.error('Failed to send booking cancellation to vendor', {
                bookingId: booking._id,
                vendorId: vendor._id,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Notify customer about vendor cancellation
     */
    static async sendBookingCancellationToCustomer(booking, customer, reason) {
        try {
            const data = {
                customerName: `${customer.firstName || customer.fullName || ''}`.trim(),
                bookingId: booking.bookingId,
                serviceName: booking.serviceId?.title || booking.serviceName || 'Service',
                eventDate: formatDate(booking.eventDetails.date, 'MMMM DD, YYYY'),
                reason: reason || booking.cancellationPolicy?.reason || 'No reason provided',
                refundAmount: booking.cancellationPolicy?.refundAmount || 0
            };

            await sendEmail(customer.email, 'bookingCancellationCustomer', data);

            emailLogger.info('Booking cancellation notice sent to customer', {
                bookingId: booking._id,
                customerId: customer._id
            });

            return { success: true };
        } catch (error) {
            emailLogger.error('Failed to send booking cancellation to customer', {
                bookingId: booking._id,
                customerId: customer._id,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Send refund confirmation to customer
     */
    static async sendRefundConfirmation(booking, customer, amount) {
        try {
            const data = {
                customerName: `${customer.firstName || customer.fullName || ''}`.trim(),
                bookingId: booking.bookingId,
                amount
            };

            await sendEmail(customer.email, 'refundConfirmation', data);

            emailLogger.info('Refund confirmation sent', {
                bookingId: booking._id,
                customerId: customer._id,
                amount
            });

            return { success: true };
        } catch (error) {
            emailLogger.error('Failed to send refund confirmation', {
                bookingId: booking._id,
                customerId: customer._id,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Send event reminder
     */
    static async sendEventReminder(booking, customer, vendor, service) {
        try {
            const data = {
                customerName: customer.fullName,
                eventName: booking.eventDetails.name || 'Your Event',
                serviceName: service.title,
                eventDate: formatDate(booking.eventDetails.date, 'MMMM DD, YYYY'),
                eventTime: booking.eventDetails.startTime,
                eventLocation: `${booking.eventDetails.location.address}, ${booking.eventDetails.location.city}, ${booking.eventDetails.location.state}`,
                vendorName: vendor.fullName,
                vendorPhone: vendor.phone,
                vendorEmail: vendor.email,
                specialRequests: booking.eventDetails.specialRequests || 'None'
            };

            await sendEmail(customer.email, 'eventReminder', data);

            emailLogger.info('Event reminder sent', {
                bookingId: booking._id,
                customerId: customer._id,
                eventDate: booking.eventDetails.date
            });

            return { success: true };
        } catch (error) {
            emailLogger.error('Failed to send event reminder', {
                bookingId: booking._id,
                customerId: customer._id,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Send bulk reminders for upcoming events
     */
    static async sendBulkEventReminders(reminders) {
        try {
            const emails = reminders.map(reminder => ({
                to: reminder.customer.email,
                templateName: 'eventReminder',
                data: {
                    customerName: reminder.customer.fullName,
                    eventName: reminder.booking.eventDetails.name || 'Your Event',
                    serviceName: reminder.service.title,
                    eventDate: formatDate(reminder.booking.eventDetails.date, 'MMMM DD, YYYY'),
                    eventTime: reminder.booking.eventDetails.startTime,
                    eventLocation: `${reminder.booking.eventDetails.location.address}, ${reminder.booking.eventDetails.location.city}`,
                    vendorName: reminder.vendor.fullName,
                    vendorPhone: reminder.vendor.phone,
                    vendorEmail: reminder.vendor.email
                }
            }));

            const result = await sendBulkEmails(emails);

            emailLogger.info('Bulk event reminders sent', {
                totalReminders: reminders.length,
                successful: result.successful,
                failed: result.failed
            });

            return result;
        } catch (error) {
            emailLogger.error('Failed to send bulk event reminders', {
                error: error.message,
                reminderCount: reminders.length
            });
            throw error;
        }
    }

    /**
     * Send vendor payout notification
     */
    static async sendVendorPayout(vendor, payoutDetails) {
        try {
            const data = {
                vendorName: vendor.fullName,
                amount: payoutDetails.amount,
                bookingId: payoutDetails.bookingId,
                payoutDate: formatDate(payoutDetails.payoutDate, 'MMMM DD, YYYY'),
                transferId: payoutDetails.transferId,
                dashboardUrl: `${process.env.FRONTEND_URL}/vendor/earnings`
            };

            // This would use a payout notification template
            await sendEmail(vendor.email, 'vendorPayout', data);

            emailLogger.info('Vendor payout notification sent', {
                vendorId: vendor._id,
                amount: payoutDetails.amount,
                transferId: payoutDetails.transferId
            });

            return { success: true };
        } catch (error) {
            emailLogger.error('Failed to send vendor payout notification', {
                vendorId: vendor._id,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Send review request to customer
     */
    static async sendReviewRequest(booking, customer, vendor, service) {
        try {
            const reviewUrl = `${process.env.FRONTEND_URL}/reviews/create?booking=${booking._id}`;

            const data = {
                customerName: customer.fullName,
                serviceName: service.title,
                vendorName: vendor.fullName,
                eventDate: formatDate(booking.eventDetails.date, 'MMMM DD, YYYY'),
                reviewUrl,
                incentive: 'Leave a review and get 5% off your next booking!'
            };

            // This would use a review request template
            await sendEmail(customer.email, 'reviewRequest', data);

            emailLogger.info('Review request sent', {
                bookingId: booking._id,
                customerId: customer._id,
                customerEmail: customer.email
            });

            return { success: true };
        } catch (error) {
            emailLogger.error('Failed to send review request', {
                bookingId: booking._id,
                customerId: customer._id,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Send newsletter to subscribers
     */
    static async sendNewsletter(subscribers, content) {
        try {
            const emails = subscribers.map(subscriber => ({
                to: subscriber.email,
                templateName: 'newsletter',
                data: {
                    firstName: subscriber.firstName,
                    content: content.html,
                    subject: content.subject,
                    unsubscribeUrl: `${process.env.FRONTEND_URL}/unsubscribe?email=${encodeURIComponent(subscriber.email)}`
                }
            }));

            const result = await sendBulkEmails(emails);

            emailLogger.info('Newsletter sent', {
                subscriberCount: subscribers.length,
                successful: result.successful,
                failed: result.failed,
                subject: content.subject
            });

            return result;
        } catch (error) {
            emailLogger.error('Failed to send newsletter', {
                error: error.message,
                subscriberCount: subscribers.length
            });
            throw error;
        }
    }

    /**
     * Send administrative notifications
     */
    static async sendAdminNotification(admins, subject, message, data = {}) {
        try {
            const emails = admins.map(admin => ({
                to: admin.email,
                templateName: 'adminNotification',
                data: {
                    adminName: admin.fullName,
                    subject,
                    message,
                    timestamp: formatDateTime(new Date()),
                    dashboardUrl: `${process.env.FRONTEND_URL}/admin`,
                    ...data
                }
            }));

            const result = await sendBulkEmails(emails);

            emailLogger.info('Admin notification sent', {
                adminCount: admins.length,
                successful: result.successful,
                failed: result.failed,
                subject
            });

            return result;
        } catch (error) {
            emailLogger.error('Failed to send admin notification', {
                error: error.message,
                adminCount: admins.length,
                subject
            });
            throw error;
        }
    }

    /**
     * Send custom email with template
     */
    static async sendCustomEmail(to, templateName, data) {
        try {
            await sendEmail(to, templateName, data);

            emailLogger.info('Custom email sent', {
                to,
                templateName,
                hasData: !!data
            });

            return { success: true };
        } catch (error) {
            emailLogger.error('Failed to send custom email', {
                to,
                templateName,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Queue email for later sending
     * (In production, this would integrate with a job queue like Bull)
     */
    static async queueEmail(emailData, sendAt = null) {
        try {
            // For now, just send immediately
            // In production, implement proper email queuing
            if (sendAt && new Date(sendAt) > new Date()) {
                emailLogger.info('Email queued for later sending', {
                    to: emailData.to,
                    sendAt,
                    templateName: emailData.templateName
                });

                // Would add to queue here
                return { success: true, queued: true, sendAt };
            } else {
                return await this.sendCustomEmail(emailData.to, emailData.templateName, emailData.data);
            }
        } catch (error) {
            emailLogger.error('Failed to queue email', {
                error: error.message,
                emailData
            });
            throw error;
        }
    }
}

module.exports = EmailService;
