.verification-banner {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 16px;
  border-radius: 8px;
  border-left: 4px solid;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.verification-banner--info {
  background-color: #e3f2fd;
  border-left-color: #2196f3;
  color: #1565c0;
}

.verification-banner--warning {
  background-color: #fff3e0;
  border-left-color: #ff9800;
  color: #e65100;
}

.verification-banner--error {
  background-color: #ffebee;
  border-left-color: #f44336;
  color: #c62828;
}

.verification-banner--urgent {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
}

.verification-banner__content {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 12px;
}

.verification-banner__icon {
  font-size: 24px;
  flex-shrink: 0;
}

.verification-banner__details {
  flex: 1;
}

.verification-banner__title {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.2;
}

.verification-banner__message {
  margin: 0;
  font-size: 14px;
  line-height: 1.4;
  opacity: 0.9;
}

.verification-banner__due-date {
  margin: 4px 0 0 0;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.verification-banner__actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.verification-banner__action-btn {
  background-color: currentColor;
  color: white;
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.verification-banner__action-btn:hover {
  opacity: 0.8;
}

.verification-banner__dismiss {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: currentColor;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.verification-banner__dismiss:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.1);
}

.verification-banner__retry {
  background-color: currentColor;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  margin-left: 8px;
}

/* Responsive design */
@media (max-width: 768px) {
  .verification-banner {
    padding: 10px 12px;
  }
  
  .verification-banner__content {
    gap: 8px;
  }
  
  .verification-banner__icon {
    font-size: 20px;
  }
  
  .verification-banner__title {
    font-size: 14px;
  }
  
  .verification-banner__message {
    font-size: 13px;
  }
  
  .verification-banner__actions {
    flex-direction: column;
    gap: 4px;
  }
  
  .verification-banner__action-btn {
    padding: 4px 8px;
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .verification-banner__content {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .verification-banner__actions {
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
  }
}
