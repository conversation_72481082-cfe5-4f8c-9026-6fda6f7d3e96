/* =========================
   🔐 LoginPage Component Styles
   ========================= */

/* Main Container */
.login-page {
  display: grid;
  grid-template-columns: 1fr 1fr;
  height: 100vh;
  width: 100%;
  margin: 0;
  background: var(--pp-clr-primary);
}

/* Image Section */
.login-page__image-section {
  background: var(--pp-clr-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  height: var(--pp-auth-image-height-desktop);
}

.login-page__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* Form Section */
.login-page__form-section {
  background: var(--pp-clr-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  min-height: var(--pp-auth-image-height-desktop);
}

.login-page__form-wrapper {
  width: 100%;
  max-width: 500px;
}

.login-page__title {
  font-size: var(--pp-font-heading4);
  font-weight: 600;
  color: var(--pp-clr-text-main);
  margin-bottom: var(--pp-sp-40);
  text-align: left;
}

/* Password Field Container */
.login-page__password-container {
  position: relative;
}

.login-page__password-toggle {
  position: absolute;
  right: var(--pp-sp-16);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--pp-sp-4);
  color: var(--pp-clr-text-gray);
  transition: color 300ms ease;
}

.login-page__password-toggle:hover {
  color: var(--pp-clr-text-main);
}

.login-page__password-toggle:focus {
  outline: none;
  color: var(--pp-clr-secondary);
}

/* Forgot Links */
.login-page__forgot-links {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-8);
  margin: var(--pp-sp-16) 0 var(--pp-sp-32) 0;
}

/* Bottom Link */
.login-page__bottom-link {
  margin-top: var(--pp-sp-32);
}

/* =========================
   📱 Responsive Design
   ========================= */

/* Tablet & Below (≤1024px) */
@media (max-width: 1024px) {
  .login-page {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }

  .login-page__image-section {
    height: var(--pp-auth-image-height-tablet);
    order: 1;
  }

  .login-page__form-section {
    order: 2;
    padding: var(--pp-sp-60);
    align-items: flex-start;
  }
}

/* Mobile (≤768px) */
@media (max-width: 768px) {
  .login-page__image-section {
    height: var(--pp-auth-image-height-mobile);
  }

  .login-page__form-section {
    padding: var(--pp-sp-40);
    align-items: flex-start;
  }

  .login-page__title {
    font-size: var(--pp-font-heading5);
    margin-bottom: var(--pp-sp-32);
  }
}

/* Small Mobile (≤414px) */
@media (max-width: 414px) {
    .login-page {
    grid-template-columns: 1fr;
    grid-template-rows:1fr;
  }
  .login-page__image-section {
 display: none;
  }

  .login-page__form-section {
    padding: var(--pp-sp-24);
    align-items: center;
  }

  .login-page__title {
    font-size: var(--pp-font-heading6);
    margin-bottom: var(--pp-sp-24);
  }
}
