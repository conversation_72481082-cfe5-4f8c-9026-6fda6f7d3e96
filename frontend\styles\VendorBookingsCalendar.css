.bookings-calendar__container {
  background: var(--pp-clr-primary);
  border-radius: var(--pp-bor-rad-12);
  padding: var(--pp-sp-24);
  box-shadow: var(--pp-shdw-sm);
  border: 1px solid var(--pp-clr-border);
}

.bookings-calendar__controls {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: var(--pp-sp-20);
  margin-bottom: var(--pp-sp-24);
  font-size: var(--pp-font-base2-font);
  color: var(--pp-clr-text-main);
}

.bookings-calendar__grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: var(--pp-sp-12);
}

.bookings-calendar__weekday {
  color: var(--pp-clr-text-gray);
  text-align: center;
  font-size: var(--pp-font-extra-small);
  padding-bottom: var(--pp-sp-8);
  font-weight: 500;
}

.bookings-calendar__cell {
  min-height: 80px;
  border-radius: var(--pp-bor-rad-8);
  background: var(--pp-clr-primary);
  color: var(--pp-clr-text-main);
  font-size: var(--pp-font-extra-small);
  padding: var(--pp-sp-8);
  border: 1px solid var(--pp-clr-border-light);
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-4);
}
.bookings-calendar__date {
  font-weight: 600;
}

.bookings-calendar__booking {
  border-radius: var(--pp-bor-rad-4);
  padding: var(--pp-sp-4) var(--pp-sp-8);
  background: var(--pp-clr-guarantee-section-bg);
  color: var(--pp-clr-secondary);
  margin-top: var(--pp-sp-4);
  font-weight: 500;
  /* add color variations based on booking type/status as needed */
}
.bookings-calendar__booking--cancelled {
  background: #fbeaea; /* create tokens for cancel, etc */
  color: #ce2828;
}
