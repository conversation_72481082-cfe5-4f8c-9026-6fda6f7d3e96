import React, { useState } from "react";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "react-google-recaptcha";
import Checkbox from "./Checkbox";
const ContactForm = () => {
  const [form, setForm] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    message: "",
    recaptchaToken: "",
  });

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!form.recaptchaToken) {
      alert("Please complete the captcha");
      return;
    }
    console.log("Form Submitted: ", form);
  };

  return (
    <form className="pp-contact-form" onSubmit={handleSubmit}>
      <h2>Send us a message</h2>
      <div className="pp-form-row">
        <div className="pp-form-field">
          <label htmlFor="firstName" className="pp-form-label">
            Your First Name *
          </label>
          <input
            id="firstName"
            className="pp-form-input"
            placeholder=""
            type="text"
            name="firstName"
            value={form.firstName}
            onChange={handleChange}
            required
          />
        </div>
        <div className="pp-form-field">
          <label htmlFor="lastName" className="pp-form-label">
            Your Last Name *
          </label>
          <input
            id="lastName"
            className="pp-form-input"
            placeholder=""
            type="text"
            name="lastName"
            value={form.lastName}
            onChange={handleChange}
            required
          />
        </div>
      </div>

      <div className="pp-form-row">
        <div className="pp-form-field">
          <label htmlFor="email" className="pp-form-label">
            Email *
          </label>
          <input
            id="email"
            className="pp-form-input"
            placeholder=""
            type="email"
            name="email"
            value={form.email}
            onChange={handleChange}
            required
          />
        </div>
        <div className="pp-form-field">
          <label htmlFor="phone" className="pp-form-label">
            Phone Number *
          </label>
          <input
            id="phone"
            className="pp-form-input"
            placeholder=""
            type="tel"
            name="phone"
            value={form.phone}
            onChange={handleChange}
            required
          />
        </div>
      </div>

      <div className="pp-form-field">
        <label htmlFor="message" className="pp-form-label">
          Message *
        </label>
        <textarea
          id="message"
          className="pp-form-input"
          placeholder=""
          name="message"
          value={form.message}
          onChange={handleChange}
          required
        />
      </div>

      <div className="pp-form-actions">
        <div className="robotcontainer">
          <ReCAPTCHA
            sitekey={import.meta.env.VITE_RECAPTCHA_SITE_KEY}
            onChange={(token) =>
              setForm({ ...form, recaptchaToken: token || "" })
            }
          />
           {/* <Checkbox label={"I am not a robot"} name="robot" value={form.robot} onChange={handleChange} /> */}
        </div>

        <button type="submit" className="pp-btn-primary">
          Send Message
        </button>
      </div>
    </form>
  );
};

export default ContactForm;
