const {
    stripe,
    createConnectAccount,
    updateConnectAccount,
    createAccountLink,
    createPaymentIntent,
    confirmPaymentIntent,
    createRefund,
    getAccountDetails,
    getAccountBalance,
    createTransfer,
    verifyWebhookSignature,
    calculatePlatformFee,
    calculateStripeFee
} = require('../config/stripe');
const { paymentLogger } = require('../utils/logger');

/**
 * Stripe Service
 * Handles all Stripe-related operations including payments, refunds,
 * Connect account management, and marketplace transactions
 */
class StripeService {
    /**
     * Create a Stripe Connect account for vendors with Custom Connect data
     */
    static async createVendorAccount(vendor, stripeConnectData = {}) {
        try {
            const { individual, bankAccount, compliance } = stripeConnectData;

            console.log('🔍 Debug: Individual data:', individual);
            console.log('🔍 Debug: Individual website:', individual?.website);

            const accountData = {
                email: vendor.email,
                business_type: 'individual',
                country: 'US',
                capabilities: {
                    card_payments: { requested: true },
                    transfers: { requested: true }
                },
                // Add required compliance data
                ip_address: compliance?.ipAddress,
                user_agent: compliance?.userAgent,
                mcc: compliance?.mcc || '7299', // Event planning services
                business_url: individual?.website || process.env.FRONTEND_URL,
                product_description: 'Party and event planning services'
            };

            // Add individual data only
            if (individual) {
                accountData.individual = {
                    first_name: individual.firstName,
                    last_name: individual.lastName,
                    dob: individual.dateOfBirth,
                    ssn_last_4: individual.ssn.slice(-4), // Extract last 4 digits for Stripe API
                    id_number: individual.ssn, // Send full SSN for verification
                    phone: individual.phone,
                    email: individual.email,
                    address: {
                        line1: individual.address.line1,
                        city: individual.address.city,
                        state: individual.address.state,
                        postal_code: individual.address.postalCode,
                        country: individual.address.country || 'US'
                    }
                };
            }

            // Add external account (bank account) if provided
            if (bankAccount) {
                accountData.external_account = {
                    object: 'bank_account',
                    country: 'US',
                    currency: 'usd',
                    account_holder_name: bankAccount.accountHolderName,
                    account_holder_type: 'individual',
                    routing_number: bankAccount.routingNumber,
                    account_number: bankAccount.accountNumber
                };
            }

            // Add metadata for compliance tracking
            if (compliance) {
                accountData.metadata = {
                    tos_agreed_at: compliance.tosAgreedAt?.toISOString(),
                    info_accurate_at: compliance.infoAccurateAt?.toISOString(),
                    ip_address: compliance.ipAddress,
                    user_agent: compliance.userAgent,
                    mcc: compliance.mcc || ''
                };
            }

            const account = await createConnectAccount(vendor.email, accountData);

            paymentLogger.info('Stripe Connect account created', {
                vendorId: vendor._id,
                stripeAccountId: account.id,
                email: vendor.email,
                businessType: 'individual'
            });

            return {
                success: true,
                accountId: account.id,
                account
            };
        } catch (error) {
            paymentLogger.error('Failed to create Stripe Connect account', {
                vendorId: vendor._id,
                email: vendor.email,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Create onboarding link for vendor
     */
    static async createOnboardingLink(stripeAccountId, vendorId) {
        try {
            const refreshUrl = `${process.env.FRONTEND_URL}/vendor/onboarding/refresh`;
            const returnUrl = `${process.env.FRONTEND_URL}/vendor/onboarding/complete`;

            const accountLink = await createAccountLink(stripeAccountId, refreshUrl, returnUrl);

            paymentLogger.info('Onboarding link created', {
                vendorId,
                stripeAccountId,
                linkUrl: accountLink.url
            });

            return {
                success: true,
                url: accountLink.url,
                expiresAt: accountLink.expires_at
            };
        } catch (error) {
            paymentLogger.error('Failed to create onboarding link', {
                vendorId,
                stripeAccountId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Get vendor account status and capabilities
     */
    static async getVendorAccountStatus(stripeAccountId) {
        try {
            const account = await getAccountDetails(stripeAccountId);

            const status = {
                onboardingCompleted: account.details_submitted,
                chargesEnabled: account.charges_enabled,
                payoutsEnabled: account.payouts_enabled,
                requiresAction: account.requirements.currently_due.length > 0,
                requirements: account.requirements,
                capabilities: account.capabilities
            };

            paymentLogger.info('Account status retrieved', {
                stripeAccountId,
                onboardingCompleted: status.onboardingCompleted,
                chargesEnabled: status.chargesEnabled,
                payoutsEnabled: status.payoutsEnabled
            });

            return {
                success: true,
                status,
                account
            };
        } catch (error) {
            paymentLogger.error('Failed to get account status', {
                stripeAccountId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Update Stripe account with additional information
     */
    static async updateVendorAccount(stripeAccountId, updateData) {
        try {
            const account = await updateConnectAccount(stripeAccountId, updateData);

            paymentLogger.info('Vendor account updated', {
                stripeAccountId,
                updatedFields: Object.keys(updateData)
            });

            return {
                success: true,
                account
            };
        } catch (error) {
            paymentLogger.error('Failed to update vendor account', {
                stripeAccountId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Fix existing accounts with missing required fields
     */
    static async fixAccountMissingFields(stripeAccountId, vendor, stripeConnectData = {}) {
        try {
            const { individual, compliance } = stripeConnectData;

            const updateData = {};

            // Add Terms of Service acceptance if missing
            if (compliance) {
                updateData.tos_acceptance = {
                    date: Math.floor(Date.now() / 1000),
                    ip: compliance.ipAddress,
                    user_agent: compliance.userAgent
                };
            }

            // Add business profile if missing
            updateData.business_profile = {
                mcc: compliance?.mcc || '7299',
                product_description: 'Party and event planning services',
                url: individual?.website || process.env.FRONTEND_URL || 'https://party-pipeline.com'
            };

            // Update individual data if needed (only individual supported)
            if (individual) {
                updateData.individual = {
                    first_name: individual.firstName,
                    last_name: individual.lastName,
                    dob: individual.dateOfBirth,
                    ssn_last_4: individual.ssn.slice(-4), // Extract last 4 digits for Stripe API
                    id_number: individual.ssn, // Send full SSN for verification
                    phone: individual.phone,
                    email: individual.email,
                    address: {
                        line1: individual.address.line1,
                        city: individual.address.city,
                        state: individual.address.state,
                        postal_code: individual.address.postalCode,
                        country: individual.address.country || 'US'
                    }
                };
            }

            const account = await updateConnectAccount(stripeAccountId, updateData);

            paymentLogger.info('Stripe account fixed with missing fields', {
                stripeAccountId,
                updatedFields: Object.keys(updateData)
            });

            return {
                success: true,
                account
            };
        } catch (error) {
            paymentLogger.error('Failed to fix account missing fields', {
                stripeAccountId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Create payment intent for booking
     */
    static async createBookingPaymentIntent(booking, vendor) {
        try {
            const amount = booking.serviceDetails.totalCost;
            const platformFee = calculatePlatformFee(amount);

            const paymentIntent = await createPaymentIntent({
                amount,
                platformFeeAmount: platformFee,
                stripeAccountId: vendor.stripeAccountId,
                metadata: {
                    bookingId: booking._id.toString(),
                    customerId: booking.customerId.toString(),
                    vendorId: booking.vendorId.toString(),
                    serviceId: booking.serviceId.toString(),
                    type: 'booking_payment'
                }
            });

            paymentLogger.info('Payment intent created for booking', {
                bookingId: booking._id,
                paymentIntentId: paymentIntent.id,
                amount,
                platformFee,
                vendorStripeId: vendor.stripeAccountId
            });

            return {
                success: true,
                paymentIntent,
                clientSecret: paymentIntent.client_secret,
                amount,
                platformFee
            };
        } catch (error) {
            paymentLogger.error('Failed to create payment intent', {
                bookingId: booking._id,
                vendorStripeId: vendor.stripeAccountId,
                amount: booking.serviceDetails.totalCost,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Wrapper to create a payment intent using raw parameters
     */
    static async createPaymentIntent(params) {
        try {
            // Accept both controller-style params and low-level params
            const {
                amount,
                vendorStripeAccountId,
                stripeAccountId,
                platformFeeAmount,
                metadata = {},
                description
            } = params;

            const accountId = stripeAccountId || vendorStripeAccountId;
            const fee = typeof platformFeeAmount === 'number' ? platformFeeAmount : calculatePlatformFee(amount);

            const paymentIntent = await createPaymentIntent({
                amount,
                platformFeeAmount: fee,
                stripeAccountId: accountId,
                metadata: {
                    description,
                    ...metadata
                }
            });
            return paymentIntent;
        } catch (error) {
            paymentLogger.error('Failed to create payment intent (wrapper)', {
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Confirm payment for booking
     */
    static async confirmBookingPayment(paymentIntentId, paymentMethodId) {
        try {
            const paymentIntent = await confirmPaymentIntent(paymentIntentId, paymentMethodId);

            paymentLogger.info('Payment confirmed', {
                paymentIntentId,
                status: paymentIntent.status,
                amount: paymentIntent.amount,
                bookingId: paymentIntent.metadata.bookingId
            });

            return {
                success: true,
                paymentIntent,
                status: paymentIntent.status
            };
        } catch (error) {
            paymentLogger.error('Failed to confirm payment', {
                paymentIntentId,
                paymentMethodId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Retrieve a payment intent by ID
     */
    static async getPaymentIntent(paymentIntentId) {
        try {
            return await stripe.paymentIntents.retrieve(paymentIntentId);
        } catch (error) {
            paymentLogger.error('Failed to retrieve payment intent', {
                paymentIntentId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * High-level booking payment: create and confirm a destination charge
     */
    static async processBookingPayment({ booking, paymentMethodId, customerId, vendorStripeAccountId }) {
        try {
            const amount = booking.serviceDetails.totalCost;
            const platformFee = calculatePlatformFee(amount);

            const paymentIntent = await createPaymentIntent({
                amount,
                platformFeeAmount: platformFee,
                stripeAccountId: vendorStripeAccountId,
                metadata: {
                    bookingId: booking._id.toString(),
                    customerId: customerId,
                    vendorId: booking.vendorId._id.toString(),
                    type: 'booking_payment'
                }
            });

            const confirmed = await confirmPaymentIntent(paymentIntent.id, paymentMethodId);
            if (confirmed.status !== 'succeeded') {
                throw new Error(`Payment ${confirmed.status}`);
            }

            const vendorAmount = (confirmed.amount - (confirmed.application_fee_amount || 0)) / 100;
            const chargeId = confirmed.latest_charge;
            const receiptUrl = confirmed.charges?.data?.[0]?.receipt_url;

            return {
                paymentIntentId: confirmed.id,
                amount: confirmed.amount / 100,
                platformFee: (confirmed.application_fee_amount || 0) / 100,
                vendorAmount,
                chargeId,
                receiptUrl
            };
        } catch (error) {
            paymentLogger.error('Failed to process booking payment', {
                bookingId: booking._id,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Process refund for cancelled booking
     */
    static async processBookingRefund(paymentIntentId, refundAmount, reason, bookingId) {
        try {
            const refund = await createRefund(paymentIntentId, refundAmount, reason);

            paymentLogger.info('Refund processed', {
                refundId: refund.id,
                paymentIntentId,
                refundAmount,
                reason,
                bookingId,
                status: refund.status
            });

            return {
                success: true,
                refund,
                refundId: refund.id,
                amount: refund.amount / 100,
                status: refund.status
            };
        } catch (error) {
            paymentLogger.error('Failed to process refund', {
                paymentIntentId,
                refundAmount,
                bookingId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Simple refund helper used by controllers
     * Expects amount in cents (to align with most callers);
     * converts to dollars for the lower-level helper
     */
    static async processRefund(paymentIntentId, amountInCents, reason) {
        const amountInDollars = typeof amountInCents === 'number' ? amountInCents / 100 : undefined;
        try {
            const refund = await createRefund(paymentIntentId, amountInDollars, reason);
            paymentLogger.info('Refund processed (simple)', {
                refundId: refund.id,
                paymentIntentId,
                amount: refund.amount / 100,
                reason
            });
            return refund;
        } catch (error) {
            paymentLogger.error('Failed to process refund (simple)', {
                paymentIntentId,
                amountInCents,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Get vendor balance
     */
    static async getVendorBalance(stripeAccountId) {
        try {
            const balance = await getAccountBalance(stripeAccountId);

            const formattedBalance = {
                available: balance.available.map(b => ({
                    amount: b.amount / 100,
                    currency: b.currency
                })),
                pending: balance.pending.map(b => ({
                    amount: b.amount / 100,
                    currency: b.currency
                }))
            };

            paymentLogger.info('Vendor balance retrieved', {
                stripeAccountId,
                availableAmount: formattedBalance.available[0]?.amount || 0
            });

            return {
                success: true,
                balance: formattedBalance
            };
        } catch (error) {
            paymentLogger.error('Failed to get vendor balance', {
                stripeAccountId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Create payout for vendor
     */
    static async createVendorPayout(stripeAccountId, amount, vendorId) {
        try {
            const transfer = await createTransfer(amount, stripeAccountId);

            paymentLogger.info('Vendor payout created', {
                transferId: transfer.id,
                stripeAccountId,
                vendorId,
                amount,
                status: transfer.status
            });

            return {
                success: true,
                transfer,
                transferId: transfer.id,
                amount: transfer.amount / 100,
                status: transfer.status
            };
        } catch (error) {
            paymentLogger.error('Failed to create vendor payout', {
                stripeAccountId,
                vendorId,
                amount,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Release funds to vendor (transfer from platform to vendor account)
     */
    static async releaseFundsToVendor(chargeId, stripeAccountId, amount) {
        try {
            // Note: In destination charge flow, funds are already routed.
            // This transfer acts as a fallback/manual payout.
            const transfer = await createTransfer(amount, stripeAccountId);
            paymentLogger.info('Funds released to vendor', {
                chargeId,
                stripeAccountId,
                amount,
                transferId: transfer.id
            });
            return transfer;
        } catch (error) {
            paymentLogger.error('Failed to release funds to vendor', {
                chargeId,
                stripeAccountId,
                amount,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Handle Stripe webhooks
     */
    static async handleWebhook(payload, signature) {
        try {
            const event = verifyWebhookSignature(payload, signature, process.env.STRIPE_WEBHOOK_SECRET);

            paymentLogger.info('Webhook received', {
                eventType: event.type,
                eventId: event.id
            });

            // Handle different event types
            switch (event.type) {
                case 'payment_intent.succeeded':
                    return await this.handlePaymentSucceeded(event.data.object);

                case 'payment_intent.payment_failed':
                    return await this.handlePaymentFailed(event.data.object);

                case 'account.updated':
                    return await this.handleAccountUpdated(event.data.object);

                case 'transfer.created':
                    return await this.handleTransferCreated(event.data.object);

                case 'charge.dispute.created':
                    return await this.handleDisputeCreated(event.data.object);

                default:
                    paymentLogger.info('Unhandled webhook event type', {
                        eventType: event.type,
                        eventId: event.id
                    });
                    return { handled: false, eventType: event.type };
            }
        } catch (error) {
            paymentLogger.error('Webhook handling failed', {
                error: error.message,
                signature
            });
            throw error;
        }
    }

    /**
     * Handle successful payment webhook
     */
    static async handlePaymentSucceeded(paymentIntent) {
        try {
            const bookingId = paymentIntent.metadata.bookingId;

            paymentLogger.info('Payment succeeded webhook', {
                paymentIntentId: paymentIntent.id,
                bookingId,
                amount: paymentIntent.amount / 100
            });

            // Update booking status in database
            // This would typically trigger other services
            return {
                handled: true,
                action: 'payment_succeeded',
                bookingId,
                paymentIntentId: paymentIntent.id
            };
        } catch (error) {
            paymentLogger.error('Failed to handle payment succeeded webhook', {
                paymentIntentId: paymentIntent.id,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Handle failed payment webhook
     */
    static async handlePaymentFailed(paymentIntent) {
        try {
            const bookingId = paymentIntent.metadata.bookingId;

            paymentLogger.warn('Payment failed webhook', {
                paymentIntentId: paymentIntent.id,
                bookingId,
                lastPaymentError: paymentIntent.last_payment_error
            });

            return {
                handled: true,
                action: 'payment_failed',
                bookingId,
                paymentIntentId: paymentIntent.id,
                error: paymentIntent.last_payment_error
            };
        } catch (error) {
            paymentLogger.error('Failed to handle payment failed webhook', {
                paymentIntentId: paymentIntent.id,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Handle account updated webhook
     */
    static async handleAccountUpdated(account) {
        try {
            paymentLogger.info('Account updated webhook', {
                accountId: account.id,
                chargesEnabled: account.charges_enabled,
                payoutsEnabled: account.payouts_enabled,
                detailsSubmitted: account.details_submitted,
                requirementsCurrently_due: account.requirements?.currently_due,
                requirementsPastDue: account.requirements?.past_due,
                requirementsEventuallyDue: account.requirements?.eventually_due
            });

            // Find and update the vendor profile in database
            const VendorProfile = require('../models/VendorProfile');
            const vendorProfile = await VendorProfile.findOne({ stripeAccountId: account.id });

            if (vendorProfile) {
                // Store previous status for comparison
                const previousStatus = {
                    stripeOnboardingCompleted: vendorProfile.stripeOnboardingCompleted,
                    stripePayoutsEnabled: vendorProfile.stripePayoutsEnabled,
                    stripeChargesEnabled: vendorProfile.stripeChargesEnabled,
                    verificationStatus: vendorProfile.verification.status
                };

                // Update the database fields based on Stripe account status
                vendorProfile.stripeOnboardingCompleted = account.details_submitted;
                vendorProfile.stripePayoutsEnabled = account.payouts_enabled;
                vendorProfile.stripeChargesEnabled = account.charges_enabled;

                // Update verification status based on new capabilities
                if (account.charges_enabled && account.payouts_enabled) {
                    vendorProfile.verification.status = 'approved';
                    vendorProfile.verification.completedAt = new Date();
                    vendorProfile.verification.notes = 'Account fully verified and operational';
                } else if (account.requirements?.past_due?.length > 0) {
                    vendorProfile.verification.status = 'suspended';
                    vendorProfile.verification.notes = `Account suspended: ${account.requirements?.disabled_reason || 'Past due requirements'}`;
                } else if (account.requirements?.currently_due?.length > 0) {
                    vendorProfile.verification.status = 'pending';
                    vendorProfile.verification.notes = `Additional verification required: ${account.requirements?.currently_due?.join(', ')}`;
                }

                // Handle verification requirements
                await this.processVerificationRequirements(vendorProfile, account);

                await vendorProfile.save();

                // Check if status changed significantly
                const statusChanged = (
                    previousStatus.stripePayoutsEnabled !== account.payouts_enabled ||
                    previousStatus.stripeChargesEnabled !== account.charges_enabled ||
                    previousStatus.verificationStatus !== vendorProfile.verification.status
                );

                paymentLogger.info('Vendor profile updated from webhook', {
                    vendorId: vendorProfile.userId,
                    accountId: account.id,
                    previousStatus,
                    newStatus: {
                        stripeOnboardingCompleted: account.details_submitted,
                        stripePayoutsEnabled: account.payouts_enabled,
                        stripeChargesEnabled: account.charges_enabled,
                        verificationStatus: vendorProfile.verification.status
                    },
                    statusChanged,
                    wasApproved: account.charges_enabled && account.payouts_enabled
                });

                // If account was just approved, log this as a significant event
                if (statusChanged && account.charges_enabled && account.payouts_enabled) {
                    paymentLogger.info('🎉 Stripe account fully approved!', {
                        vendorId: vendorProfile.userId,
                        accountId: account.id,
                        businessName: vendorProfile.businessName
                    });
                }
            } else {
                paymentLogger.warn('Vendor profile not found for Stripe account', {
                    accountId: account.id
                });
            }

            return {
                handled: true,
                action: 'account_updated',
                accountId: account.id,
                capabilities: {
                    chargesEnabled: account.charges_enabled,
                    payoutsEnabled: account.payouts_enabled,
                    onboardingCompleted: account.details_submitted
                },
                requirements: account.requirements
            };
        } catch (error) {
            paymentLogger.error('Failed to handle account updated webhook', {
                accountId: account.id,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Handle transfer created webhook
     */
    static async handleTransferCreated(transfer) {
        try {
            paymentLogger.info('Transfer created webhook', {
                transferId: transfer.id,
                destination: transfer.destination,
                amount: transfer.amount / 100
            });

            return {
                handled: true,
                action: 'transfer_created',
                transferId: transfer.id,
                destination: transfer.destination,
                amount: transfer.amount / 100
            };
        } catch (error) {
            paymentLogger.error('Failed to handle transfer created webhook', {
                transferId: transfer.id,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Handle dispute created webhook
     */
    static async handleDisputeCreated(dispute) {
        try {
            paymentLogger.warn('Dispute created webhook', {
                disputeId: dispute.id,
                chargeId: dispute.charge,
                amount: dispute.amount / 100,
                reason: dispute.reason
            });

            return {
                handled: true,
                action: 'dispute_created',
                disputeId: dispute.id,
                chargeId: dispute.charge,
                amount: dispute.amount / 100,
                reason: dispute.reason
            };
        } catch (error) {
            paymentLogger.error('Failed to handle dispute created webhook', {
                disputeId: dispute.id,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Calculate booking fees
     */
    static calculateBookingFees(amount) {
        const platformFee = calculatePlatformFee(amount);
        const stripeFee = calculateStripeFee(amount);
        const vendorPayout = amount - platformFee - stripeFee;

        return {
            subtotal: amount,
            platformFee,
            stripeFee,
            vendorPayout,
            total: amount
        };
    }

    /**
     * Create setup intent for saving payment methods
     */
    static async createSetupIntent(customerId) {
        try {
            const setupIntent = await stripe.setupIntents.create({
                customer: customerId,
                usage: 'off_session',
                payment_method_types: ['card']
            });

            paymentLogger.info('Setup intent created', {
                setupIntentId: setupIntent.id,
                customerId
            });

            return setupIntent;
        } catch (error) {
            paymentLogger.error('Failed to create setup intent', {
                customerId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Get customer payment methods
     */
    static async getCustomerPaymentMethods(customerId) {
        try {
            const paymentMethods = await stripe.paymentMethods.list({
                customer: customerId,
                type: 'card'
            });
            return paymentMethods;
        } catch (error) {
            paymentLogger.error('Failed to get customer payment methods', {
                customerId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Delete payment method
     */
    static async deletePaymentMethod(paymentMethodId) {
        try {
            await stripe.paymentMethods.detach(paymentMethodId);

            paymentLogger.info('Payment method deleted', {
                paymentMethodId
            });

            return { success: true };
        } catch (error) {
            paymentLogger.error('Failed to delete payment method', {
                paymentMethodId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Create Stripe customer
     */
    static async createCustomer(user) {
        try {
            const customer = await stripe.customers.create({
                email: user.email,
                name: `${user.firstName} ${user.lastName}`,
                metadata: {
                    userId: user._id.toString()
                }
            });

            paymentLogger.info('Stripe customer created', {
                customerId: customer.id,
                userId: user._id,
                email: user.email
            });

            return {
                success: true,
                customer,
                customerId: customer.id
            };
        } catch (error) {
            paymentLogger.error('Failed to create Stripe customer', {
                userId: user._id,
                email: user.email,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Attach a payment method to a customer
     */
    static async attachPaymentMethod(paymentMethodId, customerId) {
        try {
            const pm = await stripe.paymentMethods.attach(paymentMethodId, { customer: customerId });
            paymentLogger.info('Payment method attached', { paymentMethodId, customerId });
            return pm;
        } catch (error) {
            paymentLogger.error('Failed to attach payment method', { paymentMethodId, customerId, error: error.message });
            throw error;
        }
    }

    /**
     * Detach a payment method from a customer
     */
    static async detachPaymentMethod(paymentMethodId) {
        try {
            const pm = await stripe.paymentMethods.detach(paymentMethodId);
            paymentLogger.info('Payment method detached', { paymentMethodId });
            return pm;
        } catch (error) {
            paymentLogger.error('Failed to detach payment method', { paymentMethodId, error: error.message });
            throw error;
        }
    }

    /**
     * Construct webhook event (wrapper)
     */
    static constructWebhookEvent(payload, signature, secret) {
        return verifyWebhookSignature(payload, signature, secret);
    }

    /**
     * Get transfer details from Stripe
     */
    static async getTransferDetails(transferId) {
        try {
            const transfer = await stripe.transfers.retrieve(transferId);

            paymentLogger.info('Transfer details retrieved', {
                transferId,
                amount: transfer.amount / 100,
                destination: transfer.destination
            });

            return {
                success: true,
                transfer: {
                    id: transfer.id,
                    amount: transfer.amount / 100,
                    currency: transfer.currency,
                    destination: transfer.destination,
                    created: transfer.created,
                    description: transfer.description,
                    metadata: transfer.metadata,
                    status: transfer.status || 'completed'
                }
            };
        } catch (error) {
            paymentLogger.error('Failed to get transfer details', {
                transferId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Create transfer to vendor account
     */
    static async createTransfer({ amount, destination, metadata = {} }) {
        try {
            const transfer = await createTransfer({
                amount: Math.round(amount * 100), // Convert to cents
                destination,
                metadata
            });

            paymentLogger.info('Transfer created successfully', {
                transferId: transfer.id,
                amount,
                destination,
                metadata
            });

            return {
                success: true,
                transferId: transfer.id,
                transfer
            };
        } catch (error) {
            paymentLogger.error('Failed to create transfer', {
                amount,
                destination,
                metadata,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Process verification requirements from Stripe account
     */
    static async processVerificationRequirements(vendorProfile, account) {
        try {
            const requirements = account.requirements || {};
            const currentlyDue = requirements.currently_due || [];
            const pastDue = requirements.past_due || [];
            const eventuallyDue = requirements.eventually_due || [];

            // Initialize verification requirements if not exists
            if (!vendorProfile.verificationRequirements) {
                vendorProfile.verificationRequirements = {
                    currentlyDue: [],
                    pastDue: [],
                    eventuallyDue: [],
                    disabled: account.requirements?.disabled_reason || null,
                    lastUpdated: new Date()
                };
            }

            // Update requirements
            vendorProfile.verificationRequirements.currentlyDue = currentlyDue;
            vendorProfile.verificationRequirements.pastDue = pastDue;
            vendorProfile.verificationRequirements.eventuallyDue = eventuallyDue;
            vendorProfile.verificationRequirements.disabled = account.requirements?.disabled_reason || null;
            vendorProfile.verificationRequirements.lastUpdated = new Date();

            // Update verification status based on requirements
            if (pastDue.length > 0 || account.requirements?.disabled_reason) {
                vendorProfile.verification.status = 'suspended';
                vendorProfile.verification.notes = `Account suspended: ${account.requirements?.disabled_reason || 'Past due requirements'}`;
            } else if (currentlyDue.length > 0) {
                vendorProfile.verification.status = 'pending';
                vendorProfile.verification.notes = `Additional verification required: ${currentlyDue.join(', ')}`;
            } else if (account.charges_enabled && account.payouts_enabled) {
                vendorProfile.verification.status = 'approved';
                vendorProfile.verification.completedAt = new Date();
                vendorProfile.verification.notes = 'Account fully verified and operational';
            }

            // Check for document requirements and flag for conditional verification
            const documentRequirements = currentlyDue.filter(req =>
                req.includes('document') ||
                req.includes('verification') ||
                req.includes('identity')
            );

            if (documentRequirements.length > 0) {
                vendorProfile.requiresAdditionalVerification = true;
                vendorProfile.additionalVerificationDetails = {
                    requiredDocuments: documentRequirements,
                    reason: 'Stripe requires additional identity verification',
                    dueDate: account.requirements?.current_deadline ?
                        new Date(account.requirements.current_deadline * 1000) : null
                };
            } else {
                vendorProfile.requiresAdditionalVerification = false;
                vendorProfile.additionalVerificationDetails = null;
            }

            paymentLogger.info('Verification requirements processed', {
                vendorId: vendorProfile.userId,
                accountId: account.id,
                currentlyDue: currentlyDue.length,
                pastDue: pastDue.length,
                eventuallyDue: eventuallyDue.length,
                verificationStatus: vendorProfile.verification.status,
                requiresAdditionalVerification: vendorProfile.requiresAdditionalVerification
            });

        } catch (error) {
            paymentLogger.error('Failed to process verification requirements', {
                vendorId: vendorProfile.userId,
                accountId: account.id,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Update Stripe account with additional information
     */
    static async updateAccountWithAdditionalInfo(stripeAccountId, updateData) {
        try {
            const account = await updateConnectAccount(stripeAccountId, updateData);

            paymentLogger.info('Stripe account updated with additional info', {
                accountId: stripeAccountId,
                updatedFields: Object.keys(updateData)
            });

            return {
                success: true,
                account,
                updatedAt: new Date()
            };
        } catch (error) {
            paymentLogger.error('Failed to update Stripe account', {
                accountId: stripeAccountId,
                error: error.message,
                updateData
            });
            throw error;
        }
    }

    /**
     * Submit document to Stripe for verification
     */
    static async submitVerificationDocument(stripeAccountId, documentType, fileData) {
        try {
            // Map document types to Stripe purposes
            const purposeMap = {
                'government_id': 'identity_document',
                'selfie_verification': 'identity_document',
                'bank_statement': 'account_requirement',
                'id': 'identity_document'
            };

            const stripePurpose = purposeMap[documentType] || 'identity_document';

            // Prepare file data for Stripe
            const fileBuffer = fileData.buffer || fileData;
            const fileName = fileData.originalname || fileData.name || 'document';
            const fileType = fileData.mimetype || fileData.type || 'application/octet-stream';

            // Upload file to Stripe
            const file = await stripe.files.create({
                purpose: stripePurpose,
                file: {
                    data: fileBuffer,
                    name: fileName,
                    type: fileType
                }
            });

            // Update account with document based on type
            const updateData = {};

            if (documentType === 'government_id' || documentType === 'id') {
                updateData.individual = {
                    verification: {
                        document: {
                            front: file.id
                        }
                    }
                };
            } else if (documentType === 'selfie_verification') {
                updateData.individual = {
                    verification: {
                        additional_document: {
                            front: file.id
                        }
                    }
                };
            } else if (documentType === 'bank_statement') {
                updateData.individual = {
                    verification: {
                        additional_document: {
                            front: file.id
                        }
                    }
                };
            }

            if (Object.keys(updateData).length > 0) {
                await this.updateAccountWithAdditionalInfo(stripeAccountId, updateData);
            }

            paymentLogger.info('Document submitted to Stripe', {
                accountId: stripeAccountId,
                documentType,
                stripePurpose,
                fileId: file.id,
                fileName,
                fileSize: fileBuffer.length
            });

            return {
                success: true,
                fileId: file.id,
                documentType,
                stripePurpose
            };
        } catch (error) {
            paymentLogger.error('Failed to submit document to Stripe', {
                accountId: stripeAccountId,
                documentType,
                error: error.message,
                errorCode: error.code,
                errorType: error.type
            });
            throw error;
        }
    }

    /**
     * Get account verification status
     */
    static async getAccountVerificationStatus(stripeAccountId) {
        try {
            const account = await getAccountDetails(stripeAccountId);

            return {
                success: true,
                account: {
                    id: account.id,
                    chargesEnabled: account.charges_enabled,
                    payoutsEnabled: account.payouts_enabled,
                    detailsSubmitted: account.details_submitted,
                    requirements: account.requirements,
                    capabilities: account.capabilities
                }
            };
        } catch (error) {
            paymentLogger.error('Failed to get account verification status', {
                accountId: stripeAccountId,
                error: error.message
            });
            throw error;
        }
    }
}

module.exports = StripeService;
