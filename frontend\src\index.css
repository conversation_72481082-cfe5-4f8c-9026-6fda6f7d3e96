@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");
@import "tailwindcss";
@import "lenis/dist/lenis.css";
/* @import url("https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400..900;1,400..900&display=swap"); */

/* Metro Sans local font family (woff2 + woff only) */
@font-face {
  font-family: "Metro Sans";
  src: url("./assets/font/MetroSans-Light.woff2") format("woff2"),
    url("./assets/font/MetroSans-Light.woff") format("woff");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Metro Sans";
  src: url("./assets/font/MetroSans-LightItalic.woff2") format("woff2"),
    url("./assets/font/MetroSans-LightItalic.woff") format("woff");
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Metro Sans";
  src: url("./assets/font/MetroSans-Regular.woff2") format("woff2"),
    url("./assets/font/MetroSans-Regular.woff") format("woff");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Metro Sans";
  src: url("./assets/font/MetroSans-RegularItalic.woff2") format("woff2"),
    url("./assets/font/MetroSans-RegularItalic.woff") format("woff");
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Metro Sans";
  src: url("./assets/font/MetroSans-Medium.woff2") format("woff2"),
    url("./assets/font/MetroSans-Medium.woff") format("woff");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Metro Sans";
  src: url("./assets/font/MetroSans-MediumItalic.woff2") format("woff2"),
    url("./assets/font/MetroSans-MediumItalic.woff") format("woff");
  font-weight: 500;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Metro Sans";
  src: url("./assets/font/MetroSans-SemiBold.woff2") format("woff2"),
    url("./assets/font/MetroSans-SemiBold.woff") format("woff");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Metro Sans";
  src: url("./assets/font/MetroSans-SemiBoldItalic.woff2") format("woff2"),
    url("./assets/font/MetroSans-SemiBoldItalic.woff") format("woff");
  font-weight: 600;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Metro Sans";
  src: url("./assets/font/MetroSans-Bold.woff2") format("woff2"),
    url("./assets/font/MetroSans-Bold.woff") format("woff");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Metro Sans";
  src: url("./assets/font/MetroSans-BoldItalic.woff2") format("woff2"),
    url("./assets/font/MetroSans-BoldItalic.woff") format("woff");
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}

/* =========================
   🌐 Party Pipeline Variables
   ========================= */
:root {
  /* 🎨 Colors */
  --pp-clr-primary: #ffffff;
  --pp-clr-secondary: #000000;
  --pp-clr-text-main: #000000;
  --pp-clr-text-gray: #656565;
  --pp-clr-border: #d2d2d2;
  --pp-clr-border-light: #e5e5e5;

  /* Guarantee Section Colors */
  --pp-clr-guarantee-section-bg: #d4edda;
  --pp-clr-guarantee-badge-bg: #e0f2f7;

  /* Feature Icon Colors */
  --pp-clr-feature-icon-green: #76e1a7;

  /* Background Colors */
  --pp-clr-bg: #ffffff;
  --pp-clr-blue-bg: #cbe5ff;
  --pp-clr-blue-txt: #11538f;

  /* 🖋 Fonts */
  --pp-font-Playfair-Display: "Playfair Display", serif;
  --pp-font-Metro-Sans: "Metro Sans";

  /* 🔠 Font Sizes (px) */
  --pp-font-heading1: 96px; /* Hero title */
  --pp-font-heading2: 48px;
  --pp-font-heading3: 40px;
  --pp-font-heading4: 32px;
  --pp-font-heading5: 22px;
  --pp-font-heading6: 20px;
  --pp-font-base-font: 19px; /* navbar */
  --pp-font-base2-font: 18px;
  --pp-font-small-font: 17px;
  --pp-font-extra-small: 16px;

  /* 📏 Spacing (px) */
  --pp-sp-4: 4px;
  --pp-sp-8: 8px;
  --pp-sp-10: 10px;
  --pp-sp-12: 12px;
  --pp-sp-16: 16px;
  --pp-sp-20: 20px;
  --pp-sp-24: 24px;
  --pp-sp-32: 32px;
  --pp-sp-40: 40px;
  --pp-sp-48: 48px;
  --pp-sp-60: 60px;
  --pp-section-padding: 120px;

  /* 🔲 Border Radius (px) */
  --pp-bor-rad-8: 8px;
  --pp-bor-rad-12: 12px;
  --pp-bor-rad-16: 16px;
  --pp-bor-rad-24: 24px;
  --pp-bor-rad-round: 9999px;

  /* 🌫 Shadows */
  --pp-shdw-sm: 0px 1px 2px rgba(0, 0, 0, 0.05);
  --pp-shdw-md: 0px 4px 6px rgba(0, 0, 0, 0.1);
  --pp-shdw-lg: 0px 10px 15px rgba(0, 0, 0, 0.15);
  --pp-shdw-xl: 0px 20px 25px rgba(0, 0, 0, 0.2);

  /* 🎛 Transitions, Overlays, Layout */
  --pp-overlay-bg: rgba(0, 0, 0, 0.32);
  --pp-modal-max-width: 951px;
  --pp-dropzone-min-height: 160px;
  --pp-textarea-min-height: 120px;
  --pp-bor-w-1: 1px;

  /* Z-Index Values */
  --pp-z-index-base: 1;
  --pp-z-index-dropdown: 100;
  --pp-z-index-sticky: 200;
  --pp-z-index-modal: 300;
  --pp-z-index-tooltip: 400;

  /* Status Colors */
  --pp-clr-success: #28a745;
  --pp-clr-warning: #ffc107;
  --pp-clr-error: #dc3545;
  --pp-clr-info: #17a2b8;

  /* Additional Colors */
  --pp-clr-border-dark: #b0b0b0;
  --pp-clr-overlay: rgba(0, 0, 0, 0.32);
  --pp-clr-text-gray-light: #9ca3af;
  --pp-clr-card-rentals-bg: #f8f9fa;
}

/* =========================
   🌐 Global Styles
   ========================= */
*:focus {
  outline: none;
}
::selection {
  color: var(--pp-clr-primary);
  background-color: var(--pp-clr-secondary);
}
select {
  cursor: pointer;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
body {
  color: var(--pp-clr-text-main);
  background-color: var(--pp-clr-bg);
  font-family: var(--pp-font-Metro-Sans);
  font-weight: 400;
  margin: 0;
  outline: none;
  padding: 0;
  text-underline-offset: 4px;
}
input[type="checkbox"],
input[type="radio"] {
  accent-color: var(--pp-clr-secondary);
  width: 20px !important;
  height: 20px !important;
}

/* Auth pages specific body styling */
body.auth-page {
  background-color: #ffffff;
}

.pp-max-container {
  max-width: 1400px;
  margin: auto;
  padding: 0 var(--pp-sp-24);
}

/* =========================
   🌐 Reusable Utilities
   ========================= */
.pp-text-gradient {
  background: linear-gradient(90deg, #ffffff, #000000);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* ======================
   🔲 Custom Checkbox Component
   ========================= */
.pp-checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-12);
  cursor: pointer;
  width: fit-content;
  user-select: none;
}

.pp-checkbox {
  position: relative;
  width: var(--pp-sp-20);
  height: var(--pp-sp-20);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-4);
  background: var(--pp-clr-primary);
  cursor: pointer;
  transition: all 300ms ease;
  flex-shrink: 0;
}

.pp-checkbox:hover {
  border-color: var(--pp-clr-text-gray);
  transform: scale(1.05);
}

.pp-checkbox:focus {
  outline: none;
  border-color: var(--pp-clr-secondary);
}

.pp-checkbox:checked {
  background-color: var(--pp-clr-secondary) !important;
  border-color: var(--pp-clr-secondary) !important;
}

.pp-checkbox:checked::after {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: var(--pp-sp-12);
  height: var(--pp-sp-12);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.pp-checkbox-label {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-main);
  font-weight: 400;
  cursor: pointer;
}

/* =========================
   🔐 Reusable Form Components
   ========================= */
.pp-form-group {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-4);
  margin-bottom: var(--pp-sp-10);
}

.pp-form-label {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-main);
  font-weight: 600;
  margin-bottom: var(--pp-sp-4);
}

.pp-form-input {
  width: 100%;
  padding: var(--pp-sp-10) var(--pp-sp-16);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-8);
  font-size: var(--pp-font-extra-small);
  font-family: var(--pp-font-Metro-Sans);
  background: var(--pp-clr-primary);
  transition: border-color 300ms ease;
  box-sizing: border-box;
}

.pp-form-input:focus {
  outline: none;
  border-color: var(--pp-clr-secondary);
}

.pp-form-input:invalid:not(:placeholder-shown) {
  border-color: #dc3545;
}

.pp-form-input::placeholder {
  color: var(--pp-clr-text-gray);
}

.pp-form-button {
  width: 100%;
  padding: var(--pp-sp-16);
  background: var(--pp-clr-secondary);
  color: var(--pp-clr-primary);
  border: none;
  border-radius: var(--pp-bor-rad-8);
  font-size: var(--pp-font-extra-small);
  font-weight: 600;
  font-family: var(--pp-font-Metro-Sans);
  cursor: pointer;
  transition: all 300ms ease;
}

.pp-form-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--pp-shdw-md);
}

.pp-form-button:active {
  transform: translateY(0);
}

.pp-form-link {
  color: var(--pp-clr-blue-txt);
  text-decoration: none;
  font-size: var(--pp-font-extra-small);
  transition: color 300ms ease;
}

.pp-form-link:hover {
  color: var(--pp-clr-secondary);
  text-decoration: underline;
}

.pp-form-text {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  text-align: center;
}

.pp-form-text a {
  color: var(--pp-clr-blue-txt);
  text-decoration: none;
  font-weight: 500;
}

.pp-form-text a:hover {
  text-decoration: underline;
}

.pp-form-text-bottom {
  margin-top: var(--pp-sp-32);
}

/* =========================
   🎨 Reusable Layout Utilities
   ========================= */
.pp-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--pp-sp-16);
  margin-bottom: var(--pp-sp-10);
}

.pp-form-row .pp-form-group {
  margin-bottom: 0;
}

.pp-form-divider {
  height: 1px;
  background: var(--pp-clr-border);
  margin: var(--pp-sp-24) 0;
}

.pp-form-help-text {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  margin-top: var(--pp-sp-4);
}

.pp-form-error {
  color: #dc3545;
  font-size: var(--pp-font-extra-small);
  margin-top: var(--pp-sp-4);
}

.pp-form-success {
  color: #28a745;
  font-size: var(--pp-font-extra-small);
  margin-top: var(--pp-sp-4);
}

/* Authentication loading state */
.auth-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: var(--pp-font-small);
  color: var(--pp-clr-text-gray);
}

/* Navbar user menu styles */
.c-navbar__user-menu {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-12);
}

.c-navbar__user-name {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-main);
  font-weight: 500;
}

.c-navbar__mobile-user {
  font-size: var(--pp-font-small);
  color: var(--pp-clr-text-main);
  font-weight: 500;
  padding: var(--pp-sp-8) 0;
  border-bottom: 1px solid var(--pp-clr-border);
  margin-bottom: var(--pp-sp-8);
}

/* Disabled button state */
.pp-form-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* =========================
   🎯 Button Components
   ========================= */
.pp-btn {
  @apply inline-block font-semibold transition;
  padding: var(--pp-sp-12) var(--pp-sp-20);

  cursor: pointer;
}

.pp-btn-primary {
  background: var(--pp-clr-secondary);
  color: var(--pp-clr-primary);
  padding: var(--pp-sp-12) var(--pp-sp-24);
  border: none;
  border-radius: var(--pp-bor-rad-24);
  cursor: pointer;
  width: fit-content;
}

.pp-btn-primary:hover {
  transform: scale(1.02);
}

.pp-btn-secondary {
  border: 1px solid var(--pp-clr-border);
  border-radius: 20rem;
  background: transparent;
  color: var(--pp-clr-secondary);
  padding: var(--pp-sp-12) var(--pp-sp-24);
  cursor: pointer;
  white-space: nowrap;
}

.pp-btn-secondary:hover {
  transform: scale(1.02);
  background: var(--pp-clr-secondary);
  color: var(--pp-clr-primary);
}

/* =========================
   💻 Responsive Breakpoints
   ========================= */
@media (max-width: 1024px) {
  :root {
    --pp-font-heading1: 64px;
    --pp-font-heading2: 48px;
    --pp-font-heading3: 32px;
    --pp-font-heading4: 28px;
    --pp-font-heading5: 22px;
    --pp-font-heading6: 20px;
    --pp-font-base-font: 19px;
    --pp-font-base2-font: 18px;
    --pp-font-small-font: 17px;
    --pp-font-extra-small: 16px;
  }

  .pp-max-container {
    padding: 0 var(--pp-sp-20);
  }
}

@media (max-width: 768px) {
  :root {
    --pp-font-heading1: 48px;
    --pp-font-heading2: 36px;
    --pp-font-heading3: 28px;
    --pp-font-heading4: 24px;
    --pp-font-heading5: 20px;
    --pp-font-heading6: 18px;
    --pp-font-base-font: 17px;
    --pp-font-base2-font: 16px;
    --pp-font-small-font: 15px;
    --pp-font-extra-small: 14px;
  }

  .pp-max-container {
    padding: 0 var(--pp-sp-16);
  }
}

@media (max-width: 414px) {
  :root {
    --pp-font-heading1: 32px;
    --pp-font-heading2: 28px;
    --pp-font-heading3: 24px;
    --pp-font-heading4: 22px;
    --pp-font-heading5: 20px;
    --pp-font-heading6: 18px;
    --pp-font-base-font: 16px;
    --pp-font-base2-font: 15px;
    --pp-font-small-font: 14px;
    --pp-font-extra-small: 13px;
  }
  .pp-btn-secondary,
  .pp-btn-primary {
    padding: var(--pp-sp-8) var(--pp-sp-24);
  }
  .pp-btn-primary .pp-max-container {
    padding: 0 var(--pp-sp-12);
  }
  .pp-form-input {
    padding: var(--pp-sp-10) var(--pp-sp-16);
  }
}

/* =========================
   📦 Component CSS Imports
   ========================= */
@import "../styles/CreateServiceModal.css";
@import "../styles/GeneralForm.css";
@import "../styles/PhotoForm.css";
@import "../styles/PricingForm.css";
@import "../styles/TravelFees.css";
@import "../styles/AddOns.css";
@import "../styles/SchedulingForm.css";
@import "../styles/DurationInputs.css";
@import "../styles/AvailabilityInputs.css";
@import "../styles/BlackoutDates.css";
@import "../styles/ReviewForm.css";
@import "../styles/Toggle.css";
@import "../styles/InputField.css";
@import "../styles/ScrollToTopButton.css";
