/* =========================
   📊 Vendor Booking Table Component
   ========================= */

.vendor-booking-table {
  width: 100%;
  background: var(--pp-clr-primary);
  border-radius: var(--pp-bor-rad-12);
  overflow: hidden;
}

/* =========================
   🎛 Header Controls
   ========================= */
.vendor-booking-table__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--pp-sp-20);
  border-bottom: 1px solid var(--pp-clr-border-light);
  background: var(--pp-clr-primary);
}

.vendor-booking-table__filter {
  position: relative;
  display: flex;
  align-items: center;
}

.vendor-booking-table__filter-select {
  appearance: none;
  background: var(--pp-clr-primary);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-8);
  padding: var(--pp-sp-12) var(--pp-sp-16);
  padding-right: var(--pp-sp-40);
  font-size: var(--pp-font-extra-small);
  font-family: var(--pp-font-Metro-Sans);
  color: var(--pp-clr-text-main);
  cursor: pointer;
  min-width: 120px;
  transition: border-color 300ms ease;
}

.vendor-booking-table__filter-select:focus {
  outline: none;
  border-color: var(--pp-clr-secondary);
}

.vendor-booking-table__filter-icon {
  position: absolute;
  right: var(--pp-sp-12);
  color: var(--pp-clr-text-gray);
  pointer-events: none;
  font-size: 16px;
}

.vendor-booking-table__view-toggle {
  display: flex;
  gap: var(--pp-sp-8);
}

.vendor-booking-table__view-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-8);
  background: var(--pp-clr-primary);
  color: var(--pp-clr-text-gray);
  cursor: pointer;
  transition: all 300ms ease;
  font-size: 18px;
}

.vendor-booking-table__view-btn:hover {
  border-color: var(--pp-clr-secondary);
  color: var(--pp-clr-secondary);
}

.vendor-booking-table__view-btn.active {
  background: var(--pp-clr-secondary);
  color: var(--pp-clr-primary);
  border-color: var(--pp-clr-secondary);
}

/* =========================
   🖥 Desktop Table View
   ========================= */
.vendor-booking-table__desktop {
  display: block;
}

.vendor-booking-table__table {
  width: 100%;
  border-collapse: collapse;
  font-family: var(--pp-font-Metro-Sans);
}

.vendor-booking-table__th {
  background: var(--pp-clr-primary);
  padding: var(--pp-sp-16) var(--pp-sp-20);
  text-align: left;
  font-size: var(--pp-font-extra-small);
  font-weight: 600;
  color: var(--pp-clr-text-main);
  border-bottom: 2px solid #d2d2d2;
  position: relative;
  cursor: pointer;
  transition: background-color 300ms ease;
}

.vendor-booking-table__sort-icon {
  margin-left: var(--pp-sp-8);
  color: var(--pp-clr-text-gray);
  font-size: 14px;
  transition: color 300ms ease;
}

.vendor-booking-table__th:hover .vendor-booking-table__sort-icon {
  color: var(--pp-clr-secondary);
}

.vendor-booking-table__row {
  cursor: pointer;
  transition: background-color 300ms ease;
  border-radius: 12px;
}

.vendor-booking-table__row:hover {
  background: #f8f9fa;
}

.vendor-booking-table__td {
  padding: var(--pp-sp-12) var(--pp-sp-20);
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-main);

  vertical-align: middle;
}

/* =========================
   📅 Date Cell Styling
   ========================= */
.vendor-booking-table__date-cell {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-16);
}

.vendor-booking-table__date-badge {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 58px;
  aspect-ratio: 1/1;
  border-radius: var(--pp-bor-rad-8);
  font-weight: 600;
  flex-shrink: 0;
}

.vendor-booking-table__date-badge--blue {
  background: var(--pp-clr-blue-bg);
  color: var(--pp-clr-blue-txt);
}

.vendor-booking-table__date-badge--green {
  background: #d4edda;
  color: #155724;
}

.vendor-booking-table__date-badge--red {
  background: #f8d7da;
  color: #721c24;
}

.vendor-booking-table__date-month {
  font-size: 12px;
  line-height: 1;
  margin-bottom: 2px;
}

.vendor-booking-table__date-day {
  font-size: 20px;
  line-height: 1;
  font-weight: 700;
}

.vendor-booking-table__time {
  color: var(--pp-clr-text-gray);
  font-size: var(--pp-font-extra-small);
  font-weight: 400;
}

/* =========================
   📋 Bookable Column
   ========================= */
.vendor-booking-table__bookable {
  font-weight: 600;
  color: var(--pp-clr-text-main);
}

/* =========================
   📍 Address Styling
   ========================= */
.vendor-booking-table__address {
  line-height: 1.4;
}

.vendor-booking-table__address div:first-child {
  font-weight: 500;
  color: var(--pp-clr-text-main);
}

.vendor-booking-table__address div:last-child {
  color: var(--pp-clr-text-gray);
  font-size: 13px;
}

/* =========================
   🏷 Status Badge
   ========================= */
.vendor-booking-table__status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--pp-sp-8);
  padding: var(--pp-sp-8) var(--pp-sp-12);
  border-radius: var(--pp-bor-rad-8);
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
}

.vendor-booking-table__status-badge img {
  font-size: 16px;
  color: black;
  width: 18px;
  aspect-ratio: 1/1;
}

.vendor-booking-table__status-badge-icon {
  display: inline-flex;
  font-size: 12px;
  color: black;
}

/* =========================
   💰 Amount Column
   ========================= */
.vendor-booking-table__amount {
  text-align: right;
  font-weight: 600;
  color: var(--pp-clr-text-main);
  font-family: var(--pp-font-Metro-Sans);
}

/* =========================
   📱 Mobile/Tablet Card View
   ========================= */
.vendor-booking-table__mobile {
  display: none;
}

.vendor-booking-table__mobile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--pp-sp-16) var(--pp-sp-20);
  background: var(--pp-clr-primary);
  border-bottom: 1px solid var(--pp-clr-border-light);
  font-size: var(--pp-font-extra-small);
  font-weight: 600;
  color: var(--pp-clr-text-main);
}

.vendor-booking-table__mobile-header-text {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8);
}

.vendor-booking-table__mobile-cards {
  display: flex;
  flex-direction: column;
}

.vendor-booking-table__mobile-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--pp-sp-16) var(--pp-sp-20);
  border-bottom: 1px solid var(--pp-clr-border-light);
  cursor: pointer;
  transition: background-color 300ms ease;
}

.vendor-booking-table__mobile-card:hover {
  background: #f8f9fa;
}

.vendor-booking-table__mobile-card-content {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-16);
  flex: 1;
}

.vendor-booking-table__mobile-date-badge {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 50px;
  height: 50px;
  border-radius: var(--pp-bor-rad-8);
  font-weight: 600;
  flex-shrink: 0;
}

.vendor-booking-table__mobile-date-badge--blue {
  background: var(--pp-clr-blue-bg);
  color: var(--pp-clr-blue-txt);
}

.vendor-booking-table__mobile-date-badge--green {
  background: #d4edda;
  color: #155724;
}

.vendor-booking-table__mobile-date-badge--red {
  background: #f8d7da;
  color: #721c24;
}

.vendor-booking-table__mobile-date-badge--gray {
  background: #f8f9fa;
  color: var(--pp-clr-text-gray);
}

.vendor-booking-table__mobile-date-month {
  font-size: 11px;
  line-height: 1;
  margin-bottom: 2px;
}

.vendor-booking-table__mobile-date-day {
  font-size: 18px;
  line-height: 1;
  font-weight: 700;
}

.vendor-booking-table__mobile-info {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-4);
  flex: 1;
}

.vendor-booking-table__mobile-bookable {
  font-size: var(--pp-font-extra-small);
  font-weight: 600;
  color: var(--pp-clr-text-main);
  line-height: 1.3;
}

.vendor-booking-table__mobile-time {
  font-size: 13px;
  color: var(--pp-clr-text-gray);
  font-weight: 400;
}

.vendor-booking-table__mobile-amount {
  font-size: var(--pp-font-extra-small);
  font-weight: 600;
  color: var(--pp-clr-text-main);
  font-family: var(--pp-font-Metro-Sans);
  text-align: right;
}

/* =========================
   📱 Responsive Breakpoints
   ========================= */

/* Tablet: 768px - 1023px */
@media (max-width: 1023px) {
  .vendor-booking-table__desktop {
    display: none;
  }

  .vendor-booking-table__mobile {
    display: block;
  }

  .vendor-booking-table__header {
    padding: var(--pp-sp-16);
  }

  .vendor-booking-table__filter-select {
    min-width: 100px;
    padding: var(--pp-sp-10) var(--pp-sp-12);
    padding-right: var(--pp-sp-32);
  }

  .vendor-booking-table__view-btn {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }
}

/* Mobile: 320px - 767px */
@media (max-width: 767px) {
  .vendor-booking-table__header {
    padding: var(--pp-sp-12);
    flex-direction: column;
    gap: var(--pp-sp-12);
    align-items: stretch;
  }

  .vendor-booking-table__filter {
    justify-content: center;
  }

  .vendor-booking-table__view-toggle {
    justify-content: center;
  }

  .vendor-booking-table__mobile-header {
    padding: var(--pp-sp-12) var(--pp-sp-16);
  }

  .vendor-booking-table__mobile-card {
    padding: var(--pp-sp-12) var(--pp-sp-16);
  }

  .vendor-booking-table__mobile-card-content {
    gap: var(--pp-sp-12);
  }

  .vendor-booking-table__mobile-date-badge {
    min-width: 45px;
    height: 45px;
  }

  .vendor-booking-table__mobile-date-month {
    font-size: 10px;
  }

  .vendor-booking-table__mobile-date-day {
    font-size: 16px;
  }

  .vendor-booking-table__mobile-bookable {
    font-size: 14px;
  }

  .vendor-booking-table__mobile-time {
    font-size: 12px;
  }

  .vendor-booking-table__mobile-amount {
    font-size: 14px;
  }
}

/* Small Mobile: 320px - 479px */
@media (max-width: 479px) {
  .vendor-booking-table__mobile-card {
    
    align-items: flex-start;
    gap: var(--pp-sp-8);
  }

  .vendor-booking-table__mobile-card-content {
    width: 100%;
  }

  .vendor-booking-table__mobile-amount {
    align-self: flex-end;
  }
}

/* =========================
   🎨 Additional Status Colors
   ========================= */
.vendor-booking-table__date-badge--gray {
  background: #f8f9fa;
  color: var(--pp-clr-text-gray);
}

/* =========================
   ♿ Accessibility
   ========================= */
.vendor-booking-table__row:focus-within {
  outline: 2px solid var(--pp-clr-secondary);
  outline-offset: -2px;
}

.vendor-booking-table__view-btn:focus {
  outline: 2px solid var(--pp-clr-secondary);
  outline-offset: 2px;
}

.vendor-booking-table__filter-select:focus {
  outline: 2px solid var(--pp-clr-secondary);
  outline-offset: 2px;
}

/* =========================
   🎭 Animation & Transitions
   ========================= */
.vendor-booking-table__row {
  transition: all 300ms ease;
}

.vendor-booking-table__mobile-card {
  transition: all 300ms ease;
}

.vendor-booking-table__date-badge,
.vendor-booking-table__mobile-date-badge {
  transition: transform 300ms ease;
}

.vendor-booking-table__row:hover .vendor-booking-table__date-badge,
.vendor-booking-table__mobile-card:hover
  .vendor-booking-table__mobile-date-badge {
  transform: scale(1.05);
}

.vendor-booking-table__status-badge {
  transition: all 300ms ease;
}
