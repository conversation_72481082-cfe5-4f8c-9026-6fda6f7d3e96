import { createSlice } from "@reduxjs/toolkit";

const initialState = {
	loading: false,
	error: null,
	layout: "grid", // "grid" | "list"
};

const uiSlice = createSlice({
	name: "ui",
	initialState,
	reducers: {
		setLoading: (state, action) => {
			state.loading = action.payload;
		},
		setError: (state, action) => {
			state.error = action.payload;
		},
		setLayout: (state, action) => {
			state.layout = action.payload;
		},
	},
});

export const { setLoading, setError, setLayout } = uiSlice.actions;
export default uiSlice.reducer;
