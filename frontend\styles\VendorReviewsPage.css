

.rev-toolbar {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 32px;
  margin-bottom: 32px;
}

.rev-summary {
  display: flex;
  gap: 72px;
}

.rev-summary-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  min-width: 105px;
}

/* value + stars on one row */
.rev-summary-line {
  display: flex;
  align-items: center;
  gap: 10px;
}

.rev-summary-value {
  font-size: 34px;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1;
}

.rev-stars {
  display: flex;
  gap: 2px;
}
.rev-stars .star {
  color: #e5e5e5;
  font-size: 16px;
  vertical-align: middle;
}
.rev-stars .filled {
  color: #ffc107;
}

.rev-summary-label {


  font-weight: 600;
}

.rev-bookable-filter {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-start;
  min-width: 130px;
}

.rev-bookable-filter label {
  
  font-weight: 600;
}

.rev-bookable-filter .custom-select-box{
  width: 250px;
}

/* Review Card List */
.rev-list {
  display: flex;
  flex-direction: column;

}

/* Card */
.rev-card {
 
  border-top: 1px solid #D2D2D2;

  padding: 25px 0;
  display: flex;
  flex-direction: column;
  gap: 14px;
  
}

/* Two-column layout inside each card */
.rev-card-inner {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 24px;
}

.rev-card-left {
  width: 100%;
  padding-right: 24px;

  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* group wrapper for each left meta row */
.rev-left-item {
  display: flex;
  gap: 10px;
  align-items: center;
}

.rev-left-label {
  color: #8a8fa0;
 
  font-weight: 400;
  margin-bottom: 4px;
}

.rev-left-value {
  
  font-weight: 600;


}

.rev-card-right {
  flex: 1;
  min-width: 0;
}

.rev-row {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.rev-row-info {
  margin-top: -4px;
  margin-bottom: -4px;
}

.rev-user {
  font-weight: 600;
  font-size: var(--pp-font-heading5);
  color: #1a1a1a;
  margin-right: 8px;
}

.rev-rating {
  display: flex;
  gap: 2px;
}

.rev-rating .star {
  color: #e5e5e5;
 
  vertical-align: middle;
}

.rev-rating .filled {
  color: #ffc107;
}

.rev-date {

  
  color: #727272;
  font-weight: 600;
}

.rev-meta {
  display: flex;
  gap: 16px;
  color: #727272;
  font-size: 12px;
  flex-wrap: wrap;
}

.rev-bold {
  font-weight: 600;
  color: #1a1a1a;
}

.rev-text {
  font-weight: 400;
 
  line-height: 1.5;
  margin-top: 15px;
  margin-bottom: 15px;
  flex: 1;
}

/* Clamp long review text when not expanded */
.rev-text--clamp {
  display: -webkit-box;
  -webkit-line-clamp: 3; /* show 3 lines when collapsed */
  -webkit-box-orient: vertical;
  line-clamp: 3; /* standard property for broader compatibility */
  overflow: hidden;
}

/* Container for text + read more/less button */
.rev-row-readmore {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

/* Read more / Read less button appearance */
.rev-read-more {
  background: transparent;
  border: none;
  color: var(--pp-clr-secondary);
  font-weight: 600;
  cursor: pointer;
  padding: 0;
}

.rev-row-actions {
  justify-content: flex-start;
  gap: 10px;
  margin-top: 6px;
}

/* Action Buttons */



.rev-btn-heart {
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 50%;
 padding: 12px ;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.rev-btn-heart:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.rev-heart-empty {
  font-size: 18px;
  color: #6b7280;
}

.rev-heart-filled {
  font-size: 18px;
  color: #ef4444;
}

.rev-card-comment-gray {
  background: #f4f4f4;
  border-radius: 6px;
  margin: 5px 0 25px 0;
  padding: 16px 18px 12px 18px;
  font-size: 1rem;

  
  display: grid
  ;
      gap: 10px;
      grid-template-columns: 20px 1fr;
}

.rev-card-comment-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 2px;
}

.rev-card-comment-icon {
  color: #a3a3b0;

  font-size: 1.25rem;
}

.rev-card-comment-name {
  font-weight: 600;
  
  
  margin-right: 6px;
}

.rev-card-comment-date {
  color: #727272;
  
  font-weight: 500;
}

.rev-card-comment-body {
  color: #2a2a37;
line-height: 22px;
  margin-top: 2px;
  font-weight: 400;
  word-break: break-word;
}

@media (max-width: 900px) {
  .rev-toolbar {
    margin-top: 20px;
    align-items: stretch;
    gap: 16px;
  }
  .rev-summary {
    gap: 32px;
  }
  .rev-list {
    gap: 16px;
  }
  .rev-card-inner {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  .rev-card-left {

    gap: 12px;
    display: grid;
  }
}

@media (max-width: 600px) {
  .rev-root {
    padding: 16px;
  }
  .rev-card {
    padding: 16px 0;
    gap: 12px;
  }
  .rev-summary {
    gap: 24px;
  }
  .rev-summary-value {
    font-size: 28px;
  }
  .rev-card-comment-header {
 flex-direction: column;
 align-items: start;
 gap: 0px;
 margin-bottom: 10px;
  }
}
