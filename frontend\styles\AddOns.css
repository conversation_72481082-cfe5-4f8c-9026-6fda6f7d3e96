/* =========================
   ➕ Add-ons
   ========================= */

.ao-section {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-32);
}

.ao-section-title {
  font-size: var(--pp-font-heading6);
  font-weight: 600;
  color: var(--pp-clr-text-main);
  margin: 0;
  line-height: 1.3;
}

.ao-section-description {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  margin: 0 0 var(--pp-sp-24) 0;
  line-height: 1.5;
}

/* Add-on Items */
.ao-addon-item {
  border: 1px solid var(--pp-clr-border-light);
  border-radius: var(--pp-bor-rad-12);
  padding: var(--pp-sp-24);
  background-color: var(--pp-clr-primary);
  margin-bottom: var(--pp-sp-20);
}

.ao-new-addon {
  border-radius: var(--pp-bor-rad-12);
  padding: var(--pp-sp-24);
  background-color: rgba(0, 0, 0, 0.02);
  margin-bottom: var(--pp-sp-20);
}

/* Header Section */
.ao-header {
  margin-bottom: var(--pp-sp-20);
}

.ao-form-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr auto;
  gap: var(--pp-sp-16);
  align-items: start;
}

/* Form Groups */
.ao-form-group {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-8);
}

.ao-form-label {
  font-size: var(--pp-font-extra-small);
  font-weight: 500;
  color: var(--pp-clr-text-main);
  margin: 0;
}

.ao-form-input {
  width: 100%;
  padding: var(--pp-sp-12) var(--pp-sp-16);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-8);
  font-size: var(--pp-font-extra-small);
  font-family: var(--pp-font-Metro-Sans);
  background: var(--pp-clr-primary);
  transition: all 0.3s ease;
  color: var(--pp-clr-text-main);
}

.ao-form-input:focus {
  outline: none;
  border-color: var(--pp-clr-secondary);
}

.ao-form-input::placeholder {
  color: var(--pp-clr-text-gray);
}

.ao-form-textarea {
  width: 100%;
  padding: var(--pp-sp-12) var(--pp-sp-16);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-8);
  font-size: var(--pp-font-extra-small);
  font-family: var(--pp-font-Metro-Sans);
  background: var(--pp-clr-primary);
  transition: all 0.3s ease;
  color: var(--pp-clr-text-main);
  resize: vertical;
  min-height: var(--pp-textarea-min-height);
}

.ao-form-textarea:focus {
  outline: none;
  border-color: var(--pp-clr-secondary);
}

.ao-form-textarea::placeholder {
  color: var(--pp-clr-text-gray);
}

/* Price Inputs */
.ao-price-input {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8);
  position: relative;
}

.ao-currency {
  font-size: var(--pp-font-extra-small);
  font-weight: 600;
  color: var(--pp-clr-text-main);
  margin-right: var(--pp-sp-4);
  position: absolute;
  left: var(--pp-sp-16);
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}
.ao-price-input .ao-form-input {
  padding-left: calc(var(--pp-sp-16) + 14px);
}

/* Toggles Section */
.ao-toggles {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-20);
  margin-bottom: var(--pp-sp-20);
}

.ao-toggle-group {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-8);
}

.ao-toggle-wrapper {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-12);
  cursor: pointer;
  user-select: none;
}

.ao-toggle {
  position: relative;
  width: var(--pp-sp-20);
  height: var(--pp-sp-20);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-4);
  background: var(--pp-clr-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.ao-toggle:hover {
  border-color: var(--pp-clr-text-gray);
  transform: scale(1.05);
}

.ao-toggle:focus {
  outline: none;
  border-color: var(--pp-clr-secondary);
}

.ao-toggle:checked {
  background-color: var(--pp-clr-secondary) !important;
  border-color: var(--pp-clr-secondary) !important;
}

.ao-toggle:checked::after {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: var(--pp-sp-12);
  height: var(--pp-sp-12);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.ao-toggle-label {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-main);
  font-weight: 500;
  cursor: pointer;
}

.ao-toggle-description {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  margin: 0 0 0 var(--pp-sp-32);
  line-height: 1.5;
}

/* Buttons */
.ao-btn-remove {
  background: transparent;
  border: none;
  color: var(--pp-clr-text-gray);
  cursor: pointer;
  padding: var(--pp-sp-8);
  border-radius: var(--pp-bor-rad-8);
  transition: all 0.3s ease;
  font-size: var(--pp-font-extra-small);
}

.ao-btn-remove:hover {
  background-color: var(--pp-clr-border-light);
  color: var(--pp-clr-secondary);
}

.ao-btn-add {
  background-color: var(--pp-clr-secondary);
  color: var(--pp-clr-primary);
  border: none;
  border-radius: var(--pp-bor-rad-24);
  padding: var(--pp-sp-12) var(--pp-sp-24);
  font-size: var(--pp-font-extra-small);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: var(--pp-sp-16);
  align-self: flex-start;
}

.ao-btn-add:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--pp-shdw-sm);
}

.ao-btn-add:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Character Count */
.ao-char-count {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  text-align: right;
  margin-top: var(--pp-sp-4);
}

/* Responsive */
@media (max-width: 768px) {
  .ao-section {
    gap: var(--pp-sp-24);
  }

  .ao-form-row {
    grid-template-columns: 1fr;
    gap: var(--pp-sp-12);
  }

  .ao-addon-item,
  .ao-new-addon {
    padding: var(--pp-sp-20);
  }

  .ao-toggles {
    gap: var(--pp-sp-16);
  }

  .ao-toggle-description {
    margin-left: var(--pp-sp-24);
  }
}

@media (max-width: 480px) {
  .ao-section {
    gap: var(--pp-sp-20);
  }

  .ao-addon-item,
  .ao-new-addon {
    padding: var(--pp-sp-16);
  }

  .ao-toggles {
    gap: var(--pp-sp-12);
  }

  .ao-toggle-description {
    margin-left: var(--pp-sp-20);
  }

  .ao-form-input,
  .ao-form-textarea {
    padding: var(--pp-sp-10) var(--pp-sp-12);
  }
}
