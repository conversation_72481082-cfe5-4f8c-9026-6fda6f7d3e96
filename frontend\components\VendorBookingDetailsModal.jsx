import React from "react";
import { Link } from "react-router-dom";
import { IoClose } from "react-icons/io5";
import "../styles/VendorBookingDetailsModal.css";
import img88 from "../src/assets/images/servicesimages/Rectangle 88.svg";
import img101 from "../src/assets/images/servicesimages/Rectangle 101.svg";
import pendingbadge from "../src/assets/images/Pendingbadge.svg";

const paymentData = [
  { label: "Batman", cost: "$55/hr", duration: "1.5 hrs", total: "$82.50" },
  { label: "Robin", cost: "$45/hr", duration: "1 hr", total: "$45.00" },
];
const extraRows = [
  { label: "Travel", total: "$35.00" },
  { label: "Order Total", total: "$162.50", footer: true },
];

const BookingDetailsModal = ({ open, onClose }) => {
  const handleClose = () => {
    onClose();
  };

  const handleOverlayClick = (e) => {
    // Only close if clicking on the overlay, not the modal content
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  // Don't render the modal if it's not open
  if (!open) {
    return null;
  }

  return (
    <div
      className="booking-details-modal__overlay"
      role="dialog"
      aria-modal="true"
      onClick={handleOverlayClick}
    >
      <div className="booking-details-modal" style={{ position: "relative" }}>
        {/* Close icon */}
        <button
          className="booking-details-modal__close-btn"
          type="button"
          aria-label="Close modal"
          onClick={handleClose}
        >
          <IoClose size={28} />
        </button>

  <div className="overflow-containerbooking">
          {/* Modal Header */}
          <div className="booking-details-modal__header">
          <div>
            <h2 className="booking-details-modal__title">New Booking</h2>
            <div className="flex items-center justify-start gap-1 flex-wrap ">
              {" "}
              <div className="booking-details-modal__status flex items-center  gap-1">
                <img src={pendingbadge} alt="" /> Pending Approval |{" "}
              </div>
              <div className="booking-details-modal__meta">
                <Link to="#"> Customer info provided once approved</Link>
              </div>
            </div>
          </div>
          <button className="booking-details-modal__action-btn" type="button">
            Approve Booking
          </button>
        </div>

        {/* Booking Details */}
        <section className="w-full px-0 pt-2 pb-4">
          <div className="flex items-start justify-between w-full">
            <h3 className="text-[22px] semi-bold leading-tight text-black mb-2">
              Booking Details
            </h3>
            <span className="text-[13px] text-black  pr-1 mt-1">
              Order # 3712
            </span>
          </div>
          <div className="flex w-full gap-6 md:gap-4 flex-wrap md:flex-nowrap">
            {/* Left: Details */}
            <div className="flex-1 min-w-[220px]">
              <div className="flex mb-2">
                <span className="w-32 min-w-[120px] text-[13px] font-semibold text-black">
                  Date
                </span>
                <span className="text-[13px] text-black">June 7, 2023</span>
              </div>
              <div className="flex mb-2 align-top">
                <span className="w-32 min-w-[120px] text-[13px] font-semibold text-black">
                  Bookable(s)
                </span>
                <div className="flex flex-col gap-2">
                  <div className="flex flex-col text-[13px] text-black leading-tight">
                    <span className="font-semibold">Batman</span>
                    <span className="text-[13px] text-black font-normal ml-1">
                      4:00pm – 5:30pm
                    </span>
                  </div>
                  <div className="flex flex-col text-[13px] text-black leading-tight mt-1">
                    <span className="font-semibold">Robin</span>
                    <span className="text-[13px] text-black font-normal ml-1">
                      4:30pm – 5:30pm
                    </span>
                  </div>
                </div>
              </div>
              {/* Avatars for small screens */}
              <div className="flex flex-row gap-4 mt-3 md:hidden">
                <img
                  src={img88}
                  alt="Batman"
                  className="w-[80px] h-[64px] object-cover rounded-md border border-gray-200"
                  style={{ boxShadow: "0 1px 2px rgba(0,0,0,0.04)" }}
                />
                <img
                  src={img101}
                  alt="Robin"
                  className="w-[80px] h-[64px] object-cover rounded-md border border-gray-200"
                  style={{ boxShadow: "0 1px 2px rgba(0,0,0,0.04)" }}
                />
              </div>
              <div className="flex mb-2 mt-2 md:mt-0">
                <span className="w-32 min-w-[120px] text-[13px] font-semibold text-black">
                  Company
                </span>
                <span className="text-[13px] text-black">N/A</span>
              </div>
              <div className="flex mb-2">
                <span className="w-32 min-w-[120px] text-[13px] font-semibold text-black">
                  Event Address
                </span>
                <a
                  href="https://maps.google.com/?q=Salt+Lake+City,+UT+85515"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-[13px] text-blue-700 underline"
                >
                  Salt Lake City, UT 85515
                </a>
              </div>
            </div>
            {/* Right: Avatars for medium and up */}
            <div className="flex flex-row gap-4 min-w-[180px] justify-end pt-2 hidden md:flex">
              <img
                src={img88}
                alt="Batman"
                className="w-[80px] h-[64px] object-cover rounded-md border border-gray-200"
                style={{ boxShadow: "0 1px 2px rgba(0,0,0,0.04)" }}
              />
              <img
                src={img101}
                alt="Robin"
                className="w-[80px] h-[64px] object-cover rounded-md border border-gray-200"
                style={{ boxShadow: "0 1px 2px rgba(0,0,0,0.04)" }}
              />
            </div>
          </div>
          <hr className="mt-4 border-t border-gray-200" />
        </section>

        {/* Event Location */}
        <section className="booking-details-modal__location">
          <h3 className="booking-details-modal__section-title">
            Event Location
          </h3>
          <div className="booking-details-modal__map">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d29350.408776610206!2d72.5549056!3d23.1409445!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x395e829d5440f6b5%3A0x5caf5a2cf0a1a628!2sKhoraj%2C%20Gujarat%20382501!5e0!3m2!1sen!2sin!4v1756188279955!5m2!1sen!2sin"
              width="100%"
              height="100%"
              style={{ border: 0 }} // ✅ object
              allowFullScreen // ✅ camelCase
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade" // ✅ camelCase
            ></iframe>
          </div>
        </section>

        {/* Payment Info */}
        <section className="booking-details-modal__payment">
          <h3 className="booking-details-modal__section-title">Payment Info</h3>
          <div className="booking-details-modal__payment-table">
            <div className="booking-details-modal__row booking-details-modal__header">
              <span>Bookable(s)</span>
              <span>Cost</span>
              <span>Duration</span>
              <span>Total</span>
            </div>
            {paymentData.map((item) => (
              <div className="booking-details-modal__row" key={item.label}>
                <span>{item.label}</span>
                <span>{item.cost}</span>
                <span>{item.duration}</span>
                <span>{item.total}</span>
              </div>
            ))}
            <div className="booking-details-modal__divider"></div>
            {extraRows.map((row) => (
              <div
                key={row.label}
                className={`booking-details-modal__row${
                  row.footer ? " booking-details-modal__footer" : ""
                }`}
              >
                <strong>{row.label}</strong>
                <span></span>
                <span></span>
                <span>{row.total}</span>
              </div>
            ))}
            <div className="booking-details-modal__divider"></div>
            <div className="booking-details-modal__refund">
              <Link to="#">Unpaid</Link>
            </div>
          </div>
        </section>

        {/* History */}
        <section className="booking-details-modal__history">
          <h3 className="booking-details-modal__section-title">History</h3>
          <div className="flex flex-wrap items-center gap-2 justify-between max-w-[637px] mt-5 ">
            <span>May 27</span>
            <div>Order submitted. Status set to "Pending Vendor Approval"</div>
          </div>
        </section>
  </div>
      </div>
    </div>
  );
};

export default BookingDetailsModal;
