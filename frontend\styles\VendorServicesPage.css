.srv-root {
  width: 100%;

  background: var(--pp-clr-bg);
  color: var(--pp-clr-text-main);
  font-family: var(--pp-font-Metro-Sans), system-ui, -apple-system, "Segoe UI",
    Roboto, Arial, sans-serif;
}

.srv-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--pp-sp-20);
  margin-bottom: var(--pp-sp-40);
}
.srv-toolbar-left {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-16);
}
.srv-toolbar-left .custom-select-box {
  min-width: 150px;
}
.srv-search {
  position: relative;
  min-width: 230px;
  width: 100%;
  max-width: 500px;
  display: flex;
  align-items: center;
}
.srv-search input {
  width: 100%;
  padding: var(--pp-sp-10) var(--pp-sp-16) var(--pp-sp-10) 36px;
  border: 1px solid var(--pp-clr-border);
  border-radius: 6px;
  background: var(--pp-clr-primary);
  color: var(--pp-clr-text-main);
}
.srv-search-icon {
  position: absolute;
  left: 12px;
  pointer-events: none;
  color: var(--pp-clr-text-gray);
  font-size: 20px;
}

.srv-add-btn {
  background: var(--pp-clr-secondary);
  color: var(--pp-clr-primary);
  border: none;
  border-radius: var(--pp-bor-rad-24);
  font-size: var(--pp-font-base2-font);
  font-weight: 500;
  padding: var(--pp-sp-12) var(--pp-sp-32);
  cursor: pointer;
  margin-left: auto;
  transition: background 0.2s, color 0.2s;
  max-width: fit-content;
}
.srv-add-btn:hover,
.srv-add-btn:focus-visible {
  background: var(--pp-clr-text-main);
  color: var(--pp-clr-primary);
  outline: none;
}

/* Table styles */
.srv-table {
  width: 100%;
  border-radius: var(--pp-bor-rad-12);
}

.srv-table-row {
  display: grid;
  grid-template-columns: 1.5fr 1fr 0.7fr;
  align-items: center;
  min-height: 62px;
  padding: var(--pp-sp-16);
  border-bottom: 1px solid var(--pp-clr-border);
  cursor: pointer;
  background: var(--pp-clr-primary);
  transition: background 0.15s;
}
.srv-table-head {
  background: var(--pp-clr-primary);
  font-size: var(--pp-font-base2-font);
  color: var(--pp-clr-text-gray);
  font-weight: 600;
  min-height: 64px;
  border-bottom: 3px solid var(--pp-clr-border);
  position: sticky;
  top: 0;
  z-index: 2;
  cursor: default;
  padding: 0;
}
.srv-table-row:hover:not(.srv-table-head),
.srv-table-row:focus-visible:not(.srv-table-head) {
  background: #f4f4f4;
  outline: none;
}
.srv-table-caret {
  font-size: 1.2em;
  color: var(--pp-clr-secondary);
  margin-left: 3px;
  vertical-align: middle;
}

.srv-col {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-16);
  font-size: var(--pp-font-base2-font);
  min-width: 0;
  height: 62px;
}
.srv-col-avatar {
  gap: var(--pp-sp-12);
  font-weight: 500;
  color: var(--pp-clr-secondary);
}
.srv-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: var(--pp-font-base2-font);
  color: var(--pp-clr-text-main);
}
.srv-avatar {
  width: 58px;
  aspect-ratio: 1/1;
  border-radius: 6px;
  object-fit: cover;
  background: var(--pp-clr-border-light);
}
.srv-col-status {
  color: var(--pp-clr-secondary);
  font-weight: 500;
  text-transform: capitalize;
}
.srv-col-fee {
  color: var(--pp-clr-text-main);
  font-weight: 500;
  justify-content: flex-end;
}

/* Responsive */
@media (max-width: 1200px) {
  .srv-toolbar {
    gap: var(--pp-sp-16);
  }
  .srv-add-btn {
    margin-left: 0;
    width: 100%;
  }
}
@media (max-width: 800px) {
  .srv-table-row,
  .srv-table-head {
    padding: 0 var(--pp-sp-8);
    font-size: var(--pp-font-small-font);
    min-height: 48px;
  }
  .srv-avatar {
    width: 32px;
    height: 32px;
  }
}
@media (max-width: 600px) {
  .srv-table-row,
  .srv-table-head {
    grid-template-columns: 3fr 1fr 0.8fr;
    min-height: 42px;
  }
  .srv-avatar {
    width: 28px;
    height: 28px;
  }
  .srv-toolbar {
    gap: 12px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-start;
  }
}

@media (max-width: 412px) {
  .srv-search {
    order: -1;
  }
  .srv-toolbar-left {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }
}
