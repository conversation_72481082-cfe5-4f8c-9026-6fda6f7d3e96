import "../styles/Footer.css";
import {
  <PERSON>a<PERSON><PERSON><PERSON><PERSON>,
  Fa<PERSON>inked<PERSON><PERSON>n,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FaInstagram,
} from "react-icons/fa";
import { IoIosArrowRoundForward } from "react-icons/io";
import { Link } from "react-router-dom";
import logo from "../src/assets/images/party-pipeline-white-logo.svg";
import accredited from "../src/assets/images/AccreditedBusiness.svg";
import trustpilot from "../src/assets/images/Trustpilot.svg";

export default function Footer() {
  return (
    <footer className="c-footer">
      <div className="pp-max-container c-footer__top">
        <div className="c-footer__brand">
          <img
            className="c-footer__logo"
            src={logo}
            alt="Party Pipeline"
            loading="lazy"
            decoding="async"
          />
          <p className="c-footer__tagline">Bring your event to life!</p>
          <Link className="c-footer__cta" to="/browse-services">
            <span>Browse vendors</span>
            <IoIosArrowRoundForward className="c-footer__cta-icon" />
          </Link>
        </div>

        <nav className="c-footer__nav">
          <div className="c-footer__col">
            <h4 className="c-footer__heading">Events</h4>
            <ul className="c-footer__links">
              <li>
                <Link to="/browse-services" className="c-footer__link">
                  Browse all vendors
                </Link>
              </li>
              <li>
                <Link to="/become-vendor" className="c-footer__link">
                  Become a vendor
                </Link>
              </li>
              <li>
                <Link to="/cart" className="c-footer__link">
                  Cart
                </Link>
              </li>
            </ul>
          </div>
          <div className="c-footer__col">
            <h4 className="c-footer__heading">Party Pipeline</h4>
            <ul className="c-footer__links">
              <li>
                <Link to="/why-book" className="c-footer__link">
                  Why book with us
                </Link>
              </li>
              <li>
                <Link to="/blog" className="c-footer__link">
                  Blog
                </Link>
              </li>
              <li>
                <Link to="/contact" className="c-footer__link">
                  Contact
                </Link>
              </li>
            </ul>
          </div>
        </nav>

        <div className="c-footer__social">
          <a href="#" aria-label="Facebook" className="c-footer__social-btn">
            <FaFacebookF />
          </a>
          <a href="#" aria-label="LinkedIn" className="c-footer__social-btn">
            <FaLinkedinIn />
          </a>

          <a href="#" aria-label="Instagram" className="c-footer__social-btn">
            <FaInstagram />
          </a>
        </div>
      </div>

      <div className="c-footer__divider pp-max-container " />

      <div className="pp-max-container c-footer__bottom">
        <div className="c-footer__legal">
          <span className="c-footer__copyright">
            © {new Date().getFullYear()} Party Pipeline
          </span>
          <Link to="/privacy-policy" className="c-footer__legal-link">
            Privacy policy
          </Link>
          <Link to="/terms-of-use" className="c-footer__legal-link">
            Terms of use
          </Link>
        </div>
      </div>
    </footer>
  );
}
