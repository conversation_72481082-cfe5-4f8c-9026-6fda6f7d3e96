// CheckoutPage.jsx
import React, { useState } from "react";
import CheckoutFormSteps from "../components/checkout/CheckoutFormSteps";
import CheckoutBillingForm from "../components/checkout/CheckoutBillingForm";
import CheckoutSummaryCard from "../components/checkout/CheckoutSummaryCard";
import CheckoutEventForm from "../components/checkout/CheckoutEventForm";
import PaymentForm from "../components/checkout/PaymentForm";
import "../styles/CheckoutPage.css";

export default function CheckoutPage() {
  const [activeStep, setActiveStep] = useState(1);

  const handleStepChange = (step) => {
    setActiveStep(step);
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 1:
        return <CheckoutBillingForm />;
      case 2:
        return <CheckoutEventForm />;
      case 3:
        return <PaymentForm />;
      default:
        return <CheckoutBillingForm />;
    }
  };

  return (
    <section>
      <div className="pp-max-container">
        <h1 className="pp-checkout-title">Checkout</h1>
        <div className="pp-checkout-page">
          <div className="checkout-first">
            <div className="pp-checkout-header">
              <div className="pp-checkout-notice" role="status">
                <em>Returning Customer?</em>&nbsp;
                <button type="button" className="pp-checkout-notice-link">
                  Login
                </button>
              </div>
              <div className="pp-checkout-steps-bar">
                <CheckoutFormSteps
                  activeStep={activeStep}
                  onStepChange={handleStepChange}
                />
              </div>
            </div>

            <div className="pp-checkout-main-form">{renderStepContent()}</div>
          </div>

          <div className="pp-checkout-sidebar" aria-label="Order Summary">
            <CheckoutSummaryCard />
          </div>
        </div>
      </div>
    </section>
  );
}
