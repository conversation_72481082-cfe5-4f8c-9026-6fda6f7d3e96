.tf-root {
  width: 100%;
  
  font-family: var(--pp-font-Metro-Sans), system-ui, -apple-system, "Segoe UI",
    Roboto, Arial, sans-serif;
  background: var(--pp-clr-bg);
}

.tf-block {
  max-width: 600px;
}

.tf-title {
  font-size: var(--pp-font-heading5);
  font-weight: 600;
  color: var(--pp-clr-text-main);
  margin-bottom: var(--pp-sp-8);
}

.tf-subtitle {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  margin-bottom: var(--pp-sp-16);
}

.tf-checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8);
  user-select: none;
  cursor: pointer;
  margin-bottom: var(--pp-sp-24);
}

.tf-checkbox {
  display: none;
}

.tf-custom-checkbox {
  width: var(--pp-sp-20);
  height: var(--pp-sp-20);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-4);
  background: var(--pp-clr-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: border-color 0.2s;
  box-sizing: border-box;
}
.tf-checkbox:checked + .tf-custom-checkbox {
  border-color: var(--pp-clr-secondary);
}
.tf-check-icon {
  color: var(--pp-clr-secondary);
  font-size: 1.1em;
  font-weight: bold;
}
.tf-checkbox-text {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-main);
  font-weight: 500;
}

.tf-form {
  display: grid;
  grid-template-columns: 1fr 1fr; /* 2 columns */
  grid-template-rows: auto auto; /* 2 rows */
  gap: var(--pp-sp-40);
}

.tf-form > :nth-child(1) {
  grid-column: 1 / -1; /* first item spans both columns */
}
.tf-form-group {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-8);
}

.tf-label {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-main);
  font-weight: 500;
  margin-bottom: var(--pp-sp-4);
}

.tf-label-desc {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  margin-bottom: var(--pp-sp-4);
}

.tf-input-row {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8);
}

.tf-input-adorn {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8);
  position: relative;
}
.tf-dollar-text {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
}
.tf-input-symbol {
  color: var(--pp-clr-text-main);
  font-size: var(--pp-font-base2-font);
  padding-right: var(--pp-sp-4);
}

.tf-input {
  width: 100px;
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-8);
  padding: var(--pp-sp-10) var(--pp-sp-8);
  background: var(--pp-clr-primary);
  color: var(--pp-clr-text-main);
  font-size: var(--pp-font-base2-font);
  outline: none;
  transition: border-color 0.2s;
}

.tf-input:disabled {
  background: var(--pp-clr-border-light);
  color: var(--pp-clr-text-gray);
}

.tf-input-suffix {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  margin-left: var(--pp-sp-4);
}

/* Responsive tweaks */
@media (max-width: 1000px) {
  .tf-form {
    grid-template-columns: 1fr 1fr;
    gap: var(--pp-sp-20);
  }
}
@media (max-width: 700px) {
  .tf-block {
    margin-left: 0;
    padding: 0;
  }
  .tf-form {
    grid-template-columns: 1fr;
    gap: var(--pp-sp-16);
  }
}
