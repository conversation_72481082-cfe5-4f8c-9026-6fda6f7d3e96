const Message = require('../models/Message');
const User = require('../models/User');
const { apiLogger } = require('../utils/logger');

/**
 * Socket.IO Message Handler
 * Handles real-time messaging between users
 */
class MessageSocketHandler {
    constructor(io) {
        this.io = io;
        this.connectedUsers = new Map(); // userId -> socketId mapping
        this.userRooms = new Map(); // socketId -> Set of rooms
    }

    /**
     * Initialize socket handlers
     */
    initialize() {
        this.io.on('connection', (socket) => {
            apiLogger.info('User connected to socket', {
                socketId: socket.id,
                userId: socket.user?.id
            });

            this.handleConnection(socket);
            this.handleDisconnection(socket);
            this.handleJoinRoom(socket);
            this.handleLeaveRoom(socket);
            this.handleSendMessage(socket);
            this.handleTyping(socket);
            this.handleMessageRead(socket);
            this.handleOnlineStatus(socket);
        });
    }

    /**
     * Handle new socket connection
     */
    handleConnection(socket) {
        if (socket.user) {
            // Store user connection
            this.connectedUsers.set(socket.user.id, socket.id);
            this.userRooms.set(socket.id, new Set());

            // Join user to their personal room
            socket.join(`user_${socket.user.id}`);

            // Broadcast online status to contacts
            this.broadcastOnlineStatus(socket.user.id, 'online');

            // Send pending notifications
            this.sendPendingNotifications(socket);
        }
    }

    /**
     * Handle socket disconnection
     */
    handleDisconnection(socket) {
        socket.on('disconnect', (reason) => {
            apiLogger.info('User disconnected from socket', {
                socketId: socket.id,
                userId: socket.user?.id,
                reason
            });

            if (socket.user) {
                // Remove user connection
                this.connectedUsers.delete(socket.user.id);
                this.userRooms.delete(socket.id);

                // Broadcast offline status to contacts
                this.broadcastOnlineStatus(socket.user.id, 'offline');
            }
        });
    }

    /**
     * Handle joining conversation room
     */
    handleJoinRoom(socket) {
        socket.on('join_conversation', async (data) => {
            try {
                const { otherUserId } = data;

                if (!otherUserId) {
                    socket.emit('error', { message: 'Other user ID is required' });
                    return;
                }

                // Verify other user exists
                const otherUser = await User.findById(otherUserId);
                if (!otherUser) {
                    socket.emit('error', { message: 'User not found' });
                    return;
                }

                // Create conversation room ID
                const roomId = this.createConversationRoomId(socket.user.id, otherUserId);

                // Join the room
                socket.join(roomId);
                this.userRooms.get(socket.id)?.add(roomId);

                socket.emit('joined_conversation', {
                    otherUserId,
                    roomId,
                    otherUser: {
                        id: otherUser._id,
                        firstName: otherUser.firstName,
                        lastName: otherUser.lastName,
                        profileImage: otherUser.profileImage,
                        isOnline: this.isUserOnline(otherUserId)
                    }
                });

                // Mark messages as delivered
                await this.markMessagesAsDelivered(socket.user.id, otherUserId);

                apiLogger.debug('User joined conversation', {
                    userId: socket.user.id,
                    otherUserId,
                    roomId
                });

            } catch (error) {
                apiLogger.error('Error joining conversation', {
                    error: error.message,
                    userId: socket.user?.id
                });
                socket.emit('error', { message: 'Failed to join conversation' });
            }
        });
    }

    /**
     * Handle leaving conversation room
     */
    handleLeaveRoom(socket) {
        socket.on('leave_conversation', (data) => {
            const { otherUserId } = data;
            const roomId = this.createConversationRoomId(socket.user.id, otherUserId);

            socket.leave(roomId);
            this.userRooms.get(socket.id)?.delete(roomId);

            socket.emit('left_conversation', { otherUserId, roomId });

            apiLogger.debug('User left conversation', {
                userId: socket.user.id,
                otherUserId,
                roomId
            });
        });
    }

    /**
     * Handle sending messages
     */
    handleSendMessage(socket) {
        socket.on('send_message', async (data) => {
            try {
                const { recipientId, content, bookingId, attachments = [] } = data;

                if (!recipientId || !content) {
                    socket.emit('error', { message: 'Recipient ID and content are required' });
                    return;
                }

                // Verify recipient exists
                const recipient = await User.findById(recipientId);
                if (!recipient) {
                    socket.emit('error', { message: 'Recipient not found' });
                    return;
                }

                // Check if sender is blocked by recipient
                if (recipient.blockedUsers.includes(socket.user.id)) {
                    socket.emit('error', { message: 'You are blocked by this user' });
                    return;
                }

                // Create message
                const message = new Message({
                    senderId: socket.user.id,
                    recipientId,
                    content,
                    bookingId,
                    attachments,
                    type: attachments.length > 0 ? 'media' : 'text'
                });

                await message.save();

                // Populate message for response
                await message.populate([
                    { path: 'senderId', select: 'firstName lastName profileImage' },
                    { path: 'recipientId', select: 'firstName lastName profileImage' },
                    { path: 'bookingId', select: 'serviceDetails.title eventDetails.date' }
                ]);

                // Create room ID for the conversation
                const roomId = this.createConversationRoomId(socket.user.id, recipientId);

                // Emit message to conversation room
                this.io.to(roomId).emit('new_message', {
                    message: message.toObject(),
                    conversationId: roomId
                });

                // Send push notification to recipient if offline
                if (!this.isUserOnline(recipientId)) {
                    await this.sendPushNotification(recipient, {
                        type: 'new_message',
                        title: `New message from ${socket.user.firstName}`,
                        body: content.length > 50 ? content.substring(0, 50) + '...' : content,
                        data: {
                            senderId: socket.user.id,
                            messageId: message._id,
                            conversationId: roomId
                        }
                    });
                }

                // Emit delivery confirmation to sender
                socket.emit('message_sent', {
                    tempId: data.tempId, // For client-side message tracking
                    messageId: message._id,
                    sentAt: message.createdAt
                });

                apiLogger.info('Message sent via socket', {
                    messageId: message._id,
                    senderId: socket.user.id,
                    recipientId,
                    hasAttachments: attachments.length > 0
                });

            } catch (error) {
                apiLogger.error('Error sending message via socket', {
                    error: error.message,
                    userId: socket.user?.id
                });
                socket.emit('error', { message: 'Failed to send message' });
            }
        });
    }

    /**
     * Handle typing indicators
     */
    handleTyping(socket) {
        socket.on('typing_start', (data) => {
            const { recipientId } = data;
            const roomId = this.createConversationRoomId(socket.user.id, recipientId);

            socket.to(roomId).emit('user_typing', {
                userId: socket.user.id,
                userName: `${socket.user.firstName} ${socket.user.lastName}`,
                isTyping: true
            });
        });

        socket.on('typing_stop', (data) => {
            const { recipientId } = data;
            const roomId = this.createConversationRoomId(socket.user.id, recipientId);

            socket.to(roomId).emit('user_typing', {
                userId: socket.user.id,
                userName: `${socket.user.firstName} ${socket.user.lastName}`,
                isTyping: false
            });
        });
    }

    /**
     * Handle message read receipts
     */
    handleMessageRead(socket) {
        socket.on('mark_message_read', async (data) => {
            try {
                const { messageId } = data;

                const message = await Message.findById(messageId);

                if (!message) {
                    socket.emit('error', { message: 'Message not found' });
                    return;
                }

                // Check if user is the recipient
                if (message.recipientId.toString() !== socket.user.id) {
                    socket.emit('error', { message: 'You can only mark messages sent to you as read' });
                    return;
                }

                if (!message.isRead) {
                    message.isRead = true;
                    message.readAt = new Date();
                    await message.save();

                    // Notify sender about read receipt
                    const roomId = this.createConversationRoomId(message.senderId, socket.user.id);
                    this.io.to(roomId).emit('message_read', {
                        messageId: message._id,
                        readAt: message.readAt,
                        readBy: socket.user.id
                    });

                    apiLogger.debug('Message marked as read via socket', {
                        messageId: message._id,
                        readBy: socket.user.id
                    });
                }

            } catch (error) {
                apiLogger.error('Error marking message as read', {
                    error: error.message,
                    userId: socket.user?.id
                });
                socket.emit('error', { message: 'Failed to mark message as read' });
            }
        });

        socket.on('mark_conversation_read', async (data) => {
            try {
                const { otherUserId } = data;

                const result = await Message.updateMany(
                    {
                        senderId: otherUserId,
                        recipientId: socket.user.id,
                        isRead: false
                    },
                    {
                        isRead: true,
                        readAt: new Date()
                    }
                );

                // Notify other user about read receipts
                const roomId = this.createConversationRoomId(socket.user.id, otherUserId);
                this.io.to(roomId).emit('conversation_read', {
                    readBy: socket.user.id,
                    readAt: new Date(),
                    messagesCount: result.modifiedCount
                });

                socket.emit('conversation_marked_read', {
                    otherUserId,
                    messagesMarked: result.modifiedCount
                });

                apiLogger.debug('Conversation marked as read via socket', {
                    userId: socket.user.id,
                    otherUserId,
                    messagesMarked: result.modifiedCount
                });

            } catch (error) {
                apiLogger.error('Error marking conversation as read', {
                    error: error.message,
                    userId: socket.user?.id
                });
                socket.emit('error', { message: 'Failed to mark conversation as read' });
            }
        });
    }

    /**
     * Handle online status
     */
    handleOnlineStatus(socket) {
        socket.on('get_online_status', async (data) => {
            const { userIds } = data;

            if (!Array.isArray(userIds)) {
                socket.emit('error', { message: 'User IDs must be an array' });
                return;
            }

            const onlineStatus = {};
            userIds.forEach(userId => {
                onlineStatus[userId] = this.isUserOnline(userId);
            });

            socket.emit('online_status', onlineStatus);
        });
    }

    /**
     * Utility methods
     */

    createConversationRoomId(userId1, userId2) {
        // Create a consistent room ID regardless of order
        const sortedIds = [userId1, userId2].sort();
        return `conversation_${sortedIds[0]}_${sortedIds[1]}`;
    }

    isUserOnline(userId) {
        return this.connectedUsers.has(userId);
    }

    async broadcastOnlineStatus(userId, status) {
        // Get user's contacts (users they have conversations with)
        const contacts = await this.getUserContacts(userId);

        contacts.forEach(contactId => {
            const contactSocketId = this.connectedUsers.get(contactId);
            if (contactSocketId) {
                this.io.to(contactSocketId).emit('user_status_changed', {
                    userId,
                    status,
                    timestamp: new Date()
                });
            }
        });
    }

    async getUserContacts(userId) {
        // Get unique user IDs from conversations
        const conversations = await Message.aggregate([
            {
                $match: {
                    $or: [{ senderId: userId }, { recipientId: userId }]
                }
            },
            {
                $group: {
                    _id: {
                        $cond: [
                            { $eq: ['$senderId', userId] },
                            '$recipientId',
                            '$senderId'
                        ]
                    }
                }
            }
        ]);

        return conversations.map(conv => conv._id.toString());
    }

    async markMessagesAsDelivered(userId, otherUserId) {
        await Message.updateMany(
            {
                senderId: otherUserId,
                recipientId: userId,
                isDelivered: false
            },
            {
                isDelivered: true,
                deliveredAt: new Date()
            }
        );
    }

    async sendPendingNotifications(socket) {
        // Get unread message count
        const unreadCount = await Message.countDocuments({
            recipientId: socket.user.id,
            isRead: false
        });

        if (unreadCount > 0) {
            socket.emit('unread_messages_count', { count: unreadCount });
        }

        // Get any pending real-time notifications
        // This would typically come from a notifications collection
        socket.emit('pending_notifications', {
            messages: unreadCount,
            timestamp: new Date()
        });
    }

    async sendPushNotification(user, notification) {
        // In a real implementation, this would integrate with push notification services
        // like Firebase Cloud Messaging, Apple Push Notification Service, etc.
        apiLogger.info('Push notification sent', {
            userId: user._id,
            type: notification.type,
            title: notification.title
        });

        // Could also save notification to database for later retrieval
    }

    /**
     * Send real-time notification to user
     */
    sendNotificationToUser(userId, notification) {
        const userSocketId = this.connectedUsers.get(userId);
        if (userSocketId) {
            this.io.to(userSocketId).emit('notification', notification);
            return true;
        }
        return false;
    }

    /**
     * Send booking-related notifications
     */
    sendBookingNotification(bookingData, notificationType) {
        const { customerId, vendorId, booking } = bookingData;

        const notification = {
            type: notificationType,
            bookingId: booking._id,
            timestamp: new Date(),
            data: booking
        };

        switch (notificationType) {
            case 'booking_approved':
                notification.title = 'Booking Approved';
                notification.message = 'Your booking request has been approved by the vendor';
                this.sendNotificationToUser(customerId, notification);
                break;

            case 'booking_rejected':
                notification.title = 'Booking Declined';
                notification.message = 'Your booking request has been declined';
                this.sendNotificationToUser(customerId, notification);
                break;

            case 'new_booking_request':
                notification.title = 'New Booking Request';
                notification.message = 'You have received a new booking request';
                this.sendNotificationToUser(vendorId, notification);
                break;

            case 'payment_received':
                notification.title = 'Payment Received';
                notification.message = 'Payment has been received for your booking';
                this.sendNotificationToUser(vendorId, notification);
                break;

            case 'booking_completed':
                notification.title = 'Booking Completed';
                notification.message = 'Your event has been marked as completed';
                this.sendNotificationToUser(customerId, notification);
                break;
        }
    }

    /**
     * Get connected users count
     */
    getConnectedUsersCount() {
        return this.connectedUsers.size;
    }

    /**
     * Get all connected users (admin only)
     */
    getConnectedUsers() {
        return Array.from(this.connectedUsers.entries()).map(([userId, socketId]) => ({
            userId,
            socketId,
            connectedAt: new Date() // In real implementation, track connection time
        }));
    }
}

module.exports = MessageSocketHandler;
