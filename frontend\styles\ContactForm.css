/* ================================
   📄 CONTACT PAGE WRAPPER
   ================================ */
.pp-contact-page {
  background: var(--pp-clr-bg);
  color: var(--pp-clr-text-main);
  padding-top: var(--pp-sp-60);
  padding-bottom: var(--pp-sp-60);
}

/* ================================
     🎉 BANNER & TITLE
     ================================ */
.pp-contact-banner {
  display: grid;
  justify-items: center;
  gap: var(--pp-sp-24);
  margin-bottom: var(--pp-sp-60);
}

.pp-contact-title {
  font-size: var(--pp-font-heading3);
  font-family: var(--pp-font-Playfair-Display);
  font-weight: 600;
  text-align: center;
}

/* ================================
     📐 GRID LAYOUT
     ================================ */
.pp-contact-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--pp-sp-60);
  margin-bottom: var(--pp-sp-60);
}

/* ================================
     📝 FORM STYLES
     ================================ */
.pp-contact-form {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-20);
}
.pp-contact-form h2 {
  font-size: var(--pp-font-heading6);

  font-weight: 600;
}
.pp-contact-info h3 {
  font-size: var(--pp-font-heading6);
  font-weight: 600;
}

.pp-form-field {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-8);
  flex: 1;
}

.pp-form-field label {
  font-size: var(--pp-font-small-font);
  font-weight: 500;
  color: var(--pp-clr-text-main);
}

.pp-contact-form input,
.pp-contact-form textarea {
  width: 100%;
  padding: var(--pp-sp-10) var(--pp-sp-12);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-8);
  font-size: var(--pp-font-base2-font);
  background: var(--pp-clr-bg);
}

.pp-contact-form textarea {
  min-height: var(--pp-sp-160);
  resize: none;
  height: 190px;
}

/* ================================
     ✅ FORM ACTIONS
     ================================ */
.pp-form-actions {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-16);
  align-items: flex-start;
}

.pp-recaptcha {
  border: 1px solid var(--pp-clr-border);
  padding: var(--pp-sp-12);
  font-size: var(--pp-font-small-font);
  color: var(--pp-clr-text-gray);
  border-radius: var(--pp-bor-rad-8);
  background: var(--pp-clr-bg-light);
}

/* ================================
     📞 CONTACT INFO
     ================================ */
.pp-contact-info {
  font-size: var(--pp-font-small-font);
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-16);
}

/* ================================
     📱 RESPONSIVE
     ================================ */
@media (max-width: 1024px) {
  .pp-contact-grid {
    grid-template-columns: 1fr;
    gap: var(--pp-sp-40);
    margin-bottom: 40px;
  }
  .pp-contact-info {
    flex-direction: row;
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .pp-contact-page {
    padding-top: var(--pp-sp-40);
    padding-bottom: var(--pp-sp-60);
  }

  .pp-contact-title {
    font-size: var(--pp-font-heading4);
  }

  .pp-contact-page .pp-form-row {
    flex-direction: column;
  }

  .pp-form-actions {
    align-items: stretch;
  }
  .pp-contact-grid {
    grid-template-columns: 1fr;
    gap: var(--pp-sp-40);
    padding: 0;
    margin-bottom: 20px;
  }
}

@media (max-width: 480px) {
  .pp-contact-info {
    flex-direction: column;
    justify-content: space-between;
  }
}
