import trustedbyone from "../src/assets/images/trustedby/trustedbyone.svg";
import trustedbytwo from "../src/assets/images/trustedby/trustedbytwo.svg";
import trustedbythree from "../src/assets/images/trustedby/trustedbythree.svg";
import trustedbyfour from "../src/assets/images/trustedby/trustedbyfour.svg";
import trustedbyfive from "../src/assets/images/trustedby/trustedbyfive.svg";

export default function TrustedBySection() {
  const logos = [
    trustedbyone,
    trustedbytwo,
    trustedbythree,
    trustedbyfour,
    trustedbyfive,
  ];
  
  return (
    <section className="trustedby">
      <p>Trusted by</p>
      <div className="logos-container">
        {logos.map((src, i) => (
          <img key={i} src={src} alt="logo" loading="lazy" decoding="async" />
        ))}
      </div>
    </section>
  );
}
