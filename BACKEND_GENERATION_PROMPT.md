# 🎪 Party Pipeline - Backend Generation Prompt

## 🎯 Project Overview

Build a scalable, secure Node.js backend for **Party Pipeline** - an online event marketplace that connects customers with event vendors (entertainers, food trucks, photo booths, rentals, etc.). The platform supports three user roles: Customers, Vendors, and Admins with comprehensive booking, payment, and communication systems.

## 🛠 Tech Stack Requirements

- **Runtime**: Node.js (JavaScript)
- **Framework**: Express.js (REST API)
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT-based with role-based access control
- **Payments**: Stripe Connect for marketplace payments with escrow
- **Real-time**: Socket.IO for messaging
- **Email**: Nodemailer with SMTP/SendGrid
- **File Upload**: Multer with cloud storage (AWS S3)
- **Validation**: Joi validation library
- **Security**: bcrypt, helmet, rate limiting

## 📂 Required Project Structure

```
/src
  /config
    - database.js          // MongoDB connection
    - stripe.js           // Stripe configuration
    - email.js            // Email service config
    - cloudinary.js       // File upload config
  /models
    - User.js             // Customer/Vendor/Admin model
    - VendorProfile.js    // Extended vendor information
    - Service.js          // Service listings
    - Category.js         // Service categories/subcategories
    - Booking.js          // Booking management
    - Event.js            // Customer events
    - Cart.js             // Shopping cart
    - Message.js          // Real-time messaging
    - Review.js           // Reviews and ratings
    - Transaction.js      // Payment transactions
    - Blog.js             // CMS blog posts
    - CMSPage.js          // Static CMS pages
  /controllers
    - authController.js   // Authentication logic
    - userController.js   // User management
    - vendorController.js // Vendor operations
    - serviceController.js // Service management
    - bookingController.js // Booking operations
    - cartController.js   // Cart operations
    - paymentController.js // Stripe payment processing
    - messageController.js // Messaging system
    - adminController.js  // Admin operations
    - reviewController.js // Review system
    - cmsController.js    // Content management
  /routes
    - auth.js            // Authentication routes
    - users.js           // User routes
    - vendors.js         // Vendor routes
    - services.js        // Service routes
    - bookings.js        // Booking routes
    - cart.js            // Cart routes
    - payments.js        // Payment routes
    - messages.js        // Messaging routes
    - admin.js           // Admin routes
    - reviews.js         // Review routes
    - cms.js             // CMS routes
  /middlewares
    - auth.js            // JWT authentication
    - roleAuth.js        // Role-based access control
    - validation.js      // Request validation
    - upload.js          // File upload handling
    - rateLimiting.js    // Rate limiting
    - errorHandler.js    // Global error handling
  /services
    - emailService.js    // Email notifications
    - stripeService.js   // Stripe operations
    - uploadService.js   // File upload service
    - searchService.js   // Search and filtering
  /utils
    - logger.js          // Winston logging
    - responseHelper.js  // Standardized responses
    - pagination.js      // Pagination utility
    - dateHelper.js      // Date/time utilities
  /sockets
    - messageSocket.js   // Real-time messaging handlers
  /validators
    - authValidators.js  // Auth validation schemas
    - serviceValidators.js // Service validation schemas
    - bookingValidators.js // Booking validation schemas
  server.js              // Application entry point
```

## 🗃 Database Models (MongoDB Schemas)

### User Model
```javascript
{
  email: String (unique, required),
  password: String (hashed),
  role: String (enum: ['customer', 'vendor', 'admin']),
  firstName: String,
  lastName: String,
  phone: String,
  addresses: [{
    type: String (billing/event),
    street: String,
    city: String,
    state: String,
    zipCode: String,
    country: String
  }],
  isVerified: Boolean,
  isActive: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```

### VendorProfile Model
```javascript
{
  userId: ObjectId (ref: User),
  businessName: String,
  businessType: String,
  taxId: String,
  description: String,
  website: String,
  socialMedia: {
    facebook: String,
    instagram: String,
    twitter: String
  },
  serviceAreas: [String], // Cities/regions served
  documents: [{
    type: String (license, insurance, id),
    url: String,
    status: String (pending, approved, rejected)
  }],
  stripeAccountId: String,
  stripeOnboardingCompleted: Boolean,
  verification: {
    status: String (pending, approved, rejected, suspended),
    submittedAt: Date,
    reviewedAt: Date,
    reviewedBy: ObjectId,
    notes: String
  },
  ratings: {
    average: Number,
    count: Number
  },
  travelSettings: {
    costPerMile: Number,
    freeRadius: Number,
    maxDistance: Number
  }
}
```

### Service Model
```javascript
{
  vendorId: ObjectId (ref: User),
  categoryId: ObjectId (ref: Category),
  subcategoryId: ObjectId (ref: Category),
  title: String,
  description: String,
  images: [String], // URLs
  videos: [String], // URLs
  pricing: {
    method: String (hourly, package, per_person),
    hourlyRate: Number,
    packages: [{
      title: String,
      duration: Number,
      price: Number,
      description: String
    }],
    perPersonRate: Number,
    minPeople: Number,
    maxPeople: Number
  },
  addOns: [{
    title: String,
    pricingMethod: String (flat, quantity, hourly),
    price: Number,
    description: String,
    multipleCost: Boolean
  }],
  availability: {
    schedule: [{
      dayOfWeek: Number (0-6),
      timeSlots: [{
        startTime: String,
        endTime: String
      }]
    }],
    blackoutDates: [Date],
    minDuration: Number, // minutes
    maxDuration: Number, // minutes
    advanceBooking: Number // days
  },
  travelFee: {
    costPerMile: Number,
    freeRadius: Number
  },
  status: String (draft, active, inactive),
  orderConfirmationEmail: String,
  createdAt: Date,
  updatedAt: Date
}
```

### Category Model
```javascript
{
  name: String,
  slug: String (unique),
  description: String,
  icon: String,
  image: String,
  parentId: ObjectId (ref: Category), // null for main categories
  status: String (active, inactive),
  sortOrder: Number,
  createdAt: Date
}
```

### Booking Model
```javascript
{
  bookingId: String (unique),
  customerId: ObjectId (ref: User),
  vendorId: ObjectId (ref: User),
  serviceId: ObjectId (ref: Service),
  eventId: ObjectId (ref: Event),
  eventDetails: {
    date: Date,
    startTime: String,
    endTime: String,
    duration: Number, // minutes
    location: {
      address: String,
      city: String,
      state: String,
      zipCode: String,
      coordinates: {
        lat: Number,
        lng: Number
      }
    },
    attendees: Number,
    specialRequests: String
  },
  serviceDetails: {
    selectedPackage: String,
    addOns: [{
      title: String,
      quantity: Number,
      price: Number
    }],
    totalServiceCost: Number,
    travelCost: Number,
    totalCost: Number
  },
  status: String (pending, approved, confirmed, completed, cancelled, refunded),
  paymentStatus: String (pending, paid, refunded, failed),
  paymentIntentId: String,
  cancellationPolicy: {
    reason: String,
    refundAmount: Number,
    refundPercentage: Number,
    cancelledAt: Date,
    cancelledBy: ObjectId
  },
  metadata: {
    approvedAt: Date,
    completedAt: Date,
    lastStatusUpdate: Date
  },
  createdAt: Date,
  updatedAt: Date
}
```

### Event Model
```javascript
{
  customerId: ObjectId (ref: User),
  name: String,
  description: String,
  attendees: Number,
  eventDate: Date,
  venue: {
    name: String,
    address: String,
    city: String,
    state: String,
    zipCode: String,
    contactPerson: {
      name: String,
      phone: String,
      email: String
    }
  },
  companyName: String,
  status: String (draft, planned, completed),
  totalBudget: Number,
  createdAt: Date,
  updatedAt: Date
}
```

### Cart Model
```javascript
{
  customerId: ObjectId (ref: User),
  items: [{
    serviceId: ObjectId (ref: Service),
    eventId: ObjectId (ref: Event),
    selectedPackage: String,
    eventDate: Date,
    startTime: String,
    duration: Number,
    attendees: Number,
    addOns: [{
      title: String,
      quantity: Number,
      price: Number
    }],
    totalPrice: Number,
    addedAt: Date
  }],
  totalAmount: Number,
  expiresAt: Date,
  createdAt: Date,
  updatedAt: Date
}
```

### Message Model
```javascript
{
  conversationId: String,
  senderId: ObjectId (ref: User),
  recipientId: ObjectId (ref: User),
  bookingId: ObjectId (ref: Booking),
  content: String,
  attachments: [String],
  messageType: String (text, image, file),
  isRead: Boolean,
  readAt: Date,
  createdAt: Date
}
```

### Transaction Model
```javascript
{
  bookingId: ObjectId (ref: Booking),
  customerId: ObjectId (ref: User),
  vendorId: ObjectId (ref: User),
  stripePaymentIntentId: String,
  amount: Number,
  platformFee: Number,
  stripeFee: Number,
  vendorPayout: Number,
  currency: String,
  status: String (pending, completed, failed, refunded),
  payoutStatus: String (pending, completed, failed),
  payoutDate: Date,
  refundAmount: Number,
  refundReason: String,
  metadata: Object,
  createdAt: Date,
  updatedAt: Date
}
```

### Review Model
```javascript
{
  bookingId: ObjectId (ref: Booking),
  customerId: ObjectId (ref: User),
  vendorId: ObjectId (ref: User),
  serviceId: ObjectId (ref: Service),
  rating: Number (1-5),
  comment: String,
  response: String, // Vendor response
  isPublic: Boolean,
  isVerified: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```

## 🔐 Authentication & Security

### JWT Implementation
- Access tokens (15 minutes expiry)
- Refresh tokens (7 days expiry)
- Role-based middleware: `requireAuth`, `requireRole(['customer', 'vendor', 'admin'])`

### Password Security
- bcrypt hashing (rounds: 12)
- Password reset tokens (30 minutes expiry)
- Account verification tokens

### Stripe Security
- Webhook signature verification
- Idempotency keys for payments
- Secure Connect account onboarding

## 🌐 API Endpoints

### Authentication Routes (`/api/auth`)
```
POST   /register              // User registration
POST   /login                 // User login
POST   /logout                // User logout
POST   /refresh               // Refresh token
POST   /forgot-password       // Password reset request
POST   /reset-password        // Password reset confirmation
POST   /verify-email          // Email verification
POST   /resend-verification   // Resend verification email
```

### Customer Routes (`/api/customers`)
```
GET    /profile               // Get customer profile
PUT    /profile               // Update customer profile
GET    /events                // Get customer events
POST   /events                // Create new event
PUT    /events/:id            // Update event
DELETE /events/:id            // Delete event
GET    /bookings              // Get customer bookings
GET    /bookings/:id          // Get booking details
PUT    /bookings/:id/cancel   // Cancel booking
GET    /messages              // Get customer messages
POST   /messages              // Send message
GET    /reviews               // Get customer reviews
POST   /reviews               // Create review
```

### Vendor Routes (`/api/vendors`)
```
GET    /dashboard             // Vendor dashboard data
GET    /profile               // Get vendor profile
PUT    /profile               // Update vendor profile
POST   /onboarding            // Stripe Connect onboarding
GET    /onboarding/status     // Check onboarding status
GET    /services              // Get vendor services
POST   /services              // Create new service
PUT    /services/:id          // Update service
DELETE /services/:id          // Delete service
GET    /bookings              // Get vendor bookings
PUT    /bookings/:id/approve  // Approve booking
PUT    /bookings/:id/reject   // Reject booking
GET    /balance               // Get account balance
POST   /payout                // Request payout
GET    /analytics             // Vendor analytics
GET    /reviews               // Get vendor reviews
POST   /reviews/:id/respond   // Respond to review
```

### Service Routes (`/api/services`)
```
GET    /                      // Browse services (with filters)
GET    /search                // Search services
GET    /categories            // Get all categories
GET    /:id                   // Get service details
GET    /:id/availability      // Check availability
POST   /:id/calculate-price   // Calculate total price
```

### Cart Routes (`/api/cart`)
```
GET    /                      // Get cart contents
POST   /add                   // Add item to cart
PUT    /update/:itemId        // Update cart item
DELETE /remove/:itemId        // Remove cart item
DELETE /clear                 // Clear cart
```

### Booking Routes (`/api/bookings`)
```
POST   /                      // Create booking from cart
GET    /:id                   // Get booking details
PUT    /:id/confirm           // Confirm booking payment
```

### Payment Routes (`/api/payments`)
```
POST   /create-intent         // Create payment intent
POST   /confirm               // Confirm payment
POST   /webhook               // Stripe webhook
GET    /methods               // Get saved payment methods
POST   /methods               // Add payment method
DELETE /methods/:id           // Remove payment method
```

### Message Routes (`/api/messages`)
```
GET    /conversations         // Get all conversations
GET    /conversations/:id     // Get conversation messages
POST   /conversations/:id     // Send message
PUT    /conversations/:id/read // Mark as read
```

### Admin Routes (`/api/admin`)
```
GET    /dashboard             // Admin dashboard
GET    /vendors               // List all vendors
PUT    /vendors/:id/approve   // Approve vendor
PUT    /vendors/:id/suspend   // Suspend vendor
GET    /bookings              // List all bookings
GET    /transactions          // List all transactions
POST   /refund                // Process refund
GET    /categories            // Manage categories
POST   /categories            // Create category
PUT    /categories/:id        // Update category
DELETE /categories/:id        // Delete category
GET    /cms/pages             // CMS page management
POST   /cms/pages             // Create CMS page
PUT    /cms/pages/:id         // Update CMS page
GET    /cms/blogs             // Blog management
POST   /cms/blogs             // Create blog post
PUT    /cms/blogs/:id         // Update blog post
GET    /analytics             // Platform analytics
```

## 💳 Stripe Payment Integration

### Payment Flow
1. **Create Payment Intent**: When customer proceeds to checkout
2. **Hold in Escrow**: Funds held in marketplace account
3. **Vendor Approval**: Vendor accepts/rejects booking
4. **Auto-refund**: If vendor rejects within time limit
5. **Event Completion**: Mark as completed after event date
6. **Payout Release**: Transfer to vendor minus commission (T+3 days)

### Stripe Connect Setup
```javascript
// Vendor onboarding
const account = await stripe.accounts.create({
  type: 'express',
  country: 'US',
  email: vendor.email,
  capabilities: {
    card_payments: {requested: true},
    transfers: {requested: true}
  }
});

// Payment with commission
const paymentIntent = await stripe.paymentIntents.create({
  amount: totalAmount,
  currency: 'usd',
  application_fee_amount: platformFee,
  transfer_data: {
    destination: vendorStripeAccountId,
  }
});
```

### Cancellation Policy Implementation
```javascript
const calculateRefund = (booking, cancellationDate) => {
  const eventDate = new Date(booking.eventDetails.date);
  const daysUntilEvent = Math.ceil((eventDate - cancellationDate) / (1000 * 60 * 60 * 24));
  
  let refundPercentage = 0;
  if (daysUntilEvent >= 30) refundPercentage = 100;
  else if (daysUntilEvent >= 14) refundPercentage = 50;
  else if (daysUntilEvent >= 7) refundPercentage = 25;
  else refundPercentage = 0;
  
  const processingFee = booking.serviceDetails.totalCost * 0.029; // 2.9%
  const refundAmount = (booking.serviceDetails.totalCost * refundPercentage / 100) - processingFee;
  
  return Math.max(0, refundAmount);
};
```

## 📧 Email Notification System

### Email Templates Required
- Welcome email (customer/vendor)
- Email verification
- Password reset
- Booking confirmation (customer)
- Booking request (vendor)
- Booking approval/rejection
- Payment confirmation
- Payout notification
- Review request
- Event reminder (24h before)

### Implementation
```javascript
const emailTemplates = {
  bookingConfirmation: {
    subject: 'Booking Confirmed - {{eventName}}',
    template: 'booking-confirmation.html'
  },
  vendorBookingRequest: {
    subject: 'New Booking Request - {{serviceName}}',
    template: 'vendor-booking-request.html'
  }
  // ... more templates
};
```

## 🔍 Search & Filter Implementation

### Service Search Features
- **Text Search**: Title, description, vendor name
- **Category Filter**: Main category and subcategory
- **Location Filter**: Distance-based search
- **Availability Filter**: Date range picker
- **Price Range**: Min/max price slider
- **Rating Filter**: Minimum star rating
- **Vendor Filter**: Specific vendor selection

### Search API
```javascript
GET /api/services?
  q=photography&
  category=entertainment&
  subcategory=music&
  location=90210&
  radius=50&
  date=2024-06-15&
  minPrice=100&
  maxPrice=500&
  minRating=4&
  sortBy=price_asc&
  page=1&
  limit=20
```

## 🎯 Performance & Optimization

### Database Indexing
```javascript
// User indexes
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ role: 1, isActive: 1 });

// Service indexes
db.services.createIndex({ vendorId: 1, status: 1 });
db.services.createIndex({ categoryId: 1, subcategoryId: 1 });
db.services.createIndex({ "location.coordinates": "2dsphere" });
db.services.createIndex({ title: "text", description: "text" });

// Booking indexes
db.bookings.createIndex({ customerId: 1, status: 1 });
db.bookings.createIndex({ vendorId: 1, status: 1 });
db.bookings.createIndex({ "eventDetails.date": 1 });
```

### Caching Strategy
- Redis for session storage
- Cache frequently accessed data (categories, popular services)
- Cache search results for common queries

### File Upload
- Image compression and resizing
- Multiple format support (JPEG, PNG, WebP)
- Video upload with size limits
- CDN integration for fast delivery

## 🚀 Deployment Requirements

### Environment Variables
```bash
# Database
MONGODB_URI=
REDIS_URL=

# JWT
JWT_SECRET=
JWT_REFRESH_SECRET=

# Stripe
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=
STRIPE_CONNECT_CLIENT_ID=

# Email
EMAIL_SERVICE=sendgrid
EMAIL_API_KEY=
EMAIL_FROM=

# File Upload
CLOUDINARY_CLOUD_NAME=
CLOUDINARY_API_KEY=
CLOUDINARY_API_SECRET=

# App
NODE_ENV=production
PORT=5000
BASE_URL=https://party-pipeline.com
FRONTEND_URL=https://party-pipeline.com
```

### Docker Configuration
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 5000
CMD ["npm", "start"]
```

## 📊 Analytics & Reporting

### Vendor Analytics
- Revenue trends (daily, weekly, monthly)
- Booking conversion rates
- Service performance metrics
- Customer feedback analysis
- Geographic booking distribution

### Admin Analytics
- Platform GMV (Gross Merchandise Volume)
- Commission revenue
- User acquisition metrics
- Popular service categories
- Vendor performance rankings

## 🛡 Security Best Practices

### Input Validation
- Joi schemas for all request validation
- SQL injection prevention
- XSS protection with helmet
- Rate limiting by IP and user

### Data Protection
- GDPR compliance for EU users
- PCI DSS compliance for payment data
- Data encryption at rest and in transit
- Regular security audits

### API Security
- CORS configuration
- Request size limits
- API versioning
- Comprehensive logging

## ✅ Testing Requirements

### Unit Tests
- Model validation tests
- Controller logic tests
- Service function tests
- Utility function tests

### Integration Tests
- API endpoint tests
- Database operation tests
- Stripe integration tests
- Email service tests

### Test Coverage
- Minimum 80% code coverage
- All critical payment flows tested
- Error handling scenarios covered

## 📝 Documentation Requirements

### API Documentation
- OpenAPI/Swagger documentation
- Request/response examples
- Error code documentation
- Authentication examples

### Setup Documentation
- Installation instructions
- Environment configuration
- Database setup
- Stripe configuration

---

## 🎯 Implementation Checklist

- [ ] Project structure setup
- [ ] Database models with validation
- [ ] Authentication & authorization system
- [ ] Core API endpoints (auth, users, services)
- [ ] Stripe Connect integration
- [ ] Payment flow implementation
- [ ] Email notification system
- [ ] File upload service
- [ ] Search & filter functionality
- [ ] Real-time messaging (Socket.IO)
- [ ] Admin panel APIs
- [ ] Vendor dashboard APIs
- [ ] Customer booking flow
- [ ] Review & rating system
- [ ] Analytics & reporting
- [ ] Error handling & logging
- [ ] Input validation & security
- [ ] Testing suite
- [ ] API documentation
- [ ] Deployment configuration

**Priority Order**: Authentication → Core Models → Payment Integration → Booking Flow → Messaging → Admin Features

---

*Generate a complete, production-ready backend that implements all the above requirements with clean, maintainable code following Node.js best practices.*
