import React, { useState } from "react";
import CustomSelect from "../CustomeSelect";
import { RiDeleteBin6Line } from "react-icons/ri";
import Checkbox from "../Checkbox";
import Toggle from "../common/Toggle";
import TravelFees from "../common/TravelFees";
import AddOns from "../common/AddOns";

export default function PricingForm({ formData, onChange, config, category }) {
  // Set default pricing method based on category
  const getDefaultPricingMethod = () => {
    // Use formData.pricingMethod if available, otherwise default to "hourly"
    return formData.pricingMethod || "hourly";
  };

  const [pricingMethod, setPricingMethod] = useState(getDefaultPricingMethod());
  const [packages, setPackages] = useState(
    formData.packages || [
      { title: "", duration: "", price: "", description: "" },
    ]
  );
  const [multipleWorkers, setMultipleWorkers] = useState({
    enabled: formData.multipleWorkers?.enabled || false,
    maxWorkers: formData.multipleWorkers?.maxWorkers || "",
    multiplyCosts: formData.multipleWorkers?.multiplyCosts || false,
    workerTitle: formData.multipleWorkers?.workerTitle || "",
  });

  if (!config) return <div>No pricing configuration available</div>;

  const handleFieldChange = (e) => {
    // Support both event and direct object (for hourlyRate, etc)
    if (e && e.target) {
      const { name, value, type, checked } = e.target;
      onChange({
        ...formData,
        [name]: type === "checkbox" ? checked : value,
      });
    } else if (e && typeof e === "object") {
      // For direct object, e.g. { target: { name, value, type, checked } }
      const { name, value, type, checked } = e.target;
      onChange({
        ...formData,
        [name]: type === "checkbox" ? checked : value,
      });
    }
  };

  const handlePricingMethodChange = (method) => {
    setPricingMethod(method);
    onChange({ ...formData, pricingMethod: method });
  };

  const handlePackageChange = (index, field, value) => {
    const newPackages = [...packages];
    newPackages[index] = { ...newPackages[index], [field]: value };
    setPackages(newPackages);
    onChange({ ...formData, packages: newPackages });
  };

  const addPackage = () => {
    const newPackages = [
      ...packages,
      { title: "", duration: "", price: "", description: "" },
    ];
    setPackages(newPackages);
    onChange({ ...formData, packages: newPackages });
  };

  const removePackage = (index) => {
    const newPackages = packages.filter((_, i) => i !== index);
    setPackages(newPackages);
    onChange({ ...formData, packages: newPackages });
  };

  const handleMultipleWorkersChange = (field, value) => {
    const newMultipleWorkers = { ...multipleWorkers, [field]: value };
    setMultipleWorkers(newMultipleWorkers);
    onChange({ ...formData, multipleWorkers: newMultipleWorkers });
  };

  const renderPackageCard = (pkg, index) => (
    <div key={index} className="pf-package-card">
      <div className="pf-package-header">
        <div className="pf-package-form-row">
          <div className="pf-package-form-group">
            <label className="pf-package-form-label">Package Title</label>
            <div className="relative">
            <input
              type="text"
              className="pf-package-form-input"
              placeholder="Enter package title"
              value={pkg.title}
              onChange={(e) =>
                handlePackageChange(index, "title", e.target.value)
              }
              maxLength={40}
            />
            <div className="pf-package-char-count">40</div>
            </div>
          </div>
        </div>
        <div className="pf-package-form-row">
          <div className="pf-package-form-group">
            <label className="pf-package-form-label">Duration</label>
            <div className="pf-package-duration-input">
              <input
                type="number"
                className="pf-package-form-input"
                placeholder="0"
                value={pkg.duration}
                onChange={(e) =>
                  handlePackageChange(index, "duration", e.target.value)
                }
              />
              <span className="pf-package-unit">Hour(s)</span>
            </div>
          </div>
          <div className="pf-package-form-group">
            <label className="pf-package-form-label">Price</label>
            <div className="pf-package-price-input">
              <span className="pf-package-currency">$</span>
              <input
                type="number"
                className="pf-package-form-input"
                placeholder="0"
                value={pkg.price}
                onChange={(e) =>
                  handlePackageChange(index, "price", e.target.value)
                }
              />
            </div>
          </div>
          <div className="pf-package-form-group pf-package-delete-group">
            <button
              type="button"
              className="pf-package-btn-delete"
              onClick={() => removePackage(index)}
              aria-label="Remove package"
            >
              <RiDeleteBin6Line />
            </button>
          </div>
        </div>
      </div>
      <div className="pf-package-form-group">
        <label className="pf-package-form-label">Description</label>
        <div className="relative">
        <textarea
          className="pf-package-form-textarea"
          placeholder="Describe your package..."
          value={pkg.description}
          onChange={(e) =>
            handlePackageChange(index, "description", e.target.value)
          }
          rows={3}
          maxLength={400}
        />
        <div className="pf-package-char-count">400</div></div>
      </div>
    </div>
  );

  // Unified pricing section for all categories
  const renderPricingSection = () => {
    return (
      <div className="pf-section">
        <h3 className="pf-section-title">Pricing</h3>
        <div className="pf-form-group">
          <label className="pf-form-label">Pricing Method</label>
          <CustomSelect
            className="pf-form-select"
            value={pricingMethod}
            onChange={(val) => handlePricingMethodChange(val)}
            options={[
              { value: "hourly", label: "Hourly Rate" },
              { value: "packages", label: "Package" },
            ]}
          />
        </div>

        {pricingMethod === "hourly" && (
          <div className="pf-form-group">
            <label className="pf-form-label">Hourly Rate</label>
            <p className="pf-description">
              {category === "rental"
                ? "Cost per service (all factors) - used for partial hours"
                : "Cost per hour. Will be per actual hours."}
            </p>
            <div className="pf-price-input">
              <span className="pf-currency">$</span>
              <input
                type="number"
                className={
                  category === "music" || category === "entertainment"
                    ? "pf-form-input hourly-input"
                    : "hourly-input pf-form-input"
                }
                placeholder="0"
                value={formData.hourlyRate || ""}
                onChange={(e) =>
                  handleFieldChange({
                    target: { name: "hourlyRate", value: e.target.value },
                  })
                }
              />
              <span className="pf-unit">/ hour</span>
            </div>
          </div>
        )}

        {pricingMethod === "packages" && (
          <div className="pf-packages-section">
            <h4 className="pf-subsection-title">Packages</h4>
            <p className="pf-description">
              Create packages for customers to choose from.
            </p>
            {packages.map(renderPackageCard)}
            <button
              type="button"
              className="pf-package-btn-add"
              onClick={addPackage}
            >
              Add Package
            </button>
          </div>
        )}

        <div className="pf-form-group">
          <Checkbox
            className=" "
            checked={!!formData.isTaxable}
            // Fix: Use a direct boolean toggle for onChange
            onChange={() =>
              onChange({
                ...formData,
                isTaxable: !formData.isTaxable,
              })
            }
            label="This service is taxable"
            name="isTaxable"
            id="isTaxable"
          />
        </div>
      </div>
    );
  };

  // For worker title char count
  const workerTitleMaxLength = 30;
  const workerTitleLength = multipleWorkers.workerTitle
    ? multipleWorkers.workerTitle.length
    : 0;
  const workerTitleCharsLeft = workerTitleMaxLength - workerTitleLength;

  return (
    <div className="pf-pricing-form">
      {renderPricingSection()}
      {config.multipleWorkers && (
        <div className="pf-section">
          <h3 className="pf-section-title">Multiple Workers</h3>

          <div className="pf-multiple-workers-grid">
            <div className="pf-multiple-workers-item">
              <Toggle
                checked={multipleWorkers.enabled}
                onChange={(checked) =>
                  handleMultipleWorkersChange("enabled", checked)
                }
                label="Multiple Workers"
                description="Allow a customer to book multiple workers to perform this particular service at one event. (ie. multiple face painters)"
              />
            </div>

            {multipleWorkers.enabled && (
              <>
                <div className="pf-multiple-workers-item">
                  <label className="pf-form-label">
                    Maximum Amount of Workers
                  </label>
                  <p className="pf-description">
                    The maximum number of workers a customer can book for this
                    service.
                  </p>
                  <input
                    type="number"
                    className="pf-form-input hourly-input"
                    placeholder="0"
                    value={multipleWorkers.maxWorkers}
                    onChange={(e) =>
                      handleMultipleWorkersChange("maxWorkers", e.target.value)
                    }
                  />
                </div>

                <div className="pf-multiple-workers-item">
                  <Toggle
                    checked={multipleWorkers.multiplyCosts}
                    onChange={(checked) =>
                      handleMultipleWorkersChange("multiplyCosts", checked)
                    }
                    label="Multiply Costs"
                    description="Multiply the cost by number of workers the customer selects."
                  />
                </div>

                <div className="pf-multiple-workers-item">
                  <label className="pf-form-label">Worker Title</label>
                  <p className="pf-description">
                    What would you like your workers called? (ie. "Face
                    Painters")
                  </p>
         <div className="relative max-w-[308px]">
         <input
                    type="text"
                    className="pf-form-input "
                    placeholder=""
                    value={multipleWorkers.workerTitle}
                    onChange={(e) =>
                      handleMultipleWorkersChange("workerTitle", e.target.value)
                    }
                    maxLength={workerTitleMaxLength}
                  />
                  <span className="pf-package-char-count">
                    {workerTitleCharsLeft}
                  </span>
         </div>
                </div>
              </>
            )}
          </div>
        </div>
      )}
      {config.travelFees && (
        <TravelFees
          value={formData.travelFees || {}}
          onChange={(val) => onChange({ ...formData, travelFees: val })}
          category={category}
        />
      )}

      {config.addons && (
        <AddOns
          addOns={formData.addOns || []}
          onChange={(val) => onChange({ ...formData, addOns: val })}
          category={category}
        />
      )}
    </div>
  );
}
