import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../redux/useAuth';

/**
 * Protected Route component that requires authentication
 * Redirects to login if user is not authenticated
 */
const ProtectedRoute = ({ children, requiredRole = null, redirectTo = '/login' }) => {
  const navigate = useNavigate();
  const { isAuthenticated, user, loading } = useAuth();

  useEffect(() => {
    if (!loading) {
      if (!isAuthenticated) {
        navigate(redirectTo);
        return;
      }

      // Check role-based access if required
      if (requiredRole && user?.role !== requiredRole) {
        // Redirect based on user's actual role
        if (user?.role === 'vendor') {
          navigate('/vendor');
        } else if (user?.role === 'customer') {
          navigate('/');
        } else {
          navigate('/login');
        }
        return;
      }
    }
  }, [isAuthenticated, user, loading, navigate, requiredRole, redirectTo]);

  // Show loading while checking authentication
  if (loading) {
    return (
      <div className="auth-loading">
        <div>Loading...</div>
      </div>
    );
  }

  // Don't render children if not authenticated or wrong role
  if (!isAuthenticated || (requiredRole && user?.role !== requiredRole)) {
    return null;
  }

  return children;
};

export default ProtectedRoute;
