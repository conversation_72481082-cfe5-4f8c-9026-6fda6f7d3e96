import React, { useMemo, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { authApi } from '../redux/apiUtils';
import { toast } from 'react-toastify';

const ResetPassword = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const token = useMemo(() => searchParams.get('token') || '', [searchParams]);

  const [form, setForm] = useState({ password: '', confirm: '' });
  const [loading, setLoading] = useState(false);

  const onChange = (e) => {
    const { name, value } = e.target;
    setForm((f) => ({ ...f, [name]: value }));
  };

  const onSubmit = async (e) => {
    e.preventDefault();
    if (!token) {
      toast.error('Missing or invalid reset token.');
      return;
    }
    if (form.password.length < 6) {
      toast.error('Password must be at least 6 characters.');
      return;
    }
    if (form.password !== form.confirm) {
      toast.error('Passwords do not match.');
      return;
    }
    setLoading(true);
    try {
      await authApi.resetPassword({ token, password: form.password, confirmPassword: form.confirm });
      toast.success('Password reset successfully. Please log in.');
      navigate('/login');
    } catch (e) {
      // Errors are toasted by apiUtils
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="pp-max-container" style={{ padding: '60px 0' }}>
      <div style={{ maxWidth: 480, margin: '0 auto' }}>
        <h1 style={{ fontSize: 'var(--pp-font-heading4)', marginBottom: 'var(--pp-sp-16)' }}>
          Reset your password
        </h1>
        <p className="pp-form-text" style={{ marginBottom: 'var(--pp-sp-24)', textAlign: 'left' }}>
          Choose a new password for your account.
        </p>
        <form onSubmit={onSubmit}>
          <div className="pp-form-group">
            <label htmlFor="password" className="pp-form-label">New password</label>
            <input
              id="password"
              name="password"
              type="password"
              className="pp-form-input"
              placeholder="Enter a new password"
              value={form.password}
              onChange={onChange}
              required
              minLength={6}
            />
          </div>
          <div className="pp-form-group">
            <label htmlFor="confirm" className="pp-form-label">Confirm password</label>
            <input
              id="confirm"
              name="confirm"
              type="password"
              className="pp-form-input"
              placeholder="Re-enter your new password"
              value={form.confirm}
              onChange={onChange}
              required
              minLength={6}
            />
          </div>
          <button type="submit" className="pp-form-button" disabled={loading}>
            {loading ? 'Resetting...' : 'Reset password'}
          </button>
        </form>

        <div className="pp-form-text pp-form-text-bottom">
          Back to <a href="/login" className="pp-form-link">Log in</a>
        </div>
      </div>
    </div>
  );
};

export default ResetPassword;
