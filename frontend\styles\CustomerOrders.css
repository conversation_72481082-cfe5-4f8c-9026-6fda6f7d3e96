.CustomerOrders-root {
    max-width: 1400px;
    margin: 0 auto;
    
    background: var(--pp-clr-bg);
    color: var(--pp-clr-text-main);
    font-family: var(--pp-font-Metro-Sans);
  }
  
  .CustomerOrders-title {
    font-size: var(--pp-font-heading4);
    font-weight: 700;
    margin-bottom: var(--pp-sp-24);
  }
  .CustomerOrders-tabs {
    display: flex;
    gap: var(--pp-sp-24);
    border-bottom: 1px solid var(--pp-clr-border-light);
    margin-bottom: var(--pp-sp-24);
  }
  .CustomerOrders-tab {
    font-size: var(--pp-font-extra-small);
    color: var(--pp-clr-text-gray);
    text-decoration: none;
    padding-bottom: var(--pp-sp-12);
    transition: color 0.18s;
  }
  .CustomerOrders-tab--active {
    color: var(--pp-clr-secondary);
    font-weight: 600;
    border-bottom: 2px solid var(--pp-clr-secondary);
  }
  
  
  
  .CustomerOrders-sectionTitle {
    font-size: var(--pp-font-heading5);
    font-weight: 600;
    margin-bottom: var(--pp-sp-24);
  }
  
  .CustomerOrders-table {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: var(--pp-sp-16);
  }

  .CustomerOrders-row {
    display: flex;
    flex-direction: column;
    gap: var(--pp-sp-8);
    padding: var(--pp-sp-16) var(--pp-sp-24);
    border-radius: var(--pp-bor-rad-12);
    border: 1px solid var(--pp-clr-border-light);
    background: var(--pp-clr-primary);
    box-shadow: none;
  }
  .CustomerOrders-row:hover {
    background: var(--pp-clr-border-light);
  }
  
  /* Header: date left, links right */
  .CustomerOrders-rowHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--pp-sp-16);
    padding-bottom: var(--pp-sp-12);
  }
  .CustomerOrders-date {
    font-size: var(--pp-font-heading6);
    font-weight: 600;
    color: var(--pp-clr-text-main);
  }
  .CustomerOrders-linkCol {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--pp-sp-24);
  }
  .CustomerOrders-link {
    
    font-size: var(--pp-font-extra-small);
    text-decoration: underline;
    cursor: pointer;
    font-weight: 500;
    transition: color .16s;
  }
  .CustomerOrders-link:hover {
    color: var(--pp-clr-secondary);
  }
  
  /* Body: three labeled columns */
  .CustomerOrders-rowBody {
    display: grid;
    grid-template-columns: 120px 120px 1fr;
    gap: var(--pp-sp-24);
    align-items: start;
  }
  .CustomerOrders-colLabel {
    font-weight: 600;
    font-size: var(--pp-font-extra-small);
    margin-bottom: 4px;
  }
  .CustomerOrders-colValue {
    color: var(--pp-clr-text-main);
    font-size: var(--pp-font-small-font);
    font-weight: 400;
  
  }
  
  /* Responsive */
  @media (max-width: 800px) {
    .CustomerOrders-row {
      padding: var(--pp-sp-16) var(--pp-sp-12);
    }
    .CustomerOrders-rowHeader {
      align-items: flex-start;
      flex-direction: column;
      gap: var(--pp-sp-8);
    }
    .CustomerOrders-linkCol {
      align-items: flex-start;
      gap: var(--pp-sp-10);
    }
  
  }
  @media (max-width: 550px) {
    .CustomerOrders-rowBody{
      grid-template-columns: 1fr;
      gap: var(--pp-sp-12);
    }
  }