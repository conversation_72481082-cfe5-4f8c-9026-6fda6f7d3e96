import React, { useState } from "react";
import "../../styles/PaymentForm.css";
import visa from "../../src/assets/images/visa.svg";
import paypal from "../../src/assets/images/paypal.svg";
import mastercard from "../../src/assets/images/mastercard.svg";
import americanExpress from "../../src/assets/images/american-express.svg";
import venmo from "../../src/assets/images/venmo.svg";
import { CiBank } from "react-icons/ci";

const PAYMENT_METHODS = [
  { id: "card", label: "Credit Card" },
  { id: "paypal", label: "PayPal" },
  { id: "venmo", label: "Venmo Payments" },
  { id: "echeck", label: "eCheck" },
];

// Dummy review data
const ORDER_DATA = [
  {
    vendor: "Nero’s Heroes",
    services: [
      {
        name: "<PERSON> Trooper",
        date: "Jun 22, 2023",
        time: "4:00pm–5:30pm",
        qty: 1,
        unitPrice: "$75/hr",
        duration: "1.5 hrs",
        amount: "$112.50",
      },
      {
        name: "Darth Vader",
        date: "Jun 22, 2023",
        time: "4:00pm–5:30pm",
        qty: 1,
        unitPrice: "$75/hr",
        duration: "1.5 hrs",
        amount: "$112.50",
      },
      {
        name: "Luke Skywalker",
        date: "Jun 22, 2023",
        time: "4:30pm–5:30pm",
        qty: 1,
        unitPrice: "$75/hr",
        duration: "1.5 hrs",
        amount: "$75.00",
      },
    ],
  },
  {
    vendor: "Twisted",
    services: [
      {
        name: "Bumble Photo Kiosk",
        description: "Entertainer Package",
        date: "Jun 22, 2023",
        time: "4:45pm–5:30pm",
        qty: 2,
        unitPrice: "$50/hr",
        duration: ".75 hrs",
        amount: "$75.00",
      },
    ],
  },
];

export default function PaymentForm() {
  const [selectedMethod, setSelectedMethod] = useState("card");
  const [termsChecked, setTermsChecked] = useState(false);

  return (
    <form className="pp-payment-form">
      {/* ------ Order Review ------ */}
      <section className="pp-payment-form__section">
        <h2 className="pp-payment-form__h2">Order Review</h2>
        {ORDER_DATA.map((vendor, vIdx) => (
          <div key={vendor.vendor} className="pp-payment-form__vendor-block">
            <div className="pp-payment-form__vendor-name">{vendor.vendor}</div>
            <div className="pp-payment-form__service-table">
              <div className="pp-payment-form__table-row pp-payment-form__table-header">
                <div>Service</div>
                <div>Date / Time</div>
                <div>Qty</div>
                <div>Unit Price</div>
                <div>Duration</div>
                <div>Amount</div>
              </div>
              {vendor.services.map((service, sIdx) => (
                <div key={sIdx} className="pp-payment-form__table-row">
                  <div>
                    <div className="pp-payment-form__service-name">
                      {service.name}
                    </div>
                    {service.description && (
                      <div className="pp-payment-form__service-description">
                        {service.description}
                      </div>
                    )}
                  </div>
                  <div>
                    {service.date}
                    <br />
                    {service.time}
                  </div>
                  <div>{service.qty}</div>
                  <div>{service.unitPrice}</div>
                  <div>{service.duration}</div>
                  <div>{service.amount}</div>
                </div>
              ))}
            </div>
          </div>
        ))}
        {/* Subtotal/Table summary */}
        <div className="pp-payment-form__summary-totals">
          <div className="pp-payment-form__summary-row">
            <span>Subtotal</span>
            <span>$375.00</span>
          </div>
          <div className="pp-payment-form__summary-row">
            <span>Travel Fees</span>
            <span>$66.00</span>
          </div>
          <div className="pp-payment-form__summary-address">
            <span>
              1720 N 272 E<br />
              Apt 5E
              <br />
              Salt Lake City, UT 85515
            </span>
          </div>
          <div className="pp-payment-form__summary-row pp-payment-form__total-row">
            <span>Total</span>
            <span>$441.00</span>
          </div>
        </div>
      </section>

      {/* ------ Payment ------ */}
      <section className="pp-payment-form__section">
        <h2 className="pp-payment-form__h2">Payment</h2>

        {/* Saved card selector */}
        <div className="pp-payment-form__radio-option">
          <label className="pp-payment-form__input-label !flex !items-center">
            <input type="radio" name="paymentMethod" checked={selectedMethod === "card"} onChange={() => setSelectedMethod("card")} />
           
          </label>
          <div className="flex gap-[var(--pp-sp-8)] items-center payment-methods-heading">
          <span>My Payment Methods</span>
          <div className="pp-payment-form__payment-existing-wrap">
          </div>
          <div className="pp-payment-form__select-wrap">
            <img src={visa} alt="visa" />
            <select 
              className="pp-payment-form__select"
              value={"visa-8719"}
              onChange={() => {}}
            >
              <option value="visa-8719">Credit Card, Visa (••• 8719)</option>
            </select>
          </div>
          </div>
        </div>

        {/* Payment method choices */}
        <div className="pp-payment-form__payment-options">
          {/* -- CREDIT CARD -- */}
          <div className="pp-cradit-card-form-input">
            <label className="pp-payment-form__radio-option">
              <input
                type="radio"
                name="paymentMethod"
                checked={selectedMethod === "card"}
                onChange={() => setSelectedMethod("card")}
              />
              <span>Credit Card</span>
              <span className="pp-payment-form__cc-brands">
                <img src={visa} alt="Visa" loading="lazy" decoding="async" />
                <img
                  src={mastercard}
                  alt="Mastercard"
                  loading="lazy"
                  decoding="async"
                />
                <img
                  src={americanExpress}
                  alt="Amex"
                  loading="lazy"
                  decoding="async"
                />
                <img
                  src={paypal}
                  alt="Discover"
                  loading="lazy"
                  decoding="async"
                />
              </span>
            </label>
            {selectedMethod === "card" && (
              <div className="pp-payment-form__card-wrap">
                <div className="pp-payment-form__2col">
                  <div className="pp-payment-form__field">
                    <label className="pp-payment-form__input-label">
                      Payment Method
                    </label>
                    <input
                      className="pp-form-input"
                      placeholder="Credit card"
                      disabled
                    />
                  </div>
                  <div className="pp-payment-form__field">
                    <label className="pp-payment-form__input-label">
                      Credit Card Number
                    </label>
                    <div className="payment-card-input pp-form-input">
                      <img src={visa} alt="Visa" loading="lazy" decoding="async" className="payment-visa" />
                    <input
                    
                      className=""
                      placeholder="5712-4872-6443-8719"
                    />
                    </div>
                  </div>
                  <div className="flex gap-[var(--pp-sp-16)] payment-card-expiration">
                  <div className="pp-payment-form__field  ">
                    <label className="pp-payment-form__input-label">
                      Expiration Date
                    </label>
                    <div className="flex gap-[var(--pp-sp-8)] items-center">
                    <input
                      className="pp-form-input"
                      placeholder="06"
                    />
                    <span>/</span>
                    <input
                      className="pp-form-input"
                      placeholder="2028"
                    />
                    </div>
                  </div>
                  <div className="pp-payment-form__field ">
                    <label className="pp-payment-form__input-label">CVV</label>
                    <input
                      className="pp-form-input"
                      placeholder="775"
                    />
                  </div>
                  </div>
                </div>
              </div>
            )}
          </div>

         

     

          {/* -- eCheck -- */}
          <label className="pp-payment-form__radio-option">
            <input
              type="radio"
              name="paymentMethod"
              checked={selectedMethod === "echeck"}
              onChange={() => setSelectedMethod("echeck")}
            />
            <span>eCheck</span>
            <span className="pp-payment-form__pay-brand">
              <CiBank style={{ fontSize: "22px" }}/>
            </span>
          </label>
        </div>
      </section>

      {/* ---------- Agreement/Buttons ---------- */}
      <section className="pp-payment-form__section">
        <div className="pp-payment-form__privacy">
          Your personal data will be used to process your order, support your
          experience throughout this website, and for other purposes described
          in our privacy policy.
        </div>
        <div className="pp-payment-form__terms-row">
          <input
            type="checkbox"
            id="terms"
            checked={termsChecked}
            onChange={(e) => setTermsChecked(e.target.checked)}
            className="pp-payment-form__checkbox"
          />
          <label htmlFor="terms" className="pp-payment-form__terms-label">
            I have read and agree to the website{" "}
            <a href="#" tabIndex="-1">
              terms and conditions
            </a>{" "}
            *
          </label>
        </div>

        <div className="pp-payment-form__footer">
          <button className="pp-btn pp-btn-secondary" type="button">
            Previous
          </button>
          <button
            className="pp-btn pp-btn-primary"
            type="submit"
            disabled={!termsChecked}
          >
            Place your order
          </button>
        </div>
      </section>
    </form>
  );
}
