import React from "react";
import { FiDownload } from "react-icons/fi";
import {
  Line<PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";

// Dummy overview stats
const stats = [
  { label: "Sales this period", value: "$4,780.52" },
  { label: "Net sales", value: "$1,872.87" },
  { label: "Average daily sales", value: "$780.0" },
  { label: "Total bookings", value: "24" },
  { label: "Canceled bookings", value: "3" },
  { label: "Travel fees", value: "$237.50" },
];

// Chart data for sales (dates and sales values)
const salesChartData = [
  { date: "Apr 03", sales: 240 },
  { date: "Apr 08", sales: 425.7 },
  { date: "Apr 15", sales: 397.1 + 385.67 },
  { date: "Apr 18", sales: 816 },
  { date: "Apr 24", sales: 122.45 },
  { date: "Apr 28", sales: 416 },
  { date: "May 01", sales: 0 },
  { date: "May 16", sales: 1010 + 980 },
  { date: "May 17", sales: 780 },
  { date: "May 25", sales: 714 },
  { date: "May 28", sales: 274 + 420 },
  { date: "May 31", sales: 215 + 315 },
];

// Dummy bookings for table
const bookings = [
  {
    date: "2023-04-03",
    bookable: "Batman",
    customer: "Rosina Sauer",
    status: "Completed",
    total: "$240.00",
  },
  {
    date: "2023-04-08",
    bookable: "Ironman Thor",
    customer: "Candice Dietrich",
    status: "Completed",
    total: "$425.70",
  },
  {
    date: "2023-04-15",
    bookable: "Luke Skywalker, Storm Trooper",
    customer: "Jeanette Tromp",
    status: "Completed",
    total: "$397.10",
  },
  {
    date: "2023-04-15",
    bookable: "Storm Trooper",
    customer: "Bessie Ziemik",
    status: "Completed",
    total: "$385.67",
  },
  {
    date: "2023-04-18",
    bookable: "Batman",
    customer: "Eddi Champlin",
    status: "Completed",
    total: "$816.00",
  },
  {
    date: "2023-04-28",
    bookable: "Batman",
    customer: "Dajuan Bartz",
    status: "Completed",
    total: "$416.00",
  },
  {
    date: "2023-04-24",
    bookable: "Spider-Man, Batman",
    customer: "Nico Hansen",
    status: "Completed",
    total: "$122.45",
  },
  {
    date: "2023-05-01",
    bookable: "Storm Trooper (2)",
    customer: "Elyse Hane",
    status: "Cancelled",
    total: "$0.00",
  },
  {
    date: "2023-05-16",
    bookable: "Thor",
    customer: "Jacquelyn Wuckert",
    status: "Completed",
    total: "$1,010.00",
  },
  {
    date: "2023-05-16",
    bookable: "Wonder Woman",
    customer: "Merle Heaney",
    status: "Completed",
    total: "$980.00",
  },
  {
    date: "2023-05-17",
    bookable: "Captain America",
    customer: "Dewan Stark",
    status: "Completed",
    total: "$780.00",
  },
  {
    date: "2023-05-25",
    bookable: "Batman",
    customer: "Giovanna Grimes",
    status: "Completed",
    total: "$714.00",
  },
  {
    date: "2023-05-28",
    bookable: "Storm Trooper",
    customer: "Gary Gleichner",
    status: "Completed",
    total: "$274.00",
  },
  {
    date: "2023-05-28",
    bookable: "Captain Marvel",
    customer: "Brody Wunsh",
    status: "Completed",
    total: "$420.00",
  },
  {
    date: "2023-05-31",
    bookable: "Batman, Robin",
    customer: "Esperanza Schaefer",
    status: "Completed",
    total: "$215.00",
  },
  {
    date: "2023-05-31",
    bookable: "Batman",
    customer: "Jettie Waters",
    status: "Completed",
    total: "$315.00",
  },
];

export default function SalesReportComponent({ startDate, endDate }) {
  return (
    <div className="sr-report-content">
      <div className="sr-header-row">
        <div className="sr-title">
          <div className="sr-main-title">Sales Report</div>
          <div className="sr-sub-title">
            {startDate} to {endDate}
          </div>
        </div>
        <button className="pp-btn-secondary flex items-center gap-2">
          <FiDownload className="sr-btn-icon" /> Download Report
        </button>
      </div>

      <div className="sr-grid">
        <div className="sr-overview-card">
          <div className="sr-overview-title">Overview</div>
          <dl className="sr-overview-list">
            {stats.map((stat) => (
              <div key={stat.label} className="sr-overview-item">
                <dt>{stat.label}</dt>
                <dd>{stat.value}</dd>
              </div>
            ))}
          </dl>
        </div>
        <div className="sr-chart-card">
          <div className="sr-overview-title">Sales</div>
          <div className="dashboard-main__chart-placeholder">
            <ResponsiveContainer width="100%" height={200}>
              <LineChart data={salesChartData}>
                <CartesianGrid
                  strokeDasharray="3 3"
                  vertical={false}
                  stroke="#e5e5e5"
                />
                <XAxis
                  dataKey="date"
                  tick={{ fontSize: 12 }}
                  axisLine={false}
                />
                <YAxis tick={{ fontSize: 12 }} axisLine={false} />
                <Tooltip />
                <Line
                  type="stepAfter"
                  dataKey="sales"
                  stroke="#00B75F"
                  strokeWidth={2}
                  dot={{ r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      <div className="sr-section">
        <div className="sr-section-title">Bookings</div>
        <div className="sr-table-wrap">
          <table className="sr-table">
            <thead>
              <tr>
                <th>Date</th>
                <th>Bookable</th>
                <th>Customer</th>
                <th>Status</th>
                <th>Total</th>
              </tr>
            </thead>
            <tbody>
              {bookings.map((b, idx) => (
                <tr key={idx}>
                  <td>{b.date}</td>
                  <td>{b.bookable}</td>
                  <td>{b.customer}</td>
                  <td>{b.status}</td>
                  <td>{b.total}</td>
                </tr>
              ))}
            </tbody>
            <tfoot>
              <tr>
                <td colSpan={4} style={{ textAlign: "right" }}></td>
                <td className="sr-total-value">$4,780.52</td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </div>
  );
}
