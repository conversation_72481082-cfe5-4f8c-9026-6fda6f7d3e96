/* Scope: GuaranteeSection */
.guaranteeSection {
  background-color: var(--pp-clr-guarantee-section-bg);
  padding: var(--pp-sp-60) var(--pp-sp-32);
  border-radius: var(--pp-bor-rad-16);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  box-shadow: var(--pp-shdw-sm);
  width: 100%; /* default: take parent’s width */
}

/* Homepage full-width variation */
.guaranteeSection.fullWidth {
  max-width: 1400px;
  margin: 0 auto;
}

.guaranteeContent {
  display: grid;
  justify-content: space-between;
  gap: 10px;
  justify-items: flex-start;
  max-width: 70%;
}

.guaranteeSection p {
  font-size: var(--pp-font-heading3);
  font-weight: 700;
  font-family: var(--pp-font-Playfair-Display);
  text-align: start;
  margin: 0;
  color: var(--pp-clr-text-main);
  line-height: 1.2;
}

.guaranteeSection img {
  width: 260px;
  aspect-ratio: 1/1;
  position: absolute;
  top: 50%;
  right: var(--pp-sp-10);
  transform: translateY(-70%);
  z-index: var(--pp-z-index-base);
}

.guaranteeLink {
  font-size: var(--pp-font-small-font);
  text-decoration: underline;
  font-weight: 400;
}

/* Responsive */
@media (max-width: 1024px) {
  .guaranteeSection {
    padding: var(--pp-sp-48) var(--pp-sp-24);
  }
  .guaranteeSection img {
    width: 200px;
  }
}

@media (max-width: 767px) {
  .guaranteeSection {
    padding: var(--pp-sp-20) var(--pp-sp-20);
    text-align: center;
    gap: var(--pp-sp-32);
  }
  .guaranteeSection p {
    font-size: var(--pp-font-heading4);
  }
  .guaranteeSection img {
    width: 120px;
  }
}

@media (max-width: 500px) {
  .guaranteeSection p {
    font-size: var(--pp-font-heading6);
  }
  .guaranteeSection img {
    width: 120px;
  }
}

@media (max-width: 414px) {
  .guaranteeSection {
    padding: var(--pp-sp-32) var(--pp-sp-16);
  }
  .guaranteeSection p {
    font-size: var(--pp-font-heading6);
  }
  .guaranteeSection img {
    width: 80px;
    top: 20%;
  }
}
