import React, { useState } from "react";
import "../styles/CustomerMessages.css";
import MessagesUI from "../components/messages/MessagesUI";
import thor from "../src/assets/images/thor.svg";
import vendorprofileicon from "../src/assets/images/vendorprofileicon.svg";
import myprofileicon from "../src/assets/images/myprofileicon.svg";

const CONTACTS = [
  {
    id: 1,
    name: "Twisted",
    avatar: thor,
    lastMessage: "That's correct! <PERSON>, your balloo...",
    lastDate: "May 27",
    unread: 1,
    messages: [
      {
        id: 1,
        sender: "Twisted",
        avatar: thor,
        date: "May 27",
        text: "That's correct! <PERSON>, your balloons will be purple.",
        isUser: false,
      },
    ],
  },
  {
    id: 2,
    name: "<PERSON> Aura",
    avatar: thor,
    lastMessage: "<PERSON> <PERSON>, thanks for reaching out...",
    lastDate: "May 27",
    unread: 3,
    messages: [
      {
        id: 1,
        sender: "Mask Aura",
        avatar: thor,
        date: "May 27",
        text: "<PERSON> <PERSON>, thanks for reaching out. Let us know your requirements.",
        isUser: false,
      },
    ],
  },
  {
    id: 3,
    name: "<PERSON>’s Heroes",
    avatar: vendorprofileicon,
    lastMessage: "You: Thank you for the photos. Th...",
    lastDate: "May 27",
    unread: 0,
    messages: [
      {
        id: 1,
        sender: "Claire Ramson",
        avatar: myprofileicon,
        date: "May 27",
        text: "The party will be at Sunset Park. We'll be at the furthest Northwest pavilion, just south of 1720N. Also do you have additional pictures of the characters?",
        isUser: true,
      },
      {
        id: 2,
        sender: "Nero’s Heroes",
        avatar: vendorprofileicon,
        date: "May 28",
        text: "Hi Claire! Thanks for booking with Party Pipeline! Thanks for the location info. I just emailed you some additional pictures of the actors that will be attending your event. Please let me know if there is anything else I can help with. We look forward to your event!",
        isUser: false,
      },
      {
        id: 3,
        sender: "Claire Ramson",
        avatar: myprofileicon,
        date: "May 27",
        text: "Thanks for the photos. Those are super helpful!",
        isUser: true,
      },
    ],
  },
  {
    id: 4,
    name: "Party Pipeline",
    avatar: thor,
    lastMessage: "Thanks Claire!",
    lastDate: "May 27",
    unread: 0,
    messages: [
      {
        id: 1,
        sender: "Party Pipeline",
        avatar: thor,
        date: "May 27",
        text: "Thanks Claire!",
        isUser: false,
      },
    ],
  },
];

export default function CustomerMessages() {
  const [contacts, setContacts] = useState(CONTACTS);
  const [selectedChatId, setSelectedChatId] = useState(3);
  const [input, setInput] = useState("");
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  const selected = contacts.find((c) => c.id === selectedChatId);
  const messages = selected?.messages ?? [];

  function handleSendMessage(e) {
    e.preventDefault();
    if (!input.trim()) return;
    const newMsg = {
      id: messages.length + 1,
      sender: "Claire Ramson",
      avatar: myprofileicon,
      date: "",
      text: input.trim(),
      isUser: true,
    };
    setContacts((prev) =>
      prev.map((c) =>
        c.id === selectedChatId
          ? {
              ...c,
              messages: [...c.messages, newMsg],
              lastMessage: `You: ${input.trim()}`,
            }
          : c
      )
    );
    setInput("");
  }

  function handleOpenConversation(id) {
    setSelectedChatId(id);
    setIsSidebarOpen(false);
  }

  function handleBackToList() {
    setIsSidebarOpen(true);
  }

  return (
   <div className="CustomerMessages-section">
    <h2 className="CustomerMessages-messagesTitle">Messages</h2>
    <div className="CustomerMessages-listDivider" />
    <MessagesUI
      
      contacts={contacts}
      selectedChatId={selectedChatId}
      isSidebarOpen={isSidebarOpen}
      onOpenConversation={handleOpenConversation}
      onBackToList={handleBackToList}
      messages={messages}
      composeAvatar={myprofileicon}
      inputValue={input}
      onInputChange={(e) => setInput(e.target.value)}
      onSend={handleSendMessage}
    />
   </div>
  );
}
