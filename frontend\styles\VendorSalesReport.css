/* Sales Report Component Specific Styles */

/* ==== Root Report Layout ==== */
.sr-report-content {
  max-width: 1100px;
  margin: var(--pp-sp-24) auto 0 auto;
  background: var(--pp-clr-primary);
  border-radius: var(--pp-bor-rad-16);
  box-shadow: var(--pp-shdw-md);
  padding: var(--pp-sp-32) var(--pp-sp-40);
  font-family: var(--pp-font-Metro-Sans);
}

/* ==== Header Row ==== */
.sr-header-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--pp-sp-32);
}

.sr-title {
  display: flex;
}

.sr-main-title {
  font-size: var(--pp-font-heading6);
  font-weight: 700;
  color: var(--pp-clr-text-main);
  margin-bottom: var(--pp-sp-4);
  letter-spacing: 0.01em;
}

.sr-sub-title {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
}

/* ==== Overview & Chart Grid ==== */
.sr-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--pp-sp-32);
  margin-bottom: var(--pp-sp-40);
  margin-top: 32px;
}

/* ==== Overview Card ==== */
.sr-overview-card {
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-16);
  padding: var(--pp-sp-24) var(--pp-sp-20);
  min-width: 220px;
}

.sr-overview-title {
  font-size: var(--pp-font-heading6);
  font-weight: 600;
  color: var(--pp-clr-secondary);
  margin-bottom: var(--pp-sp-16);
}

.sr-overview-list {
  margin: 0;
  padding: 0;
}

.sr-overview-item {
  display: flex;
  justify-content: space-between;
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-main);
  margin-bottom: var(--pp-sp-10);
  font-weight: 400;
}

.sr-overview-item dt {
  color: var(--pp-clr-text-gray);
}

.sr-overview-item dd {
  font-weight: 500;
  color: var(--pp-clr-text-main);
}

/* ==== Sales Chart Card ==== */
.sr-chart-card {
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-16);
  padding: var(--pp-sp-24);
}

.sr-chart-wrap {
  padding: var(--pp-sp-18);
  background: var(--pp-clr-primary);
  border-radius: var(--pp-bor-rad-10);
  box-shadow: var(--pp-shdw-sm);
}

.sr-chart-svg {
  width: 100%;
  height: 96px;
  display: block;
}

/* ==== Bookings Table Section ==== */
.sr-section {
  margin-top: var(--pp-sp-32);
}

.sr-section-title {
  font-size: var(--pp-font-base2-font);
  font-weight: 600;
  color: var(--pp-clr-secondary);
  margin-bottom: var(--pp-sp-14);
}

.sr-table-wrap {
  overflow-x: auto;
  background: var(--pp-clr-primary);
  border-radius: var(--pp-bor-rad-8);
}

.sr-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--pp-font-extra-small);
  background: var(--pp-clr-primary);
}

.sr-table th {
  font-weight: 600;
  color: var(--pp-clr-secondary);
  padding: var(--pp-sp-8) var(--pp-sp-4);
  border-bottom: 2px solid var(--pp-clr-border-light);
  background: white !important;
  white-space: nowrap;
  text-align: start;
}

.sr-table td {
  padding: var(--pp-sp-8) var(--pp-sp-4);
  border-bottom: 1px solid var(--pp-clr-border-light);
}

.sr-table tfoot td {
  font-weight: 700;
  color: var(--pp-clr-secondary);
  border-bottom: none;
}

.sr-total-value {
  font-size: var(--pp-font-base2-font);
  font-weight: 700;
  color: var(--pp-clr-secondary);
}

/* ==== Responsive ==== */
@media (max-width: 900px) {
  .sr-report-content {
    padding: var(--pp-sp-20) var(--pp-sp-10);
  }
  .sr-grid {
    grid-template-columns: 1fr;
    gap: var(--pp-sp-16);
  }
}

@media (max-width: 600px) {
  .sr-table th,
  .sr-table td {
    font-size: 13px;
    padding: var(--pp-sp-10) var(--pp-sp-2);
  }

  .sr-section-title {
    font-size: 16px;
    margin-bottom: 8px;
  }
  .sr-main-title,
  .sr-overview-title {
    font-size: 18px;
  }
  .sr-header-row{
    flex-direction: column;
    align-items: start;
    gap: var(--pp-sp-8);
  }
}
