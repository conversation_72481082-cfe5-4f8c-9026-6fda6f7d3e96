.msg-root {
  display: grid;
  grid-template-columns: 360px 1fr;
  gap: 24px;

  box-sizing: border-box;
  overflow: hidden;

  background: var(--pp-clr-bg);
  color: var(--pp-clr-text-main);
  font-family: var(--pp-font-Metro-Sans), system-ui, -apple-system, "Segoe UI",
    Roboto, Arial, sans-serif;
  height: calc(100vh - (119px + 80px + 108px));
}

/* Sidebar */
.msg-sidebar {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-16);
  border-right: 1px solid var(--pp-clr-border-light);
  padding-right: var(--pp-sp-16);
  min-width: 0;
  /* allow children to shrink so their own overflow can work inside flex/grid */
  height: 100%;
  overflow-y: auto;
  transition: transform 300ms ease, opacity 300ms ease;
}

.msg-search {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-12);
  padding: var(--pp-sp-12) var(--pp-sp-16);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-24);
  background: var(--pp-clr-primary);
}

.msg-search input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-main);
}

.msg-icon {
  font-size: var(--pp-font-small-font);
  color: var(--pp-clr-text-gray);
}

.msg-conversations {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-8);
  /* allow the list to grow and scroll inside the sidebar */
  flex: 1 1 auto;
  min-height: 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-right: var(--pp-sp-8);
}

.msg-conversation {
  display: grid;
  grid-template-columns: 48px 1fr;
  gap: var(--pp-sp-12);
  align-items: center;
  padding: var(--pp-sp-12);
  border-radius: var(--pp-bor-rad-12);
  background: transparent;
  border: none;
  text-align: left;
  cursor: pointer;
}

.msg-conversation:hover {
  background: var(--pp-clr-primary);
  box-shadow: var(--pp-shdw-sm);
}

.msg-avatar {
  width: 48px;
  height: 48px;
  border-radius: var(--pp-bor-rad-round);
  background: var(--pp-clr-border-light);
}

.msg-avatar-sm {
  width: 32px;
  height: 32px;
}

.msg-meta {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-4);
  min-width: 0;
}

.msg-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--pp-sp-12);
}

.msg-name {
  font-size: var(--pp-font-small-font);
  font-weight: 600;
  color: var(--pp-clr-text-main);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.msg-time {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  flex-shrink: 0;
}

.msg-preview {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Chat panel */
.msg-chat {
  display: grid;
  grid-template-rows: auto 1fr auto;
  gap: var(--pp-sp-16);
  min-width: 0;
  overflow-y: auto;
}
.msg-chat-header,
.msg-composer {
  position: sticky;
  z-index: var(--pp-z-index-sticky);
  background: var(--pp-clr-bg);
}
.msg-chat-header {
  top: 0;
  padding: 10px;
}
.msg-composer {
  bottom: 0;
  padding: 10px;
}
.msg-chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: var(--pp-sp-12);
  border-bottom: 1px solid var(--pp-clr-border-light);
}
.msg-chat-title {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-12);
}
.msg-chat-title h1 {
  margin: 0;
  font-family: var(--pp-font-Playfair-Display);
  font-size: var(--pp-font-heading6);
  font-weight: 600;
  color: var(--pp-clr-text-main);
}
.msg-booked-btn {
  background: transparent;
  color: var(--pp-clr-secondary);
  border: var(--pp-border-width-thin) solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-24);
  padding: var(--pp-sp-12) var(--pp-sp-24);
  cursor: pointer;
}
.msg-booked-btn:hover {
  background: var(--pp-clr-secondary);
  color: var(--pp-clr-primary);
}

/* Thread */
.msg-thread {
  display: flex;
  flex-direction: column;
  gap: var(--pp-sp-12);
  overflow: auto;
  padding-right: var(--pp-sp-10);
  padding-left: var(--pp-sp-10);
  min-height: 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  cursor: pointer;
}
.msg-message {
  display: grid;
  gap: 10px;
}
.msg-me {
  justify-items: end;
}
.msg-bubble {
  max-width: 72%;
  padding: var(--pp-sp-12) var(--pp-sp-16);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-16);
  background: var(--pp-clr-primary);
  box-shadow: var(--pp-shdw-sm);
}
.msg-me .msg-bubble {
  background: var(--pp-clr-blue-bg);
  border-color: var(--pp-clr-blue-bg);
  color: var(--pp-clr-blue-txt);
}
.msg-message-meta {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-8);
  margin-bottom: var(--pp-sp-8);
}
.msg-author {
  font-size: var(--pp-font-extra-small);
  font-weight: 600;
}
.msg-date {
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-gray);
}
.msg-text {
  margin: 0;
  font-size: var(--pp-font-extra-small);
  line-height: 1.5;
}

/* Composer */
.msg-composer {
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  gap: var(--pp-sp-12);
  border-top: 1px solid var(--pp-clr-border-light);
  padding-top: var(--pp-sp-12);
}
.msg-input-wrap {
  display: flex;
  align-items: center;
  gap: var(--pp-sp-12);
  padding: var(--pp-sp-12) var(--pp-sp-16);
  border: 1px solid var(--pp-clr-border);
  border-radius: var(--pp-bor-rad-24);
  background: var(--pp-clr-primary);
}
.msg-emoji {
  color: var(--pp-clr-text-gray);
  font-size: var(--pp-font-small-font);
}

.msg-emoji-img {
  width: 20px;
  height: 20px;
  border-radius: var(--pp-bor-rad-round);
  object-fit: cover;
}
.msg-input-wrap input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: var(--pp-font-extra-small);
  color: var(--pp-clr-text-main);
}
.msg-attach {
  background: transparent;
  border: none;
  color: var(--pp-clr-secondary);
  cursor: pointer;
  font-size: var(--pp-font-small-font);
}
.msg-send-btn {
  background: var(--pp-clr-secondary);
  color: var(--pp-clr-primary);
  border: none;
  border-radius: var(--pp-bor-rad-24);
  padding: var(--pp-sp-12) var(--pp-sp-24);
  cursor: pointer;
}
.msg-send-btn:hover {
  transform: scale(1.02);
}

/* Mobile header buttons */
.msg-mobile-btn {
  display: none;
  background: transparent;
  border: none;
  color: var(--pp-clr-secondary);
  cursor: pointer;
  font-size: var(--pp-font-base-font);
}

/* Responsive */
@media (max-width: 1024px) {
  .msg-root {
    grid-template-columns: 320px 1fr;
  }
}
@media (max-width: 768px) {
  .msg-root {
    grid-template-columns: 1fr;
    height: calc(100vh - 40px);
  }
  .msg-sidebar {
    border-right: none;
    padding-right: 0;
  }
  .msg-chat {
    height: 100%;
    overflow: hidden;
  }
  .msg-thread {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
  .msg-chat-header {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  .msg-chat-header,
  .msg-composer {
    position: sticky;
    background: var(--pp-clr-bg);
  }
  .msg-mobile-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
  /* Show only one panel at a time: when chat is active, hide sidebar;
     otherwise (no active chat), hide chat and show the conversation list */
  .msg-root.msg--chat-active .msg-sidebar {
    display: none;
  }
  .msg-root:not(.msg--chat-active) .msg-chat {
    display: none;
  }
}
